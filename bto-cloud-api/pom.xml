<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<parent>
		<groupId>com.botong</groupId>
		<artifactId>work-order-cloud</artifactId>
		<version>${revision}</version>
	</parent>
	<modelVersion>4.0.0</modelVersion>
	<artifactId>bto-cloud-api</artifactId>
	<packaging>jar</packaging>

	<dependencies>
		<dependency>
			<groupId>com.botong</groupId>
			<artifactId>bto-cloud-common</artifactId>
			<version>${revision}</version>
		</dependency>
		<dependency>
			<groupId>com.botong</groupId>
			<artifactId>bto-cloud-security</artifactId>
			<version>${revision}</version>
		</dependency>
		<dependency>
			<groupId>org.springframework.cloud</groupId>
			<artifactId>spring-cloud-starter-openfeign</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.cloud</groupId>
			<artifactId>spring-cloud-starter-loadbalancer</artifactId>
		</dependency>
		<dependency>
			<groupId>com.github.ben-manes.caffeine</groupId>
			<artifactId>caffeine</artifactId>
		</dependency>
	</dependencies>

</project>