package com.botong.api.module.photovoltaic.vo;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 光伏系统电站信息实体
 * <AUTHOR> by zhb on 2023/9/12.
 */
@Data
@NoArgsConstructor
public class PlantInfoVO implements Serializable {

    private static final long serialVersionUID = -147130069781709128L;

    /**
     * 电站uid
     */
    private String plantUid;

    /**
     * 电站名称
     */
    private String plantName;

    /**
     * 装机容量(以最小单位Wp存储)kWp*1000
     */
    private String plantCapacity;

    /**
     * 电站朝向（0：不一致，1：一致;
     -1 :无朝向）
     */
    private String orientation;

    /**
     * 电站状态（0：离线，1：正常运行，2：告警运行,3:自检提示,5:逆变器夜间离线）
     */
    private String plantStatus;

    /**
     * 0：并网，1：储能，2：混合，3：交流耦合
     */
    private String plantType;

    /**
     * 逆变器数量
     */
    private Integer inverterNum;

    /**
     * 逆变器sn
     */
    private String inverterSn;

    /**
     * 配电箱状态（0：正常 1：  -1:无状态）
     */
    private String powerDistributor;

    /**
     * 国家
     */
    private String country;

    /**
     * 省
     */
    private String province;

    /**
     * 市/州
     */
    private String city;

    /**
     * 县/区
     */
    private String area;

    /**
     * 镇/街道
     */
    private String town;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 经度
     */
    private String longitude;

    /**
     * 纬度
     */
    private String latitude;

    /**
     * 功率(以最小单位W存储)
     */
    private Integer power;

    /**
     * 日发电量,KWh*100(以最小单位存储,最小单位KWh代表一度电，存在2位小数，需要乘100不保留小数)
     */
    private String todayElectricity;

    /**
     * 月发电量，KWh*100(以最小单位存储,最小单位KWh代表一度电，存在2位小数，需要乘100不保留小数)
     */
    private String monthElectricity;

    /**
     * 年发电量,kWh(以最小单位存储,最小单位KWh代表一度电，存在2位小数，需要乘100不保留小数)
     */
    private String yearElectricity;

    /**
     * 累计发电量,kWh(以最小单位存储,最小单位KWh代表一度电，存在2位小数，需要乘100不保留小数)
     */
    private String totalElectricity;

    /**
     * 电站电量数据接收时间
     */
    private String receiveTime;

    /**
     * 出售自发电价（元）
     */
    private String salePrice;

    /**
     * 项目分类id(1:户用，2:整县-河源。。。。)
     */
    private String projectId;

    /**
     * 项目分类名称
     */
    private String projectName;

    /**
     * 电表编号
     */
    private String meterId;

    /**
     * 用户uid
     */
    private String userUid;

    /**
     * 质保时间
     */
    private Date warrantyTime;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 更新时间
     */
    private String updateTime;

    /**
     * 用户名
     */
    private String userName;

    /**
     * 手机号码
     */
    private String userPhone;

}