package com.botong.api.module.workorder;

import com.botong.api.module.constant.ServerNames;
import com.botong.framework.common.utils.Result;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/1/14 11:31
 */
@FeignClient(name = ServerNames.WORK_ORDER_SERVER_NAME,contextId = "workOrder")
public interface FlowApi {
    /**
     * 取消挂起
     *
     * @return {@link Result }<{@link String }>
     * <AUTHOR>
     * @since 2025-01-14 11:33:28
     */
    @GetMapping(value = "api/flow/unsuspend")
    Result<String> unsuspend();
}
