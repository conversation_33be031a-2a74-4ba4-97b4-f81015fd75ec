package com.botong.api.module.system;

import com.botong.api.module.config.FeignTokenConfigure;
import com.botong.api.module.constant.ServerNames;
import com.botong.api.module.system.vo.ProjectConversionVO;
import com.botong.api.module.workorder.entity.OrgBaseEntity;
import com.botong.framework.common.utils.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 系统组织feign请求API
 * <AUTHOR> by zhb on 2023/9/19.
 */
@FeignClient(name = ServerNames.SYSTEM_SERVER_NAME, contextId = "sysOrgApi", configuration = FeignTokenConfigure.class)
public interface SysOrgApi {

    /**
     * 根据组织名称获取组织id
     * @param name 组织名称
     * @return 组织id集合
     */
    @GetMapping(value = "api/org/getOrgIdsByName")
    Long getOrgIdByName(@RequestParam String name);

    /**
     * 根据机构ID获取本机构及子机构ID
     * @param ids 机构ID
     * @return 组织id集合
     */
    @PostMapping(value = "api/workorder/org/getSubOrgIdList")
    List<Long> getSubOrgIdList(@RequestBody List<Long> ids);

    /**
     * 根据项目特殊属性获取本机构及子机构ID
     * @param projectSpecial 项目特殊属性
     * @return 组织id集合
     */
    @GetMapping(value = "api/workorder/org/getOrgIdWithRepairUserByProjectSpecial")
    Result<ProjectConversionVO> getOrgIdWithRepairUserByProjectSpecial(@RequestParam Long projectSpecial);

    /**
     * 获取所有机构
     * @return 机构集合
     */
    @GetMapping(value = "api/workorder/org/getAllOrg")
    List<OrgBaseEntity> getAllOrg();

    /**
     * 新增机构
     * @param name 机构名称
     * @param pid 上级机构ID
     */
    @PostMapping(value = "api/workorder/org/addOrg")
    String addOrg(@RequestParam String name, @RequestParam String pid);

    /**
     * 修改机构名称
     * @param orgId 机构ID
     * @param name 机构名称
     */
    @PostMapping(value = "api/workorder/org/updateNameById")
    void updateNameById(@RequestParam String orgId, @RequestParam String name);
}