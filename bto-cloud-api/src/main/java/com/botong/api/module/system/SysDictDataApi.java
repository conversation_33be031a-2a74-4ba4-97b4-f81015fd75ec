package com.botong.api.module.system;

import com.botong.api.module.constant.ServerNames;
import com.botong.api.module.system.vo.DictDataVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 字典数据feign请求API
 * <AUTHOR> by zhb on 2023/10/18.
 */
@FeignClient(name = ServerNames.SYSTEM_SERVER_NAME, contextId = "sysDictDataApi")
public interface SysDictDataApi {

    @GetMapping(value = "api/dict/dictData")
    List<DictDataVO> getDictDataByTypeId(@RequestParam String dictType);

    @GetMapping(value = "api/dict/dictSql")
    List<DictDataVO> getDictSqlByType(@RequestParam String dictType);

}
