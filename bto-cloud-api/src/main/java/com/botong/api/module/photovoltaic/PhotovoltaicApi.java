package com.botong.api.module.photovoltaic;

import com.botong.api.module.config.FeignTokenConfigure;
import com.botong.api.module.photovoltaic.dto.InverterAlarmDTO;
import com.botong.api.module.photovoltaic.dto.PlantQueryDTO;
import com.botong.framework.common.constant.InterfaceUrl;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 光伏系统feign请求API
 * <AUTHOR> by zhb on 2023/9/11.
 */
@FeignClient(name = "photovoltaic", url = InterfaceUrl.PHOTOVOLTAIC_PREFIX_URL, configuration = FeignTokenConfigure.class)
public interface PhotovoltaicApi {

    /**
     * 获取光伏电站告警数据（未处理）
     */
    @PostMapping(value = InterfaceUrl.INVERTER_ALARM_URL)
    Object getInverterAlarm(@RequestBody InverterAlarmDTO inverterAlarmDTO);

    /**
     * 获取光伏电站列表数据
     */
    @PostMapping(value = InterfaceUrl.PLANT_LIST_URL)
    Object getPlantList(@RequestBody PlantQueryDTO query);

    /**
     * 获取光伏项目类型数据
     */
    @GetMapping(value = InterfaceUrl.PROJECT_TYPE_URL)
    Object getProjectTypeTree();
}