package com.botong.api.module.system;

import com.botong.api.module.constant.ServerNames;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

/**
 * 系统参数feign请求API
 * <AUTHOR> by zhb on 2023/10/8.
 */
@FeignClient(name = ServerNames.SYSTEM_SERVER_NAME, contextId = "sysParamsApi")
public interface SysParamsApi {

    /**
     * 是否开启短信通知
     *
     * @return true：开启  false：关闭
     */
    @PostMapping(value = "api/params/smsNotify")
    Boolean smsNotifyEnabled();

}