package com.botong.api.module.system.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 维修人员响应实体
 * <AUTHOR>
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class RepairUserVO {
    @Schema(description = "id")
    private Long id;

    @Schema(description = "姓名", required = true)
    @NotBlank(message = "姓名不能为空")
    private String name;

    @Schema(description = "性别 0：男   1：女   2：未知", required = true)
    @Range(min = 0, max = 2, message = "性别不正确")
    private Integer gender;

    @Schema(description = "手机号", required = true)
    @NotBlank(message = "手机号不能为空")
    private String mobile;
    @Schema(description = "机构ID", required = true)
    @NotNull(message = "机构ID不能为空")
    private List<Long> orgIds;
    @Schema(description = "负责人标识 0：否   1：是 ")
    @Range(min = 0, max = 1, message = "负责人标识不正确")
    private Integer responsibleFlag;

    @Schema(description = "市")
    private String city;
}
