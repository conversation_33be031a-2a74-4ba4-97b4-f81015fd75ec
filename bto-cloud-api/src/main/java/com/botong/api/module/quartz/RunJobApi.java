package com.botong.api.module.quartz;

import com.botong.api.module.constant.ServerNames;
import com.botong.api.module.quartz.dto.RunSmsJobDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 定时任务feign请求API
 * <AUTHOR>
 */
@FeignClient(name = ServerNames.QUARTZ_SERVER_NAME)
public interface RunJobApi {

    /**
     * 超时提醒短信定时任务
     * @param runSmsJobDTO 短信任务参数
     * @throws Exception 异常
     */
    @PostMapping(value = "api/quartz/run")
    void runSmsJob(@RequestBody RunSmsJobDTO runSmsJobDTO) throws Exception;

    /**
     * 根据工单ID删除作业
     * @param workId 工单ID
     * @throws Exception 异常
     */
    @PostMapping(value = "api/quartz/deleteJob")
    void deleteSmsJobByWorkId(String workId) throws Exception;
}