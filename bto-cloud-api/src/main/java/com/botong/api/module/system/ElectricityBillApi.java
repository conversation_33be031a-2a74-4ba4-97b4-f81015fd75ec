package com.botong.api.module.system;

import com.botong.api.module.config.FeignTokenConfigure;
import com.botong.api.module.constant.ServerNames;
import com.botong.framework.common.utils.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

/**
 * 电费账单feign请求API
 * 
 * <AUTHOR>
 * @since 2025-01-17
 */
@FeignClient(name = ServerNames.SYSTEM_SERVER_NAME, contextId = "electricityBillApi", configuration = FeignTokenConfigure.class)
public interface ElectricityBillApi {

    /**
     * 更新越秀电费账单数据
     *
     * @return 更新结果
     */
    @PostMapping(value = "api/system/electricity_bill/updateYxBill")
    Result<String> updateYxBill();
}
