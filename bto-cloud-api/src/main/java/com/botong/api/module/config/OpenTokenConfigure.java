package com.botong.api.module.config;

import cn.hutool.core.convert.Convert;
import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSONObject;
import com.botong.framework.common.constant.InterfaceUrl;
import feign.Logger;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;

import java.util.HashMap;
import java.util.Map;

/**
 * 请求开放平台携带token
 * <AUTHOR> by zhb on 2023/12/6.
 */
@Slf4j
public class OpenTokenConfigure implements RequestInterceptor {

    /**
     * oauth2 token请求头名称
     */
    public static final String AUTHORIZATION = "Authorization";

    /**
     * feign日志级别
     */
    @Bean
    Logger.Level feignLoggerLevel() {
        return Logger.Level.FULL;
    }

    /**
     * 添加token请求头
     */
    @Override
    public void apply(RequestTemplate requestTemplate) {
        requestTemplate.header(AUTHORIZATION, "bearer " + getToken());
    }

    /**
     * 获取开放平台token
     */
    public String getToken() {
        try {
            HashMap<String, Object> hashMap = new HashMap<>();
            hashMap.put("client_id", "yuexiu");
            hashMap.put("client_secret", "yuexiu..");
            hashMap.put("grant_type", "client_credentials");
            hashMap.put("scope", "all");
            String result = HttpRequest.post(InterfaceUrl.OPEN_PREFIX_URL + InterfaceUrl.OPEN_ACCESS_TOKEN_URL)
                    .form(hashMap)
                    .header("Authorization", "Basic eXVleGl1Onl1ZXhpdS4u")
                    .execute().body();
            JSONObject jsonObject = JSONObject.parseObject(result);
            Object o = jsonObject.get("code");
            if (o != null && "2000".equals(o.toString())) {
                Object body = jsonObject.get("data");
                Map<String, Object> map = Convert.toMap(String.class, Object.class, body);
                log.info("Successfully obtain token: " + map.get("access_token"));
                return map.get("access_token").toString();
            }
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return null;
    }

}