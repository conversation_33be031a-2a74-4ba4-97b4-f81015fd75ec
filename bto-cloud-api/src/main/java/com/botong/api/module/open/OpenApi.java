package com.botong.api.module.open;

import com.botong.api.module.config.OpenTokenConfigure;
import com.botong.framework.common.constant.InterfaceUrl;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

/**
 * 开放平台feign请求API
 * <AUTHOR> by zhb on 2023/12/6.
 */
@FeignClient(name = "open", url = InterfaceUrl.OPEN_PREFIX_URL, configuration = OpenTokenConfigure.class)
public interface OpenApi {

    /**
     * 获取越秀电站数据
     */
    @PostMapping(value = InterfaceUrl.YUE_XIU_PLANT_LIST_URL)
    Object getPlantList();

}