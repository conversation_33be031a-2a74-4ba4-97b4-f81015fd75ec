package com.botong.api.module.system.vo;

import com.botong.framework.common.utils.DateUtils;
import com.botong.framework.common.utils.TreeNode;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import java.util.Date;
import java.util.List;

/**
 * 机构用户响应实体
 */
@Data
@Schema(description = "机构用户")
public class OrgWithUserVO extends TreeNode<OrgWithUserVO> {
    @Schema(description = "机构名称", required = true)
    @NotBlank(message = "机构名称不能为空")
    private String name;

    @Schema(description = "排序", required = true)
    @Min(value = 0, message = "排序值不能小于0")
    private Integer sort;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = DateUtils.DATE_TIME_PATTERN)
    private Date createTime;

    @Schema(description = "上级名称")
    private String parentName;

    @Schema(description = "维修人员集合")
    private List<RepairUserVO> repairUserList;
}
