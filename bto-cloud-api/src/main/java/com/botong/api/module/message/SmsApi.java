package com.botong.api.module.message;

import com.botong.api.module.constant.ServerNames;
import com.botong.framework.common.utils.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Map;

/**
 * 短信服务API
 */
@FeignClient(name = ServerNames.MESSAGE_SERVER_NAME)
public interface SmsApi {

    /**
     * 发送短信
     *
     * @param mobile 手机号
     * @param params 参数
     * @return 是否发送成功
     */
    @PostMapping(value = "api/message/sms/send", consumes = "application/json")
    Result<Boolean> send(@RequestParam("mobile") String mobile, @RequestParam Map<String, String> params);

    /**
     * 根据短信模板码发送短信
     * @param templateCode 短信模板CODE
     * @param mobile 手机号
     * @param params 参数
     * @return 是否发送成功
     */
    @PostMapping(value = "api/message/sms/sendByTemplateCode", consumes = "application/json")
    Result<Boolean> sendByTemplateCode(@RequestParam("templateCode") String templateCode, @RequestParam("mobile") String mobile, @RequestParam Map<String, String> params);

    /**
     * 发送短信
     *
     * @param mobile 手机号
     * @param key    参数KEY
     * @param value  参数Value
     * @return 是否发送成功
     */
    @PostMapping(value = "api/message/sms/sendCode")
    Result<Boolean> sendCode(@RequestParam("mobile") String mobile, @RequestParam("key") String key, @RequestParam("value") String value);

    /**
     * 效验短信验证码
     *
     * @param mobile 手机号
     * @param code   验证码
     * @return 是否效验成功
     */
    @PostMapping(value = "api/message/sms/verifyCode")
    Result<Boolean> verifyCode(@RequestParam("mobile") String mobile, @RequestParam("code") String code);

}