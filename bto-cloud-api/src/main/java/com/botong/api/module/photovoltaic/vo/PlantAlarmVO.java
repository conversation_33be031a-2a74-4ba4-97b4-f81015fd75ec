package com.botong.api.module.photovoltaic.vo;

import com.botong.framework.common.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 电站告警数据响应实体
 * <AUTHOR> by zhb on 2023/9/12.
 */
@Data
public class PlantAlarmVO {

    @Schema(description = "告警ID")
    private String alarmId;

    @Schema(description = "电站id")
    private String plantUid;

    @Schema(description = "电站名称")
    private String plantName;

    @Schema(description = "设备编号")
    private String deviceId;

    @Schema(description = "设备类型: 1:逆变器 2:运维器")
    private String deviceType;

    @Schema(description = "数据来源：0:三晶 1:博通")
    private String source;

    @Schema(description = "告警开始时间")
    @JsonFormat(pattern = DateUtils.DATE_TIME_PATTERN)
    private Date startTime;

    @Schema(description = "告警持续时间")
    private String alarmDuration;

    @Schema(description = "告警内容")
    private String alarmMean;

    @Schema(description = "告警状态: 0:未处理 1:已处理 2:状态失效 -1:状态异常")
    private Integer status;

    @Schema(description = "项目ID")
    private Integer projectSpecial;

    @Schema(description = "项目名称")
    private String projectName;

    @Schema(description = "是否生成工单: 0:未生成工单 1:已生成工单")
    private Integer dispatchOrNot = 0;

    @Schema(description = "工单ID")
    private Long workId;

    @Schema(description = "电站类型")
    private Long plantType;

    @Schema(description = "并网时间")
    @JsonFormat(pattern = DateUtils.DATE_PATTERN)
    private Date createTime;
}