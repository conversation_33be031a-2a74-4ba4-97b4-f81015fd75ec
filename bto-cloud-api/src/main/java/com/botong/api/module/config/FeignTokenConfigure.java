package com.botong.api.module.config;

import cn.hutool.core.convert.Convert;
import cn.hutool.http.ContentType;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.botong.api.module.photovoltaic.dto.AccessTokenDTO;
import com.botong.framework.common.constant.BtoConstant;
import com.botong.framework.common.constant.InterfaceUrl;
import feign.Logger;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;

import java.util.Map;

/**
 * 请求光伏系统携带token
 * <AUTHOR> by zhb on 2023/9/11.
 */
@Slf4j
public class FeignTokenConfigure implements RequestInterceptor {
    /**
     * oauth2 token请求头名称
     */
    public static final String AUTHORIZATION = "Authorization";

    @Bean
    Logger.Level feignLoggerLevel() {
        return Logger.Level.FULL;
    }

    @Override
    public void apply(RequestTemplate requestTemplate) {
        String url = requestTemplate.request().url();
        // 给feign请求添加请求头信息
        boolean contains = url.contains(BtoConstant.FEIGN_INTERFACE_PREFIX);
        if (Boolean.TRUE.equals(contains)) {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (authentication != null) {
                requestTemplate.header(AUTHORIZATION, authentication.getCredentials().toString());
            }
        } else {
            requestTemplate.header(AUTHORIZATION, "bearer " + getToken());
        }
    }

    /**
     * 获取光伏系统token
     */
    public String getToken() {
        try {
            String parameter = JSONUtil.parse(new AccessTokenDTO()).toString();
            HttpRequest post = HttpUtil.createPost(InterfaceUrl.PHOTOVOLTAIC_PREFIX_URL + InterfaceUrl.ACCESS_TOKEN_URL);
            HttpRequest response = post.body(parameter, ContentType.JSON.getValue());
            String result = response.execute().body();
            JSONObject jsonObject = JSONObject.parseObject(result);
            Object o = jsonObject.get("status");
            if (o != null && "00000".equals(o.toString())) {
                Object body = jsonObject.get("data");
                Map<String, Object> map = Convert.toMap(String.class, Object.class, body);
                log.info("Successfully obtain token: " + map.get("accessToken"));
                return map.get("accessToken").toString();
            }
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return null;
    }

}