package com.botong.api.module.workorder;

import com.botong.api.module.constant.ServerNames;
import com.botong.api.module.photovoltaic.vo.PlantInfoVO;
import com.botong.api.module.workorder.entity.OrgBaseEntity;
import com.botong.framework.common.utils.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 电站feign请求API
 * <AUTHOR> by zhb on 2023/9/12.
 */
@FeignClient(name = ServerNames.WORK_ORDER_SERVER_NAME)
public interface PlantApi {

    /**
     * 批量插入光伏系统电站信息
     *
     * @param plantVOList 光伏系统电站数据
     * @return 是否插入成功
     */
    @PostMapping(value = "api/workorder/plant/saveBatch")
    Result<Boolean> saveBatch(@RequestBody List<PlantInfoVO> plantVOList);

    /**
     * 获取所有项目
     *
     * @return 项目列表
     */
    @GetMapping(value = "api/workorder/plant/projectList")
    List<OrgBaseEntity> getAllProject();

    /**
     * 根据项目名称更新项目信息
     *
     * @param redisName 项目名称
     * @param name 新项目名称
     */
    @PostMapping(value = "api/workorder/plant/updateProjectByName")
    void updateProjectByName(@RequestParam String redisName, @RequestParam String name);

}