package com.botong.api.module.photovoltaic.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 光伏系统告警数据请求参数
 * <AUTHOR> by zhb on 2023/9/11.
 */

@Data
public class InverterAlarmDTO {

    @Schema(description = "告警信息")
    private String alarmInfo;

    @Schema(description = "设备类型: 1:逆变器 2:运维器")
    private String deviceType;

    @Schema(description = "是否分页")
    private Boolean isPage;

    @Schema(description = "电站名称")
    private String plantName;

    @Schema(description = "数据来源：0:三晶 1:博通")
    private String source;

    @Schema(description = "是否去重")
    private Boolean isDistinct;

    @Schema(description = "报警启动时间的开始区间")
    private String startAlarmBeginTime;

    @Schema(description = "报警启动时间的结束区间")
    private String startAlarmFinishTime;

    @Schema(description = "当前页数")
    private Integer currentPage;

    @Schema(description = "页数大小")
    private Integer pageSize;

}