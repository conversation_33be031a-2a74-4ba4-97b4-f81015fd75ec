package com.botong.api.module.quartz.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 执行短信发送任务参数
 * <AUTHOR> by zhb on 2023/9/15.
 */
@Data
public class RunSmsJobDTO {

    @Schema(description = "执行参数", required = true)
    private Map<String, String> params;

    @Schema(description = "短信发送时间")
    private LocalDateTime scheduledTime;

}