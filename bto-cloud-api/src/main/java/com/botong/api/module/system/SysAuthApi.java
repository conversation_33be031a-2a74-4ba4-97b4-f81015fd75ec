package com.botong.api.module.system;

import com.botong.api.module.config.FeignTokenConfigure;
import com.botong.api.module.constant.ServerNames;
import com.botong.api.module.system.vo.LoginVO;
import com.botong.api.module.system.vo.SysUserTokenVO;
import com.botong.framework.common.utils.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;


@FeignClient(name = ServerNames.SYSTEM_SERVER_NAME, contextId = "sysAuthApi", configuration = FeignTokenConfigure.class)
public interface SysAuthApi {
    @PostMapping(value = "api/auth/getAccessToken")
    Result<SysUserTokenVO> getAccessToken(@RequestBody LoginVO login);
}
