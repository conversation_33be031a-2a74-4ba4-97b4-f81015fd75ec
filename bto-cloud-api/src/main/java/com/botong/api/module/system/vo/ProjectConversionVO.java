package com.botong.api.module.system.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/11/14 10:38
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ProjectConversionVO {
    @Schema(description = "电站归属机构ID")
    private Integer plantType;

    @Schema(description = "电站归属机构名称")
    private String plantTypeName;
    @Schema(description = "维修人列表")
    List<RepairUserVO> repairUserVO;
}
