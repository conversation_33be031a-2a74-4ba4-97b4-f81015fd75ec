package com.botong.api.module.photovoltaic.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 光伏系统电站数据请求参数
 * <AUTHOR> by zhb on 2023/9/11.
 */
@Data
public class PlantQueryDTO {

    @Schema(description = "创建时间开始区间")
    private String startCreateTime;
    @Schema(description = "创建时间结束区间")
    private String endCreateTime;
    @Schema(description = "当前页数")
    private Integer currentPage = 1;
    @Schema(description = "页数大小")
    private Integer pageSize = 100;

}