package com.botong.api.module.utlis;

import com.alibaba.fastjson.JSONObject;
import com.botong.framework.common.exception.ServerException;
import com.botong.framework.common.utils.JsonUtils;

import java.util.HashMap;

/**
 * 请求光伏系统数据结果处理工具
 * <AUTHOR> by zhb on 2023/9/12.
 */
public class RequestPhotovoltaicUtil {

    private static final String SUCCESS_STATUS = "00000";

    /**
     * 获取光伏系统的分页列表集合数据
     */
    public static HashMap<String, Object> getPageData(Object object) {
        HashMap<String, Object> map = new HashMap<>();
        JSONObject jsonObject = objectToJsonObject(object);
        if (!SUCCESS_STATUS.equals(jsonObject.get("status"))) {
            throw new ServerException(jsonObject.get("message").toString());
        }
        JSONObject data = objectToJsonObject(jsonObject.get("data"));
        map.put("list", data.get("records"));
        map.put("total", data.get("total"));
        map.put("pages", data.get("pages"));
        return map;
    }

    /**
     * 获取光伏系统的data数据
     */
    public static Object getRequestData(Object object) {
        JSONObject jsonObject = objectToJsonObject(object);
        if (!SUCCESS_STATUS.equals(jsonObject.get("status"))) {
            throw new ServerException(jsonObject.get("message").toString());
        }
        return jsonObject.get("data");
    }

    /**
     * Java对象转alibaba fastjson对象
     */
    public static JSONObject objectToJsonObject(Object object) {
        return JSONObject.parseObject(JsonUtils.toJsonString(object));
    }
}