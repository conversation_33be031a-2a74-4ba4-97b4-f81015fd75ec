package com.botong.api.module.photovoltaic.dto;

import lombok.Data;

/**
 * 光伏系统获取token请求参数
 * <AUTHOR> by zhb on 2023/9/11.
 */
@Data
public class AccessTokenDTO {

    /**
     * 客户端ID
     */
    private final String clientId = "bto";

    /**
     * 客户端密钥
     */
    private final String clientSecret = "botong2023";

    /**
     * 授权类型
     */
    private final String grantType = "client_credentials";

    /**
     * 授权范围
     */
    private final String scope = "all";
}