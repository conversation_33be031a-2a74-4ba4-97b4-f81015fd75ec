# 越秀电费账单定时更新任务使用说明

## 概述

本文档介绍了新创建的越秀电费账单定时更新任务 `ElectricityBillUpdateTask`，该任务用于定时调用越秀电费账单更新接口，实现自动化的数据更新。

## 文件结构

### 1. 定时任务类
- **文件路径**: `bto-cloud-module/bto-module-quartz/src/main/java/com/botong/quartz/task/ElectricityBillUpdateTask.java`
- **功能**: 定时执行越秀电费账单数据更新
- **执行时间**: 每月6号、16号、26号的凌晨2点执行

### 2. API接口类
- **文件路径**: `bto-cloud-api/src/main/java/com/botong/api/module/system/ElectricityBillApi.java`
- **功能**: 提供Feign客户端接口，用于调用系统服务的电费账单更新方法

## 定时任务配置

### Cron表达式
```
0 0 2 6,16,26 * ?
```

**表达式说明**:
- `0`: 秒 (0秒)
- `0`: 分 (0分)
- `2`: 时 (凌晨2点)
- `6,16,26`: 日 (每月的6号、16号、26号)
- `*`: 月 (每月)
- `?`: 周 (不指定)

### 执行时间安排
- 每月6号 凌晨2:00
- 每月16号 凌晨2:00  
- 每月26号 凌晨2:00

## 使用方法

### 1. 在定时任务管理界面添加任务

在系统的定时任务管理界面中添加新的定时任务，配置如下参数：

- **任务名称**: 越秀电费账单更新任务
- **任务组名**: DEFAULT
- **Bean名称**: electricityBillUpdateTask
- **执行方法**: run
- **参数**: (可选，传入任务参数)
- **Cron表达式**: `0 0 2 6,16,26 * ?`
- **状态**: 启用
- **是否并发**: 否
- **备注**: 定时更新越秀电费账单数据，每月6号、16号、26号执行

### 2. 手动执行测试

可以在定时任务管理界面中手动执行该任务进行测试，确保任务能够正常运行。

## 日志记录

任务执行过程中会记录以下日志：

- **开始执行**: 记录任务开始执行的时间和参数
- **执行成功**: 记录任务执行成功的结果信息
- **执行失败**: 记录任务执行失败的错误信息和异常堆栈
- **执行完成**: 记录任务执行完成的时间

## 异常处理

- 当API调用失败时，任务会抛出RuntimeException，触发定时任务框架的异常处理机制
- 所有异常信息都会被记录到日志中，便于问题排查
- 任务失败不会影响其他定时任务的执行

## 依赖关系

该定时任务依赖以下组件：

1. **ElectricityBillApi**: Feign客户端接口
2. **ElectricityBillSummaryController**: 系统服务中的控制器
3. **BillConsolidatedStatementService**: 账单合并服务
4. **ElectricChargeUtil**: 电费工具类
5. **RedisCache**: Redis缓存服务

## 注意事项

1. **执行频率限制**: 原updateYxBill方法中有月度执行限制，一个月只允许更新一次
2. **执行时间**: 建议在业务低峰期执行，避免影响系统性能
3. **监控告警**: 建议配置任务执行失败的告警机制
4. **数据备份**: 执行前建议做好数据备份，防止数据异常

## 维护说明

- 如需修改执行时间，请更新Cron表达式
- 如需修改任务逻辑，请修改ElectricityBillUpdateTask类中的run方法
- 如需添加新的业务逻辑，请在现有方法基础上扩展，保持代码结构清晰

## 版本信息

- **创建时间**: 2025-01-17
- **创建人**: system
- **版本**: 1.0.0
