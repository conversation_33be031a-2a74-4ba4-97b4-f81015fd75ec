# 博通工单管理系统 (Work Order Cloud)

<div align="center">

![Version](https://img.shields.io/badge/version-1.0.0-blue.svg)
![Spring Boot](https://img.shields.io/badge/Spring%20Boot-2.6.14-brightgreen.svg)
![Spring Cloud](https://img.shields.io/badge/Spring%20Cloud-2021.0.1-brightgreen.svg)
![Java](https://img.shields.io/badge/Java-1.8+-orange.svg)
![License](https://img.shields.io/badge/license-MIT-blue.svg)
![Build Status](https://img.shields.io/badge/build-passing-green.svg)

</div>

## 📋 目录导航

<details>
<summary>点击展开完整目录</summary>

- [🎯 项目概览](#-项目概览)
  - [✨ 核心功能亮点](#-核心功能亮点)
  - [📈 项目统计](#-项目统计)
  - [🛠️ 技术栈概览](#️-技术栈概览)
- [🏗️ 系统架构](#️-系统架构)
  - [架构概览](#架构概览)
  - [典型业务流程](#典型业务流程)
  - [模块职责说明](#模块职责说明)
  - [项目结构](#项目结构)
- [技术栈](#技术栈)
- [核心功能](#核心功能)
- [环境要求](#环境要求)
- [🚀 快速开始](#-快速开始)
  - [📋 环境检查清单](#-环境检查清单)
  - [⚡ 一键启动](#-一键启动-推荐)
  - [🔧 详细配置步骤](#-详细配置步骤)
  - [🔍 服务健康检查](#-服务健康检查)
  - [🚨 故障排除指南](#-故障排除指南)
- [💻 开发指南](#-开发指南)
  - [📁 项目结构详解](#-项目结构详解)
  - [🎯 开发规范](#-开发规范)
  - [🏗️ API开发示例](#️-api开发示例)
  - [🧪 测试指南](#-测试指南)
  - [🐛 调试技巧](#-调试技巧)
  - [📋 数据库设计规范](#-数据库设计规范)
- [部署说明](#部署说明)
- [API文档](#api文档)
- [监控运维](#监控运维)
- [常见问题](#常见问题)
- [📚 学习资源](#-学习资源)
  - [🎓 技术栈学习路径](#-技术栈学习路径)
  - [📖 业务知识库](#-业务知识库)
  - [🆘 获得帮助](#-获得帮助)
  - [🤝 贡献指南](#-贡献指南)
  - [📞 联系方式](#-联系方式)
- [📄 许可证](#-许可证)

</details>

---

## 🚀 快速链接

| 🎯 新人必读 | 🛠️ 开发者 | 🔧 运维人员 | 📚 学习资源 |
|------------|-----------|------------|------------|
| [项目概览](#-项目概览) | [开发指南](#-开发指南) | [部署说明](#部署说明) | [学习资源](#-学习资源) |
| [快速开始](#-快速开始) | [API文档](#api文档) | [监控运维](#监控运维) | [技术栈学习](#-技术栈学习路径) |
| [系统架构](#️-系统架构) | [测试指南](#-测试指南) | [故障排除](#-故障排除指南) | [获得帮助](#-获得帮助) |

---

## 🎯 项目概览

> **专为光伏电站运维打造的智能工单管理平台，提升运维效率50%+**

博通工单管理系统是一个基于Spring Cloud微服务架构的企业级工单管理平台，专门用于光伏电站的运维管理。系统通过智能化的工单流程管理，实现了从故障发现到维修完成的全生命周期自动化，大幅提升了运维团队的工作效率和服务质量。

### ✨ 核心功能亮点

<table>
<tr>
<td align="center" width="25%">
<img src="https://img.icons8.com/fluency/48/000000/ticket.png" alt="智能工单"/>
<br><strong>🎫 智能工单</strong>
<br>自动派单 • 状态跟踪
<br>多级审核 • 超时提醒
</td>
<td align="center" width="25%">
<img src="https://img.icons8.com/fluency/48/000000/factory.png" alt="电站管理"/>
<br><strong>🏭 电站管理</strong>
<br>设备监控 • 故障分类
<br>保修管理 • 告警处理
</td>
<td align="center" width="25%">
<img src="https://img.icons8.com/fluency/48/000000/bar-chart.png" alt="数据分析"/>
<br><strong>📊 数据分析</strong>
<br>运维报表 • 费用统计
<br>效率分析 • 趋势预测
</td>
<td align="center" width="25%">
<img src="https://img.icons8.com/fluency/48/000000/bell.png" alt="消息通知"/>
<br><strong>🔔 消息通知</strong>
<br>短信提醒 • 状态通知
<br>超时告警 • 多渠道推送
</td>
</tr>
</table>

### 📈 项目统计

- **🏗️ 微服务模块**: 6个核心服务
- **💻 代码规模**: ~50K+ 行代码  
- **🔧 技术栈**: 15+ 主流技术
- **⚡ 性能提升**: 运维效率提升50%+
- **🏢 适用场景**: 光伏电站运维管理

### 🛠️ 技术栈概览

<div align="center">

| 分类 | 技术栈 |
|------|--------|
| **后端框架** | ![Spring Boot](https://img.shields.io/badge/Spring%20Boot-2.6.14-6DB33F?logo=springboot&logoColor=white) ![Spring Cloud](https://img.shields.io/badge/Spring%20Cloud-2021.0.1-6DB33F?logo=spring&logoColor=white) ![Nacos](https://img.shields.io/badge/Nacos-2.1.1-blue?logo=alibaba-cloud&logoColor=white) |
| **数据存储** | ![MySQL](https://img.shields.io/badge/MySQL-5.7+-4479A1?logo=mysql&logoColor=white) ![Redis](https://img.shields.io/badge/Redis-5.0+-DC382D?logo=redis&logoColor=white) |
| **开发工具** | ![MyBatis Plus](https://img.shields.io/badge/MyBatis%20Plus-3.5.2-red) ![Knife4j](https://img.shields.io/badge/Knife4j-4.3.0-orange) ![Hutool](https://img.shields.io/badge/Hutool-5.7.22-blue) |
| **云服务** | ![阿里云OSS](https://img.shields.io/badge/阿里云OSS-3.8.0-FF6A00) ![七牛云](https://img.shields.io/badge/七牛云-7.11.0-00D4AA) ![腾讯云COS](https://img.shields.io/badge/腾讯云COS-5.6.89-0066CC) |

</div>

## 🏗️ 系统架构

### 架构概览

本系统采用微服务架构设计，通过Spring Cloud Gateway统一入口，各服务独立部署，通过Nacos进行服务注册与发现。

```mermaid
graph TB
    subgraph "外部系统"
        Client[客户端/前端]
        LightCloud[光云平台]
        SMS[短信服务商]
    end
    
    subgraph "微服务集群"
        Gateway[API网关<br/>:58080]
        
        subgraph "核心服务"
            System[系统管理服务<br/>:58081]
            WorkOrder[工单管理服务<br/>:58085]
        end
        
        subgraph "功能模块"
            Quartz[定时任务模块<br/>:58082]
            Message[消息服务模块<br/>:58083]
            Monitor[监控模块<br/>:58084]
        end
        
        subgraph "基础设施"
            Nacos[服务注册中心<br/>Nacos]
            MySQL[(MySQL数据库)]
            Redis[(Redis缓存)]
        end
        
        subgraph "云存储"
            OSS[阿里云OSS]
            QiNiu[七牛云]
            TencentCOS[腾讯云COS]
        end
    end
    
    Client --> Gateway
    Gateway --> System
    Gateway --> WorkOrder
    Gateway --> Quartz
    Gateway --> Message
    Gateway --> Monitor
    
    System --> MySQL
    WorkOrder --> MySQL
    System --> Redis
    WorkOrder --> Redis
    
    Quartz --> LightCloud
    Message --> SMS
    
    WorkOrder --> OSS
    WorkOrder --> QiNiu
    WorkOrder --> TencentCOS
    
    Nacos -.-> Gateway
    Nacos -.-> System
    Nacos -.-> WorkOrder
    Nacos -.-> Quartz
    Nacos -.-> Message
    Nacos -.-> Monitor
```

### 典型业务流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant G as API网关
    participant W as 工单服务
    participant S as 系统服务
    participant M as 消息服务
    participant Q as 定时任务
    participant L as 光云平台
    participant DB as 数据库
    
    Note over U,DB: 工单创建流程
    U->>G: 创建工单请求
    G->>W: 转发请求
    W->>S: 验证用户权限
    S-->>W: 权限验证结果
    W->>DB: 保存工单信息
    W->>M: 发送通知消息
    M->>U: 短信/邮件通知
    W-->>G: 返回创建结果
    G-->>U: 响应结果
    
    Note over Q,L: 自动同步流程
    Q->>L: 定时获取告警信息
    L-->>Q: 返回告警数据
    Q->>W: 自动创建工单
    W->>DB: 保存工单
    W->>M: 发送派单通知
```

### 模块职责说明

| 服务模块 | 端口 | 主要职责 | 健康检查 |
|---------|------|----------|----------|
| **API网关** | 58080 | 统一入口、路由转发、负载均衡、限流熔断 | `/actuator/health` |
| **系统管理** | 58081 | 用户管理、权限控制、组织架构、菜单配置 | `/actuator/health` |
| **工单管理** | 58085 | 工单CRUD、流程控制、状态管理、文件上传 | `/actuator/health` |
| **定时任务** | 58082 | 数据同步、超时提醒、定期统计、清理任务 | `/actuator/health` |
| **消息服务** | 58083 | 短信发送、邮件通知、消息模板、推送管理 | `/actuator/health` |
| **系统监控** | 58084 | 服务监控、性能统计、日志管理、告警通知 | `/admin` |

### 项目结构

```
work-order-cloud/
├── bto-cloud-gateway/          # 网关服务 (端口: 58080)
├── bto-cloud-system/           # 系统管理服务 (端口: 58081)
├── bto-cloud-workorder/        # 工单管理服务 (端口: 58085)
├── bto-cloud-api/              # API接口定义
├── bto-cloud-framework/        # 框架核心组件
│   ├── bto-cloud-common/       # 公共组件
│   ├── bto-cloud-security/     # 安全组件
│   └── bto-cloud-mybatis/      # 数据库组件
└── bto-cloud-module/           # 功能模块
    ├── bto-module-quartz/      # 定时任务模块 (端口: 58082)
    ├── bto-module-message/     # 消息服务模块 (端口: 58083)
    └── bto-module-monitor/     # 监控模块 (端口: 58084)
```

## 技术栈

### 后端技术
- **Spring Boot**: 2.6.14
- **Spring Cloud**: 2021.0.1
- **Spring Cloud Alibaba**: 2021.0.1.0
- **Nacos**: 服务注册与配置中心
- **Spring Cloud Gateway**: API网关
- **MyBatis Plus**: 3.5.2 - ORM框架
- **Quartz**: 定时任务调度
- **Spring Boot Admin**: 服务监控

### 数据库与缓存
- **MySQL**: 关系型数据库
- **Redis**: 缓存和会话存储

### 文档与工具
- **Knife4j**: 4.3.0 - API文档
- **Hutool**: 5.7.22 - Java工具库
- **MapStruct**: 1.4.2.Final - 对象映射
- **Lombok**: 简化Java代码

### 云存储支持
- **阿里云OSS**: 3.8.0
- **七牛云**: 7.11.0
- **腾讯云COS**: 5.6.89
- **华为云OBS**: 3.22.3
- **MinIO**: 8.4.3

## 核心功能

### 工单管理
- **工单创建**: 支持光云告警自动创建工单
- **工单派单**: 智能派单给运维人员
- **工单维修**: 维修过程记录和照片上传
- **工单审核**: 多级审核流程和评价系统
- **工单状态**: 待派单 → 待维修 → 待审核 → 已完成/挂起

### 电站管理
- **电站信息**: 电站基础信息管理
- **故障类型**: 多种故障类型分类
- **告警等级**: 不同等级的告警处理
- **保修管理**: 电站保修信息维护

### 系统管理
- **用户管理**: 用户账号和权限管理
- **组织管理**: 组织架构和部门管理
- **菜单管理**: 系统菜单和权限配置
- **角色管理**: 角色权限分配

### 消息通知
- **短信通知**: 支持阿里云、腾讯云短信服务
- **超时提醒**: 工单超时自动提醒
- **状态通知**: 工单状态变更通知

## 环境要求

- **JDK**: 1.8+
- **Maven**: 3.6+
- **MySQL**: 5.7+
- **Redis**: 5.0+
- **Nacos**: 2.1.1+
- **Docker**: 20.10+ (可选)
- **Docker Compose**: 1.29+ (可选)

## 🚀 快速开始

### 📋 环境检查清单

在开始之前，请确保你的开发环境满足以下要求：

- [ ] **JDK 1.8+** - `java -version` 检查版本
- [ ] **Maven 3.6+** - `mvn -version` 检查版本  
- [ ] **MySQL 5.7+** - 数据库服务正常运行
- [ ] **Redis 5.0+** - 缓存服务正常运行
- [ ] **Git** - 用于克隆代码仓库
- [ ] **Docker & Docker Compose** (可选) - 用于容器化部署

> 💡 **快速检查**: 运行 `scripts/check-env.sh` 脚本自动检查环境依赖

### ⚡ 一键启动 (推荐)

#### 方式一：Docker Compose (最简单)

```bash
# 1. 克隆项目
git clone <repository-url>
cd work-order-cloud

# 2. 一键启动所有服务
docker-compose up -d

# 3. 查看服务状态
docker-compose ps

# 4. 验证服务
curl http://localhost:58080/actuator/health
```

#### 方式二：本地开发环境

```bash
# 1. 环境检查
./scripts/check-env.sh

# 2. 启动基础服务
./scripts/start-infrastructure.sh

# 3. 编译项目
mvn clean install -DskipTests

# 4. 启动应用服务
./scripts/start-services.sh
```

### 🔧 详细配置步骤

#### 1. 基础环境准备

**安装Nacos**
```bash
# 下载并启动Nacos
wget https://github.com/alibaba/nacos/releases/download/2.1.1/nacos-server-2.1.1.tar.gz
tar -xzf nacos-server-2.1.1.tar.gz
cd nacos/bin
./startup.sh -m standalone

# 验证Nacos启动
curl http://localhost:8848/nacos
```

**配置数据库**
```sql
-- 创建数据库
CREATE DATABASE work_order_cloud DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 导入初始化脚本
mysql -u root -p work_order_cloud < scripts/init-database.sql
```

**启动Redis**
```bash
# 启动Redis服务
redis-server
# 或使用Docker
docker run -d -p 6379:6379 redis:5.0-alpine
```

### 2. 配置管理

在Nacos配置中心添加以下配置文件：

#### datasource.yaml
```yaml
spring:
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *****************************************************************************************************************
    username: root
    password: your_password
  redis:
    host: localhost
    port: 6379
    password: 
    database: 0
```

#### common.yaml
```yaml
# 公共配置
mybatis-plus:
  global-config:
    db-config:
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
```

### 3. 编译构建

```bash
# 克隆项目
git clone <repository-url>
cd work-order-cloud

# 编译项目
mvn clean install -DskipTests

# 或者指定环境编译
mvn clean install -Ptest -DskipTests
```

### 4. 启动服务

#### 方式一：IDE启动
按以下顺序启动各个服务：
1. GatewayApplication (网关服务)
2. SystemApplication (系统服务)
3. WorkOrderApplication (工单服务)
4. QuartzApplication (定时任务服务)
5. MessageApplication (消息服务)
6. MonitorApplication (监控服务)

#### 方式二：Docker Compose启动
```bash
# 构建并启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f [service-name]
```

### 5. 验证系统启动

#### 🔍 服务健康检查

```bash
# 检查所有服务状态
curl http://localhost:58080/actuator/health  # API网关
curl http://localhost:58081/actuator/health  # 系统服务
curl http://localhost:58085/actuator/health  # 工单服务
curl http://localhost:58082/actuator/health  # 定时任务
curl http://localhost:58083/actuator/health  # 消息服务
curl http://localhost:58084/admin            # 监控服务
```

#### 🌐 系统访问地址

- **API网关**: http://localhost:58080
- **API文档**: http://localhost:58080/doc.html
- **系统监控**: http://localhost:58084/admin
- **Nacos控制台**: http://localhost:8848/nacos (用户名/密码: nacos/nacos)

#### ✅ 功能验证清单

- [ ] 所有服务健康检查通过
- [ ] Nacos控制台可以看到6个服务注册
- [ ] API文档页面正常访问
- [ ] 监控面板显示服务状态
- [ ] 数据库连接正常
- [ ] Redis缓存连接正常

> 💡 **提示**: 完成上述检查后，你的开发环境就完全就绪了！

### 🚨 故障排除指南

#### 常见启动问题

**问题1: 端口被占用**
```bash
# 检查端口占用
netstat -tulpn | grep :58080
# 或使用lsof
lsof -i :58080

# 解决方案：杀死占用进程或修改配置文件中的端口
```

**问题2: Nacos连接失败**
```bash
# 检查Nacos是否启动
curl http://localhost:8848/nacos/v1/ns/operator/metrics

# 检查服务配置中的Nacos地址
# 确保bootstrap.yml中nacos.server-addr配置正确
```

**问题3: 数据库连接失败**
```bash
# 测试数据库连接
mysql -h localhost -u root -p work_order_cloud

# 检查配置文件中的数据库连接信息
# 确保数据库已创建且权限正确
```

**问题4: Redis连接失败**
```bash
# 测试Redis连接
redis-cli ping

# 检查Redis服务状态
systemctl status redis
# 或
brew services list | grep redis
```

#### 日志查看

```bash
# 查看应用日志
tail -f logs/info.log
tail -f logs/error.log

# Docker环境查看日志
docker-compose logs -f gateway
docker-compose logs -f system
docker-compose logs -f workorder
```

## 💻 开发指南

### 📁 项目结构详解

```
src/main/java/com/botong/
├── controller/             # 🎮 控制器层
│   ├── BaseController     # 基础控制器，提供通用方法
│   ├── WorkOrderController # 工单相关接口
│   └── SystemController   # 系统管理接口
├── service/               # 🔧 服务层
│   ├── impl/             # 服务实现类
│   ├── IWorkOrderService # 工单服务接口
│   └── ISystemService    # 系统服务接口
├── dao/                  # 💾 数据访问层
│   ├── mapper/           # MyBatis映射器
│   └── entity/           # 数据库实体类
├── dto/                  # 📦 数据传输对象
│   ├── request/          # 请求DTO
│   └── response/         # 响应DTO
├── vo/                   # 👁️ 视图对象
├── convert/              # 🔄 对象转换器 (MapStruct)
├── enums/                # 📋 枚举类
├── config/               # ⚙️ 配置类
├── exception/            # ❌ 异常处理
└── utils/                # 🛠️ 工具类
```

### 🎯 开发规范

#### 1. 代码规范
```java
// ✅ 推荐的Controller写法
@RestController
@RequestMapping("/api/workorder")
@Api(tags = "工单管理")
@Slf4j
public class WorkOrderController extends BaseController {
    
    @Autowired
    private IWorkOrderService workOrderService;
    
    @PostMapping("/create")
    @ApiOperation("创建工单")
    public Result<WorkOrderVO> createWorkOrder(@RequestBody @Valid CreateWorkOrderDTO dto) {
        WorkOrderVO result = workOrderService.createWorkOrder(dto);
        return Result.success(result);
    }
}
```

#### 2. 服务层规范
```java
// ✅ 推荐的Service写法
@Service
@Transactional(rollbackFor = Exception.class)
public class WorkOrderServiceImpl implements IWorkOrderService {
    
    @Override
    public WorkOrderVO createWorkOrder(CreateWorkOrderDTO dto) {
        // 1. 参数校验
        validateCreateRequest(dto);
        
        // 2. 业务逻辑处理
        WorkOrder entity = WorkOrderConvert.INSTANCE.toEntity(dto);
        entity.setStatus(WorkOrderStatus.PENDING);
        
        // 3. 数据持久化
        workOrderMapper.insert(entity);
        
        // 4. 返回结果
        return WorkOrderConvert.INSTANCE.toVO(entity);
    }
}
```

#### 3. 数据库实体规范
```java
// ✅ 推荐的Entity写法
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("work_order")
@ApiModel("工单实体")
public class WorkOrder extends BaseEntity {
    
    @ApiModelProperty("工单编号")
    private String orderNo;
    
    @ApiModelProperty("工单标题")
    private String title;
    
    @ApiModelProperty("工单状态")
    @TableField(typeHandler = EnumTypeHandler.class)
    private WorkOrderStatus status;
}
```

### 🏗️ API开发示例

#### 1. 标准CRUD接口
```java
@RestController
@RequestMapping("/api/workorder")
public class WorkOrderController {
    
    // 分页查询
    @GetMapping("/page")
    public Result<IPage<WorkOrderVO>> getWorkOrderPage(WorkOrderQueryDTO query) {
        IPage<WorkOrderVO> page = workOrderService.getWorkOrderPage(query);
        return Result.success(page);
    }
    
    // 详情查询
    @GetMapping("/{id}")
    public Result<WorkOrderVO> getWorkOrderById(@PathVariable Long id) {
        WorkOrderVO vo = workOrderService.getWorkOrderById(id);
        return Result.success(vo);
    }
    
    // 创建
    @PostMapping
    public Result<WorkOrderVO> createWorkOrder(@RequestBody @Valid CreateWorkOrderDTO dto) {
        WorkOrderVO vo = workOrderService.createWorkOrder(dto);
        return Result.success(vo);
    }
    
    // 更新
    @PutMapping("/{id}")
    public Result<WorkOrderVO> updateWorkOrder(@PathVariable Long id, 
                                              @RequestBody @Valid UpdateWorkOrderDTO dto) {
        WorkOrderVO vo = workOrderService.updateWorkOrder(id, dto);
        return Result.success(vo);
    }
    
    // 删除
    @DeleteMapping("/{id}")
    public Result<Void> deleteWorkOrder(@PathVariable Long id) {
        workOrderService.deleteWorkOrder(id);
        return Result.success();
    }
}
```

### 🧪 测试指南

#### 1. 单元测试
```java
@SpringBootTest
@Transactional
@Rollback
class WorkOrderServiceTest {
    
    @Autowired
    private IWorkOrderService workOrderService;
    
    @Test
    void testCreateWorkOrder() {
        // Given
        CreateWorkOrderDTO dto = new CreateWorkOrderDTO();
        dto.setTitle("测试工单");
        dto.setDescription("测试描述");
        
        // When
        WorkOrderVO result = workOrderService.createWorkOrder(dto);
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result.getTitle()).isEqualTo("测试工单");
    }
}
```

#### 2. 集成测试
```java
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
class WorkOrderControllerIntegrationTest {
    
    @Autowired
    private TestRestTemplate restTemplate;
    
    @Test
    void testCreateWorkOrderAPI() {
        // 测试API接口
        CreateWorkOrderDTO dto = new CreateWorkOrderDTO();
        dto.setTitle("集成测试工单");
        
        ResponseEntity<Result> response = restTemplate.postForEntity(
            "/api/workorder", dto, Result.class);
        
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
    }
}
```

### 🐛 调试技巧

#### 1. 日志配置
```yaml
# application-dev.yml
logging:
  level:
    com.botong: DEBUG
    org.springframework.web: DEBUG
    com.baomidou.mybatisplus: DEBUG
```

#### 2. 常用调试工具
- **Knife4j**: API文档和在线测试 - http://localhost:58080/doc.html
- **Spring Boot Admin**: 服务监控 - http://localhost:58084/admin
- **Nacos**: 配置管理 - http://localhost:8848/nacos
- **MySQL Workbench**: 数据库管理
- **Redis Desktop Manager**: Redis可视化工具

#### 3. 性能调优
```java
// 使用@Async进行异步处理
@Async
public CompletableFuture<Void> sendNotificationAsync(String message) {
    // 异步发送通知
    return CompletableFuture.completedFuture(null);
}

// 使用缓存提升性能
@Cacheable(value = "workorder", key = "#id")
public WorkOrderVO getWorkOrderById(Long id) {
    return workOrderMapper.selectById(id);
}
```

### 📋 数据库设计规范

#### 1. 基础实体类
```java
@Data
@ApiModel("基础实体")
public class BaseEntity {
    
    @ApiModelProperty("主键ID")
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;
    
    @ApiModelProperty("创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    
    @ApiModelProperty("更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
    
    @ApiModelProperty("创建人")
    @TableField(fill = FieldFill.INSERT)
    private String createBy;
    
    @ApiModelProperty("更新人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;
    
    @ApiModelProperty("逻辑删除标识")
    @TableLogic
    private Integer deleted;
}
```

#### 2. 数据库命名规范
- **表名**: 使用下划线分隔，如 `work_order`
- **字段名**: 使用下划线分隔，如 `create_time`
- **索引名**: `idx_表名_字段名`，如 `idx_work_order_status`
- **外键名**: `fk_表名_关联表名`，如 `fk_work_order_user`

## 部署说明

### 环境配置
系统支持多环境配置：
- **dev**: 开发环境 (默认)
- **test**: 测试环境
- **prod**: 生产环境

### Docker部署
每个模块都包含Dockerfile，支持容器化部署：

```bash
# 构建镜像
docker build -t work-order-gateway ./bto-cloud-gateway
docker build -t work-order-system ./bto-cloud-system
docker build -t work-order-workorder ./bto-cloud-workorder

# 运行容器
docker run -d -p 58080:58080 work-order-gateway
```

## API文档

系统集成了Knife4j，提供完整的API文档：
- 访问地址: http://localhost:58080/doc.html
- 支持在线测试API接口
- 自动生成接口文档

## 监控运维

### 服务监控
- Spring Boot Admin监控面板
- 访问地址: http://localhost:58084/admin
- 实时监控各服务状态、JVM信息、日志等

### 日志管理
- 日志文件位置: `./logs/`
- 支持按级别分类: debug.log, info.log, warn.log, error.log

## 常见问题

### 1. 服务启动失败
- 检查Nacos是否正常运行
- 确认数据库连接配置正确
- 检查端口是否被占用

### 2. 配置不生效
- 确认Nacos配置中心配置正确
- 检查配置文件格式是否正确
- 重启相关服务

### 3. 接口调用失败
- 检查服务注册状态
- 确认网关路由配置
- 查看服务日志排查问题

## 📚 学习资源

### 🎓 技术栈学习路径

#### 后端技术
- **Spring Boot 官方文档**: https://spring.io/projects/spring-boot
- **Spring Cloud 官方指南**: https://spring.io/projects/spring-cloud
- **MyBatis Plus 官方文档**: https://baomidou.com/
- **Nacos 官方文档**: https://nacos.io/zh-cn/docs/quick-start.html

#### 微服务架构
- **微服务架构设计模式**: https://microservices.io/
- **Spring Cloud Alibaba**: https://github.com/alibaba/spring-cloud-alibaba
- **服务网格与微服务**: https://istio.io/latest/docs/

#### 开发工具
- **Maven 官方指南**: https://maven.apache.org/guides/
- **Docker 官方教程**: https://docs.docker.com/get-started/
- **Git 版本控制**: https://git-scm.com/doc

### 📖 业务知识库

#### 光伏行业知识
- **光伏发电基础知识**: [[内部文档链接](https://workorder.btosolarman.com/#/sys/attachment/index)]
- **电站运维管理规范**: [[内部文档链接](https://workorder.btosolarman.com/#/sys/attachment/index)]
- **故障诊断与处理**: [[内部文档链接](https://workorder.btosolarman.com/#/sys/attachment/index)]

#### 工单管理流程
- **工单生命周期管理**: [查看开发日志](development-log.md)
- **业务流程图**: [内部流程文档]
- **用户操作手册**: [[用户手册链接](https://qiniu.btosolarman.com/work-order-cloud/common/%E5%8D%9A%E9%80%9A%E5%B7%A5%E5%8D%95%E7%B3%BB%E7%BB%9F%EF%BC%88Web%E7%AB%AF%EF%BC%89%E8%BF%90%E7%BB%B4%E4%BA%BA%E5%91%98%E8%AF%B4%E6%98%8E%E4%B9%A6.pdf)]

### 🆘 获得帮助

#### 即时帮助
- **技术问题**: 在项目 Issues 中提问
- **业务咨询**: 联系项目负责人
- **紧急支持**: 技术支持热线

#### 文档资源
- **API文档**: http://localhost:58080/doc.html
- **开发日志**: [development-log.md](development-log.md)
- **常见问题**: 查看上方故障排除指南

### 🤝 贡献指南

#### 开发流程
1. **Fork 项目** - 从主仓库创建分支
2. **创建特性分支** - `git checkout -b feature/amazing-feature`
3. **开发功能** - 遵循代码规范和最佳实践
4. **编写测试** - 确保代码质量和覆盖率
5. **提交代码** - `git commit -m 'feat: add amazing feature'`
6. **推送分支** - `git push origin feature/amazing-feature`
7. **创建PR** - 提交 Pull Request 并等待审查

#### 提交规范
```bash
# 提交消息格式
<type>(<scope>): <subject>

# 示例
feat(workorder): add batch create functionality
fix(system): resolve user permission issue
docs(readme): update installation guide
```

#### 代码审查清单
- [ ] 代码符合项目规范
- [ ] 包含必要的单元测试
- [ ] API文档已更新
- [ ] 无安全漏洞
- [ ] 性能影响评估

#### 发布流程
1. **功能开发** - 在feature分支完成开发
2. **测试验证** - 通过所有自动化测试
3. **代码审查** - 至少一位团队成员审查
4. **合并主分支** - 合并到develop分支
5. **集成测试** - 在测试环境验证
6. **发布上线** - 合并到master分支并发布

### 📞 联系方式

#### 团队联系
- **项目负责人**: BTO团队
- **技术负责人**: 研发中心：张海彬、叶昊
- **产品负责人**: 运维中心：孙建伟

#### 项目信息
- **项目仓库**: [项目仓库地址](https://gitee.com/guangdong-botong-new-energy/work-order-cloud)（需要联系总监开通权限）

---

## 🎉 致谢

感谢所有为本项目做出贡献的开发者和团队成员！

**⚠️ 重要提醒**: 请在生产环境部署前，务必修改默认密码和敏感配置信息。
