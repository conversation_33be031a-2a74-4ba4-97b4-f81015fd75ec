# 博通工单管理系统开发日志

## 项目概述

博通工单管理系统是一个基于Spring Cloud微服务架构的企业级工单管理平台，专门用于光伏电站的运维管理。本文档记录了从2023年7月至今的开发历程和重要里程碑。

## 目录

- [2025年7月](#2025年7月)
- [2025年6月](#2025年6月)
- [2025年5月](#2025年5月)
- [2025年4月](#2025年4月)
- [2025年3月](#2025年3月)
- [2025年2月](#2025年2月)
- [2025年1月](#2025年1月)
- [2024年12月](#2024年12月)
- [2024年11月](#2024年11月)
- [2024年10月](#2024年10月)
- [2024年5月](#2024年5月)
- [2024年2月](#2024年2月)
- [2024年1月](#2024年1月)
- [2023年12月](#2023年12月)
- [2023年11月](#2023年11月)
- [2023年10月](#2023年10月)
- [2023年9月](#2023年9月)
- [2023年8月](#2023年8月)

---

## 2025年7月

### 开发活动概述
7月份是系统功能完善和用户体验优化的重要时期，主要专注于工程师评估评分系统的开发、操作日志功能的完善，以及工单管理功能的增强。

### 新增功能
- **工程师评估评分系统**: 实现了完整的工程师评估评分功能，支持多维度评分统计和导出
- **操作日志管理**: 添加了全面的操作日志记录和管理功能，提升系统审计能力
- **工单考核系数**: 新增工单考核系数查询功能，为评估系统提供基础数据支持
- **维修人员筛选**: 增加了维修人员筛选功能，优化工单分配流程
- **工单修复时间**: 添加工单修复时间字段，完善工单时间管理

### 功能优化
- **电站管理增强**: 电站列表新增是否生成工单逻辑和工单存在标识，提升管理效率
- **查询功能改进**: 修复电站名称模糊查询功能，提升搜索体验
- **操作日志优化**: 优化操作日志记录功能，增强日志记录的准确性和完整性

### 系统改进
- **项目文档完善**: 重构README文档结构，添加快速启动指南和开发指南
- **代码质量提升**: 更新.gitignore文件，优化项目结构管理
- **注解系统**: 为各个控制器添加操作日志注解，实现统一的操作记录

### 错误修复
- **评价分数查询**: 修复工作评价分数查询和操作中的问题，优化时间查询条件
- **数据过滤**: 在评分系统中过滤总工单数为0的工程师分数，提升数据准确性
- **物料信息处理**: 增加对物料信息的非空检查和重复key处理

### 技术架构改进
- **操作日志架构**: 建立了完整的操作日志记录架构，包括注解、切面、DTO和枚举
- **评估系统架构**: 构建了工程师评估评分的完整技术架构，支持多维度评分计算
- **缓存优化**: 优化Redis缓存键管理，提升系统性能

### 开发里程碑
- **7月2日**: 完成2025年1-6月开发日志补充，项目文档体系完善
- **7月3日**: 工单考核系数查询功能上线
- **7月7日**: 工程师评估评分功能完整实现
- **7月16-17日**: 项目文档重构和问题修复
- **7月23-24日**: 操作日志系统全面上线
- **7月26日**: 维修人员筛选和电站管理功能优化
- **7月29日**: 工单修复时间字段添加完成

---

## 2025年6月

### 开发活动概述
6月份主要专注于系统文档完善、查询功能优化和电费分析功能的重构工作。

### 文档更新
- **项目文档完善**: 添加了项目README文档，完善了开发日志文档
- **技术文档**: 补充了系统架构和使用说明

### 功能优化
- **电站电费分析**: 重构了电站电费分析功能，提升了数据处理效率
- **查询参数优化**: 优化了PlantAlarmQuery查询参数的使用方式
- **等效利用小时数**: 增加了等效利用小时数计算字段并优化了查询条件

### 错误修复
- 修复了查询电站告警信息时的字段映射错误问题
- 解决了电费账单处理逻辑中的相关问题

---

## 2025年5月

### 开发活动概述
5月份重点进行了电费电量分析功能的开发和工具库存管理的完善。

### 新增功能
- **电费电量分析**: 新增了电站电费电量分析功能，支持多维度数据统计
- **工具库存管理**: 添加了运维工具库导出功能和附件字段支持
- **未完成工单统计**: 增加了未完成工单数量统计功能
- **二次增值汇总**: 实现了二次增值台的汇总功能

### 功能优化
- **用户列表接口**: 优化了用户列表接口并重构了相关代码
- **电费列表查询**: 优化了电费列表查询SQL及返回结果结构
- **工具库存排序**: 为工具库存查询结果添加了按更新时间降序排序

### 错误修复
- 修复了工单查询时间范围的bug，增加了对startTime和endTime的空值检查
- 修改了LowPowerPlantVO创建时间格式

### 配置管理
- 同步了生产环境端口配置
- 添加了Docker部署配置

---

## 2025年4月

### 开发活动概述
4月份主要进行了工单费用管理和用户报表功能的开发。

### 新增功能
- **用户工单运维报表**: 添加了用户工单运维报表功能，支持个人工单数据统计

### 功能优化
- **工单费用列表**: 优化了工单费用列表的排序功能

---

## 2025年3月

### 开发活动概述
3月份是功能扩展的重要时期，新增了多个重要的管理和统计功能。

### 新增功能
- **运维工具库**: 新增了运维工具库功能，支持工具的入库、出库和库存管理
- **低电量电站统计**: 添加了低电量电站统计功能，支持发电率筛选
- **月度运维报表**: 实现了月度工单运维报表功能，支持导出
- **工单城市监控**: 添加了工单城市监控功能
- **附件分类管理**: 新增了附件分类管理功能并优化了附件查询
- **二次增值账单**: 增加了二次增值账单导出导入功能

### 功能优化
- **工单导入导出**: 调整了工单导入导出字段并优化了相关VO类
- **低效电站查询**: 优化了低效电站查询逻辑，增加了条件筛选功能
- **电气图处理**: 优化了电气图和现场图的处理逻辑
- **工单状态查询**: 优化了工单状态查询功能

### 系统改进
- **验证码升级**: 更新验证码为算术类型
- **代码重构**: 移除了冗余代码，优化了代码结构

### 错误修复
- 修复了城市平均工时计算逻辑的问题

---

## 2025年2月

### 开发活动概述
2月份主要进行了电费汇总功能的优化和故障管理功能的扩展。

### 功能优化
- **电费汇总导入**: 优化了电费汇总表导入Excel功能
- **合并账单导出**: 优化了合并账单导出功能
- **告警信息查询**: 重构了告警信息查询功能，改为返回IPage类型

### 功能扩展
- **故障类型扩展**: 扩展了故障类型和设备的范围
- **电站图纸查询**: 新增了电站图纸查询功能并优化了统计相关接口
- **二次增值费用**: 新增了二次增值费用功能

### 错误修复
- 修复了电站查询和质保过期逻辑的问题

### 代码改进
- 优化了代码结构和导入内容，移除了未使用的导包

---

## 2025年1月

### 开发活动概述
1月份是系统功能大幅扩展的重要时期，新增了多个财务管理和费用统计相关功能。

### 新增功能
- **电费账单管理**: 新增了跨省电费账单功能，支持账单合并和导出
- **运维费用管理**: 添加了运维费用方案、运维费单价管理和运维费账单功能
- **工单费用账单**: 实现了工单费用账单功能，支持费用明细权限控制
- **设备管理**: 新增了设备管理功能并优化了工单统计相关接口
- **工单挂起**: 新增了工单挂起功能
- **工单统计**: 添加了工单统计相关功能

### 功能优化
- **电费汇总**: 优化了电费汇总功能和更新逻辑
- **电费结算**: 优化了电费结算单导入报错问题
- **字典查询**: 新增了字典类型动态SQL查询功能
- **定时任务**: 实现了定时任务同步光云系统组织功能

### 系统改进
- **权限控制**: 添加了费用明细权限控制和相关权限管理
- **数据处理**: 优化了电费汇总表数据处理逻辑，提高了效率
- **配置优化**: 优化了bootstrap.yml配置

### 错误修复
- 修复了电费对账单中的项目公司数据来源问题
- 修复了工单统计接口SQL查询问题
- 修复了电费结算单导入报错问题

### 代码重构
- 移除了审核评价必填限制并重置了工单状态
- 优化了工单审核DTO日期格式处理
- 调整了故障相关字段的取值范围

---

## 2024年12月

### 开发活动概述
12月份主要专注于系统功能扩展和代码合并工作。

### 新增功能
- **上网电量电费管理功能**: 新增了电力管理相关的功能模块，支持电量和电费的统计管理

### 代码维护
- 完成了多个分支的代码合并工作，确保代码库的一致性

---

## 2024年11月

### 开发活动概述
11月份重点进行了工单功能的重大升级和问题修复。

### 新增功能
- **批量申请工单功能**: 实现了批量创建工单的功能，大幅提升了工作效率
- **过保字段管理**: 添加了设备过保状态的修改和回显功能

### 错误修复
- 修复了电站类型转换逻辑的问题
- 解决了非光云平台工单导入失败的问题
- 修复了保存操作时字段清空的问题

---

## 2024年10月

### 开发活动概述
10月份是系统功能完善的重要月份，进行了大量的功能优化和权限管理改进。

### 新增功能
- **多平台工单支持**: 添加了platform字段，实现了其他平台申请工单的功能
- **用户多机构设计**: 支持用户关联多个机构的功能
- **数据权限管理**: 完善了角色数据权限分配和管理
- **短信通知开关**: 添加了短信通知的开启关闭功能，避免产生过多短信费用

### 功能优化
- 优化了工单列表的多状态查询功能
- 改进了Excel导入功能，支持光云平台与非光云平台
- 完善了超时工单的处理逻辑
- 优化了区域统计和工单查询条件

### 错误修复
- 修复了获取维修人员列表接口权限问题
- 解决了数据权限的本机构数据空指针问题
- 修复了新建工单重复创建的问题
- 解决了token刷新相关问题

---

## 2024年5月

### 开发活动概述
5月份主要进行了代码重构和性能优化工作。

### 代码改进
- 优化了buildRegionTree方法的实现
- 删除了多余的代码，提升了代码质量

---

## 2024年2月

### 开发活动概述
2月份专注于告警数据的优化工作。

### 功能优化
- 对告警数据进行了全面优化，提升了数据处理效率

---

## 2024年1月

### 开发活动概述
1月份主要进行了历史数据修复和定时任务优化。

### 错误修复
- 完成了历史数据修复，传统户用新增更细分类
- 修复了定时任务短信发送失败的问题

---

## 2023年12月

### 开发活动概述
12月份重点完善了系统的API接口和依赖管理。

### 新增功能
- **图片上传功能**: WorkBaseInfoEntity添加了issue_photo字段，支持故障报修照片上传
- **API接口扩展**: 实现了getAccessToken、getSubOrgIdList、getOrgIdWithRepairUserByProjectSpecial等关键API

### 技术改进
- 更新了项目依赖版本
- 修复了数据权限异常问题
- 新增了定时任务测试开放平台接口稳定性功能

---

## 2023年11月

### 开发活动概述
11月份主要进行了工单数据完整性和系统稳定性的改进。

### 错误修复
- 修复了保存操作时没有清空对应字段的问题
- 解决了非光云平台工单导入失败的问题
- 修复了getOrgNamesByIds方法的SQL错误问题

### 功能完善
- 添加了过保字段的修改和回显功能

---

## 2023年10月

### 开发活动概述
10月份是系统架构完善和功能扩展的重要时期，进行了大量的代码重构和注释添加工作。

### 新增功能
- **多平台支持**: 实现了其他平台申请工单的功能
- **用户多机构管理**: 支持用户关联多个机构
- **权限管理优化**: 完善了数据范围权限和角色权限管理
- **验证码功能**: 添加了数字响应类型的验证码

### 代码改进和重构
- 在各个模块中添加了详细的代码注释
- 优化了日志文件配置和保留天数设置
- 重构了多个服务的命名规范

### 错误修复
- 修复了无法创建其他平台工单的问题
- 解决了设置preRepairTime不生效的问题
- 修复了超时短信获取号码为空的问题
- 解决了角色数据权限分配全部数据时的异常

---

## 2023年9月

### 开发活动概述
9月份是系统核心功能开发的关键时期，完成了工单管理的核心业务逻辑实现。

### 新增功能
- **工单审核系统**: 实现了完整的工单审核流程
- **数据范围权限**: 添加了基于角色的数据访问权限控制
- **短信通知系统**: 集成了短信模板发送和超时提醒功能
- **文件存储管理**: 实现了七牛云文件存储的批量删除功能

### 系统集成
- **光伏系统对接**: 完成了与光伏系统的数据同步和token认证
- **定时任务集成**: 实现了Quartz定时任务的依赖注入和Job管理
- **Feign调用优化**: 完善了微服务间的调用配置和日志记录

### 错误修复
- 修复了工单修改方法不成功的问题
- 解决了超时工单分页和区域树显示问题
- 修复了用户组织结构枚举的字段类型问题

---

## 2023年8月

### 开发活动概述
8月份是项目的初始开发阶段，完成了系统的基础架构搭建和核心功能实现。

### 项目初始化
- **项目创建**: 完成了博通工单管理系统的初始化
- **微服务架构**: 搭建了基于Spring Cloud的微服务架构
- **基础模块**: 创建了gateway、system、workorder等核心模块

### 核心功能实现
- **工单管理**: 实现了工单的增删改查基础功能
- **工单流程**: 完成了工单申请、派单、维修、审核的完整流程
- **电站管理**: 实现了电站信息的基础管理功能
- **用户权限**: 建立了基础的用户权限管理体系

### 技术架构
- **数据库设计**: 完成了工单相关表结构的设计
- **API文档**: 集成了Knife4j进行API文档管理
- **Excel功能**: 实现了工单数据的导入导出功能
- **热部署**: 配置了开发环境的热部署功能

### 系统集成
- **网关配置**: 完成了Spring Cloud Gateway的路由配置
- **服务注册**: 集成了Nacos作为服务注册与配置中心
- **缓存管理**: 添加了Redis缓存管理器

---

## 技术栈演进

### 核心技术
- **Spring Boot**: 2.6.14
- **Spring Cloud**: 2021.0.1
- **Spring Cloud Alibaba**: 2021.0.1.0
- **MyBatis Plus**: 3.5.2

### 新增技术组件
- **Quartz**: 定时任务调度
- **Knife4j**: API文档生成
- **七牛云**: 文件存储服务
- **短信服务**: 阿里云/腾讯云短信集成

### 开发工具
- **MapStruct**: 对象映射
- **Hutool**: Java工具库
- **Lombok**: 代码简化
- **EasyExcel**: Excel处理

---

## 项目里程碑

1. **2023年8月**: 项目启动，完成基础架构搭建
2. **2023年9月**: 核心业务功能实现，系统集成完成
3. **2023年10月**: 权限管理完善，多平台支持
4. **2023年11-12月**: 系统稳定性提升，API扩展
5. **2024年1-2月**: 数据优化，历史问题修复
6. **2024年10-11月**: 功能扩展，批量操作支持
7. **2024年12月**: 电力管理功能集成
8. **2025年1-6月**: 财务管理系统完善，电费电量分析功能开发
9. **2025年7月**: 工程师评估评分系统上线，操作日志功能完善

---

## 开发团队贡献

项目开发过程中，团队成员在不同阶段承担了重要角色：
- **架构设计**: 完成了微服务架构的设计和实现
- **业务开发**: 实现了工单管理的完整业务流程
- **系统集成**: 完成了与第三方系统的对接
- **质量保证**: 持续进行代码重构和问题修复

---

*本开发日志将持续更新，记录项目的最新进展和重要变更。*
