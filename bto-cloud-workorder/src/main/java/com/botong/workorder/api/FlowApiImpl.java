package com.botong.workorder.api;

import com.botong.api.module.workorder.FlowApi;
import com.botong.framework.common.utils.Result;
import com.botong.workorder.service.WorkOrderProcessManageService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.RestController;

/**
 * 流程API实现类
 * 实现流程相关的API接口，提供工单流程管理功能
 * <AUTHOR>
 * @since 2025-01-14 11:55
 */
@RestController
@AllArgsConstructor
public class FlowApiImpl implements FlowApi {
    private final WorkOrderProcessManageService workOrderProcessManageService;

    /**
     * 取消工单挂起状态
     * @return 操作结果
     */
    @Override
    public Result<String> unsuspend() {
        workOrderProcessManageService.unsuspend();
        return Result.ok();
    }
}
