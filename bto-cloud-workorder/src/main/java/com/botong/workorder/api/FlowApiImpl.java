package com.botong.workorder.api;

import com.botong.api.module.workorder.FlowApi;
import com.botong.framework.common.utils.Result;
import com.botong.workorder.service.WorkOrderProcessManageService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @since 2025/1/14 11:55
 */
@RestController
@AllArgsConstructor
public class FlowApiImpl implements FlowApi {
    private final WorkOrderProcessManageService workOrderProcessManageService;

    @Override
    public Result<String> unsuspend() {
        workOrderProcessManageService.unsuspend();
        return Result.ok();
    }
}
