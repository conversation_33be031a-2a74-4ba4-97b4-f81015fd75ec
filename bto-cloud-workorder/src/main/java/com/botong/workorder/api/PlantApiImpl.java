package com.botong.workorder.api;

import com.botong.api.module.photovoltaic.vo.PlantInfoVO;
import com.botong.api.module.workorder.PlantApi;
import com.botong.api.module.workorder.entity.OrgBaseEntity;
import com.botong.framework.common.utils.Result;
import com.botong.workorder.convert.PlantBaseInfoConvert;
import com.botong.workorder.entity.PlantBaseInfoEntity;
import com.botong.workorder.service.PlantBaseInfoService;
import lombok.AllArgsConstructor;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * feign请求电站管理实现类
 * 提供电站信息的批量保存、项目查询和更新功能
 * <AUTHOR> by zhb on 2023/9/12.
 */
@RestController
@AllArgsConstructor
public class PlantApiImpl implements PlantApi {

    private final PlantBaseInfoService plantBaseInfoService;

    /**
     * 批量保存电站信息
     * @param plantVOList 电站信息列表
     * @return 保存结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> saveBatch(List<PlantInfoVO> plantVOList) {
        List<PlantBaseInfoEntity> list = PlantBaseInfoConvert.INSTANCE.convertEntityList(plantVOList);
        return Result.ok(plantBaseInfoService.saveBatch(list));
    }

    /**
     * 获取所有项目信息
     * @return 项目列表
     */
    @Override
    public List<OrgBaseEntity> getAllProject() {
        return plantBaseInfoService.getAllProject();
    }

    /**
     * 根据名称更新项目信息
     * @param redisName Redis中的项目名称
     * @param name 需要更新的新名称
     */
    @Override
    public void updateProjectByName(String redisName, String name) {
        plantBaseInfoService.updateProjectByName(redisName, name);
    }

}