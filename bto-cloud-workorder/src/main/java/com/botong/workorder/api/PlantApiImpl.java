package com.botong.workorder.api;

import com.botong.api.module.photovoltaic.vo.PlantInfoVO;
import com.botong.api.module.workorder.PlantApi;
import com.botong.api.module.workorder.entity.OrgBaseEntity;
import com.botong.framework.common.utils.Result;
import com.botong.workorder.convert.PlantBaseInfoConvert;
import com.botong.workorder.entity.PlantBaseInfoEntity;
import com.botong.workorder.service.PlantBaseInfoService;
import lombok.AllArgsConstructor;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * feign请求电站管理实现类
 * <AUTHOR> by zhb on 2023/9/12.
 */
@RestController
@AllArgsConstructor
public class PlantApiImpl implements PlantApi {

    private final PlantBaseInfoService plantBaseInfoService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> saveBatch(List<PlantInfoVO> plantVOList) {
        List<PlantBaseInfoEntity> list = PlantBaseInfoConvert.INSTANCE.convertEntityList(plantVOList);
        return Result.ok(plantBaseInfoService.saveBatch(list));
    }

    @Override
    public List<OrgBaseEntity> getAllProject() {
        return plantBaseInfoService.getAllProject();
    }

    @Override
    public void updateProjectByName(String redisName, String name) {
        plantBaseInfoService.updateProjectByName(redisName, name);
    }

}