package com.botong.workorder.utils;

import com.botong.framework.common.page.PageResult;
import com.botong.framework.common.query.Query;
import lombok.RequiredArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * 分页工具类
 * <AUTHOR>
 */
@RequiredArgsConstructor
public class PageUtils {

    /**
     * 分页
     * @param data  数据
     * @param query 分页参数
     * @return 分页数据
     */
    public static <T, R extends Query> PageResult<T> page(List<T> data, R query) {
        int total = data.size();
        int startIndex = (query.getPage() - 1) * query.getLimit();
        int endIndex = Math.min(startIndex + query.getLimit(), total);

        if (startIndex >= total) {
            return new PageResult<>(new ArrayList<>(), total);
        }

        List<T> pageData = data.subList(startIndex, endIndex);
        return new PageResult<>(pageData, total);
    }
}