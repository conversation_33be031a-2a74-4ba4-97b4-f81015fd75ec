package com.botong.workorder.utils;

import com.botong.framework.common.page.PageResult;
import com.botong.framework.common.query.Query;
import lombok.RequiredArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@RequiredArgsConstructor
public class PageUtils {
    public static <T, R extends Query> PageResult<T> page(List<T> data, R query) {
        int total = data.size();
        int startIndex = (query.getPage() - 1) * query.getLimit();
        int endIndex = Math.min(startIndex + query.getLimit(), total);

        if (startIndex >= total) {
            return new PageResult<>(new ArrayList<>(), total);
        }

        List<T> pageData = data.subList(startIndex, endIndex);
        return new PageResult<>(pageData, total);
    }
}