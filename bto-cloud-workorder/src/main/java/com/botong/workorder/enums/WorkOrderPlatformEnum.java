package com.botong.workorder.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 工单平台类型枚举
 * <AUTHOR>
 */

@Getter
@AllArgsConstructor
public enum WorkOrderPlatformEnum {

    LIGHT_CLOUD(0, "光云平台"),
    OTHER(1, "非光云平台");

    private final Integer value;
    private final String name;

    public static String getNameByValue(Integer value) {
        for (WorkOrderPlatformEnum s : WorkOrderPlatformEnum.values()) {
            if (s.getValue().equals(value)) {
                return s.getName();
            }
        }
        return "";
    }

    public static Integer getValueByName(String name) {
        for (WorkOrderPlatformEnum s : WorkOrderPlatformEnum.values()) {
            if (Objects.equals(s.getName(), name)) {
                return s.getValue();
            }
        }
        return null;
    }

}