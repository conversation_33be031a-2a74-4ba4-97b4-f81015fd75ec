package com.botong.workorder.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 工单超时状态枚举
 * <AUTHOR>
 * @since 2023/9/12 8:59
 */
@Getter
@AllArgsConstructor
public enum WorkOrderOvertimeEnum {

    /**
     * 正常
     */
    NORMAL(0, "正常"),

    /**
     * 超时
     */
    TIMEOUT(1, "超时"),

    /**
     * 即将超时
     */
    ABOUT_TO_TIMEOUT(2, "即将超时");


    private final Integer value;
    private final String name;

    public static String getNameByValue(Integer value) {
        for (WorkOrderStatusEnum s : WorkOrderStatusEnum.values()) {
            if (s.getValue().equals(value)) {
                return s.getName();
            }
        }
        return "";
    }

    public static Integer getValueByName(String name) {
        for (WorkOrderStatusEnum s : WorkOrderStatusEnum.values()) {
            if (Objects.equals(s.getName(), name)) {
                return s.getValue();
            }
        }
        return null;
    }
}
