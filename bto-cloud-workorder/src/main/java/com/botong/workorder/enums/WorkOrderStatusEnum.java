package com.botong.workorder.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 工单状态枚举
 *
 * <AUTHOR>
 */

@Getter
@AllArgsConstructor
public enum WorkOrderStatusEnum {
    /**
     * 待派单
     */
    TO_BE_ASSIGN(0, "待派单"),
    /**
     * 待维修
     */
    TO_BE_REPAIR(1, "待维修"),
    /**
     * 待审核
     */
    TO_BE_AUDIT(2, "待审核"),

    /**
     * 已完成
     */
    COMPLETED(3, "已完成"),

    /**
     * 挂起
     */
    SUSPENDED(4, "挂起");


    private final Integer value;
    private final String name;

    public static String getNameByValue(Integer value) {
        for (WorkOrderStatusEnum s : WorkOrderStatusEnum.values()) {
            if (s.getValue().equals(value)) {
                return s.getName();
            }
        }
        return "";
    }

    public static Integer getValueByName(String name) {
        for (WorkOrderStatusEnum s : WorkOrderStatusEnum.values()) {
            if (Objects.equals(s.getName(), name)) {
                return s.getValue();
            }
        }
        return null;
    }

    public static List<Integer> getAllValue() {
        ArrayList<Integer> values = new ArrayList<>();
        for (WorkOrderStatusEnum s : WorkOrderStatusEnum.values()) {
            values.add(s.getValue());
        }
        return values;
    }

}
