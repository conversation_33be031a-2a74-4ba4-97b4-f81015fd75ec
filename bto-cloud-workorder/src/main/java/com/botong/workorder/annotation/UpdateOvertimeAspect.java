package com.botong.workorder.annotation;

import com.botong.workorder.service.WorkOperationInfoService;
import lombok.RequiredArgsConstructor;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.springframework.stereotype.Component;

import java.util.concurrent.CompletableFuture;

/**
 * 更新超时时间
 *
 * <AUTHOR>
 * @since 2023-09-11 16:39:40
 */
@Aspect
@Component
@RequiredArgsConstructor
public class UpdateOvertimeAspect {
    private final WorkOperationInfoService workOperationInfoService;

    @Before("@annotation(com.botong.workorder.annotation.UpdateOvertime)")
    public void beforeUpdateOvertime() {
        CompletableFuture.runAsync(() -> workOperationInfoService.updateOvertime(null));
    }
}