package com.botong.workorder.annotation;

import com.botong.workorder.annotation.handle.TimeoutDaysValidator;

import javax.validation.Constraint;
import javax.validation.Payload;
import javax.validation.constraints.NotNull;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import static java.lang.annotation.ElementType.FIELD;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

/**
 * 超时警告阈值校验注解
 * 用于校验超时警告阈值是否在允许的范围内
 * <AUTHOR> by zhb on 2023/9/15.
 */

@NotNull(message = "超时警告阈值不能为空")
@Target({FIELD})
@Retention(RUNTIME)
@Constraint(validatedBy = {TimeoutDaysValidator.class})
public @interface TimeoutDays {
    /**
     * 校验失败时的错误消息
     * @return 错误消息
     */
    String message() default "超时警告阈值不正确";

    /**
     * 校验分组
     * @return 分组数组
     */
    Class<?>[] groups() default {};

    /**
     * 负载类型
     * @return 负载类型数组
     */
    Class<? extends Payload>[] payload() default {};

    /**
     * 允许的超时警告阈值
     * @return 允许的值数组，默认值为 {1, 3, 5, 7}
     */
    int[] values() default {1, 3, 5, 7};

}