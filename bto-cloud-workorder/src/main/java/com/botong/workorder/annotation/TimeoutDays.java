package com.botong.workorder.annotation;

import com.botong.workorder.annotation.handle.TimeoutDaysValidator;

import javax.validation.Constraint;
import javax.validation.Payload;
import javax.validation.constraints.NotNull;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import static java.lang.annotation.ElementType.FIELD;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

/**
 * 超时警告阈值校验注解
 * <AUTHOR> by zhb on 2023/9/15.
 */

@NotNull(message = "超时警告阈值不能为空")
@Target({FIELD})
@Retention(RUNTIME)
@Constraint(validatedBy = {TimeoutDaysValidator.class})
public @interface TimeoutDays {
    String message() default "超时警告阈值不正确";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};

    int[] values() default {1, 3, 5, 7};

}