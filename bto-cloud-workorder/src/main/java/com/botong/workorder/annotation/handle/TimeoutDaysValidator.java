package com.botong.workorder.annotation.handle;

import com.botong.workorder.annotation.TimeoutDays;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.Arrays;

/**
 * 超时警告阈值校验注解处理器
 * 实现TimeoutDays注解的校验逻辑，验证值是否在允许的范围内
 * <AUTHOR> by zhb on 2023/9/15.
 */

public class TimeoutDaysValidator implements ConstraintValidator<TimeoutDays, Integer> {
    private int[] allowedValues;

    /**
     * 初始化校验器，获取注解中配置的值
     * @param constraintAnnotation TimeoutDays注解实例
     */
    @Override
    public void initialize(TimeoutDays constraintAnnotation) {
        allowedValues = constraintAnnotation.values();
    }

    /**
     * 执行校验逻辑
     * @param value 待校验的值
     * @param context 校验上下文
     * @return true 如果值在允许范围内或为null，false 否则
     */
    @Override
    public boolean isValid(Integer value, ConstraintValidatorContext context) {
        if (value == null) {
            // 允许为空，可通过 @NotNull 注解实现
            return true;
        }
        return Arrays.stream(allowedValues).anyMatch(val -> val == value);
    }
}