package com.botong.workorder.annotation.handle;

import com.botong.workorder.annotation.TimeoutDays;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.Arrays;

/**
 * 超时警告阈值校验注解处理器
 * <AUTHOR> by zhb on 2023/9/15.
 */

public class TimeoutDaysValidator implements ConstraintValidator<TimeoutDays, Integer> {
    private int[] allowedValues;

    @Override
    public void initialize(TimeoutDays constraintAnnotation) {
        allowedValues = constraintAnnotation.values();
    }

    @Override
    public boolean isValid(Integer value, ConstraintValidatorContext context) {
        if (value == null) {
            // 允许为空，可通过 @NotNull 注解实现
            return true;
        }
        return Arrays.stream(allowedValues).anyMatch(val -> val == value);
    }
}