package com.botong.workorder.controller;

import cn.hutool.core.util.StrUtil;
import com.botong.framework.common.constant.BtoConstant;
import com.botong.framework.common.page.PageResult;
import com.botong.framework.common.utils.DateUtils;
import com.botong.framework.common.utils.Result;
import com.botong.framework.security.operatelog.annotations.OperateLog;
import com.botong.framework.security.operatelog.enums.OperateTypeEnum;
import com.botong.workorder.annotation.UpdateOvertime;
import com.botong.workorder.query.PlantElectricChargeAnalyzeQuery;
import com.botong.workorder.query.UserAlarmTypeStatisticsQuery;
import com.botong.workorder.query.WorkOrderStatisticQuery;
import com.botong.workorder.service.WorkOrderStatisticService;
import com.botong.workorder.vo.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 工单统计管理
 *
 * <AUTHOR>
 * @since 2023/8/18 17:24
 */
@RestController
@RequestMapping("statistics")
@Tag(name = "工单统计管理")
@AllArgsConstructor
public class WorkOrderStatisticController {
    private final WorkOrderStatisticService workOrderStatisticService;


    @GetMapping("getUserAlarmTypeStatistics")
    @Operation(summary = "用户工单运维报表")
    public Result<List<Map<String, Object>>> getUserAlarmTypeStatistics(@ParameterObject UserAlarmTypeStatisticsQuery query) {
        List<Map<String, Object>> statistics = workOrderStatisticService.getUserAlarmTypeStatistics(query);
        return Result.ok(statistics);
    }

    @GetMapping("exportUserAlarmTypeStatistics")
    @Operation(summary = "导出用户工单运维报表")
    @OperateLog(type = OperateTypeEnum.EXPORT)
    public Result<String> exportUserAlarmTypeStatistics(@ParameterObject UserAlarmTypeStatisticsQuery query, HttpServletResponse response) {
        workOrderStatisticService.exportUserAlarmTypeStatistics(query, response);
        return Result.ok();
    }

    @GetMapping("monthlyOpsWorkOrderSummary")
    @Operation(summary = "月度工单运维报表")
    public Result<List<Map<String, Object>>> monthlyOpsWorkOrderSummary(
            @RequestParam("date")
            @DateTimeFormat(pattern = DateUtils.DATE_MONTH_PATTERN)
            Date date
    ) {
        List<Map<String, Object>> statistics = workOrderStatisticService.monthlyOpsWorkOrderSummary(date);
        return Result.ok(statistics);
    }

    @GetMapping("exportMonthlyOpsWorkOrderSummary")
    @Operation(summary = "导出月度工单运维报表")
    @OperateLog(type = OperateTypeEnum.EXPORT)
    public Result<String> exportMonthlyOpsWorkOrderSummary(
            @RequestParam("date")
            @DateTimeFormat(pattern = DateUtils.DATE_MONTH_PATTERN)
            Date date,
            HttpServletResponse response
    ) {
        workOrderStatisticService.exportMonthlyOpsWorkOrderSummary(date, response);
        return Result.ok();
    }

    @GetMapping("statisticsByAlarmTime")
    @Operation(summary = "故障时间分布统计 按年查询")
    // @PreAuthorize("hasAuthority('order:statistics:alarmTime')")
    public Result<List<StatisticsByAlarmTimeVO>> statisticsByAlarmTime(
            @RequestParam(name = "year", defaultValue = "2024") String year,
            @RequestParam(name = "plantId", required = false) String plantId
    ) {
        List<StatisticsByAlarmTimeVO> statistics = workOrderStatisticService.statisticsByAlarmTime(year, plantId);
        return Result.ok(statistics);
    }

    @GetMapping("statisticPlantAndWorkOrder")
    @Operation(summary = "获取电站/工单统计信息")
    @ApiResponse(description = "plant->plantStatusStatistic->status: [电站状态（0：离线，1：正常运行，2：告警运行,3:自检提示,5:逆变器夜间离线）]离线->0，告警->2，正常->1+3")
    // @PreAuthorize(value = "hasAuthority('stats:plantNum:info')")
    public Result<Map<String, Object>> getPlantNumInfo(
            @RequestParam(value = "plantType", required = false) List<Long> plantType,
            @RequestParam(value = "startTime", required = false) String startTime,
            @RequestParam(value = "endTime", required = false) String endTime

    ) {
        Map<String, Object> result = workOrderStatisticService.statisticPlantAndWorkOrder(plantType, startTime, endTime);
        return Result.ok(result);
    }

    @GetMapping
    @Operation(summary = "按分类统计")
    @PreAuthorize("hasAuthority('order:statistics:type')")
    public Result<WorkOrderStatisticsVO<StatisticsVO>> statistics(
            @ParameterObject @Valid WorkOrderStatisticQuery query) {
        List<StatisticsVO> statistics = workOrderStatisticService.statistics(query);
        // 排除 全部
        statistics = statistics.stream().filter(item -> StrUtil.isNotBlank(item.getDictValue())).collect(Collectors.toList());
        Integer total = 0;
        for (StatisticsVO item : statistics) {
            total += item.getCount();
        }
        WorkOrderStatisticsVO<StatisticsVO> workOrderStatistics = new WorkOrderStatisticsVO<>(statistics, total);
        return Result.ok(workOrderStatistics);
    }

    @GetMapping({"regionTree"})
    @Operation(summary = "区域树")
    @PreAuthorize("hasAuthority('order:statistics:regionTree')")
    @UpdateOvertime
    public Result<List<RegionTree>> regionTree(@ParameterObject @Valid WorkOrderStatisticQuery query) {
        return Result.ok(workOrderStatisticService.regionTree(query));
    }

    @GetMapping({"regionCount"})
    @Operation(summary = "区域统计")
    @PreAuthorize("hasAuthority('order:statistics:regionCount')")
    public Result<WorkOrderStatisticsVO<RegionCount>> regionCount(
            @ParameterObject @Valid WorkOrderStatisticQuery query) {
        List<RegionCount> statistics = workOrderStatisticService.regionCount(query);
        Integer total = 0;
        for (RegionCount item : statistics) {
            if (StrUtil.isBlank(item.getDictLabel())) {
                item.setDictLabel(BtoConstant.UNKNOWN_REGION);
            }
            total += item.getCount();
        }
        WorkOrderStatisticsVO<RegionCount> workOrderStatistics = new WorkOrderStatisticsVO<>(statistics, total);
        return Result.ok(workOrderStatistics);
    }

    @GetMapping({"getElectricChargeAnalyze"})
    @Operation(summary = "根据月份获取城市电费电量分析")
    public Result<List<ElectricChargeAnalyzeVO>> getElectricChargeAnalyze(@Schema(description = "查询年月，格式：yyyy-MM") @RequestParam(value = "yearMonth", required = false) String yearMonth) {
        List<ElectricChargeAnalyzeVO> list = workOrderStatisticService.getElectricChargeAnalyze(yearMonth);
        return Result.ok(list);
    }

    @GetMapping({"getPlantElectricChargeAnalyze"})
    @Operation(summary = "根据月份获取电站电费电量分析")
    public Result<PageResult<ElectricChargeAnalyzeVO>> getPlantElectricChargeAnalyze(@ParameterObject PlantElectricChargeAnalyzeQuery query) {
        PageResult<ElectricChargeAnalyzeVO> list = workOrderStatisticService.getPlantElectricChargeAnalyze(query);
        return Result.ok(list);
    }


}
