package com.botong.workorder.controller;

import com.botong.framework.common.utils.Result;
import com.botong.framework.security.operatelog.annotations.OperateLog;
import com.botong.framework.security.operatelog.enums.OperateTypeEnum;
import com.botong.workorder.convert.WorkChargeConvert;
import com.botong.workorder.entity.WorkChargeEntity;
import com.botong.workorder.query.WorkChargeQuery;
import com.botong.workorder.service.PlantBaseInfoService;
import com.botong.workorder.service.WorkChargeService;
import com.botong.workorder.vo.WorkChargeVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;

/**
* 二次增值台账表
*
* <AUTHOR> 
* @since 1.0.0 2025-02-24
*/
@RestController
@RequestMapping("charge")
@Tag(name="二次增值台账表")
@AllArgsConstructor
public class WorkChargeController {
    private final WorkChargeService workChargeService;
    private final PlantBaseInfoService plantBaseInfoService;

    @GetMapping("page")
    @Operation(summary = "分页")
    // @PreAuthorize("hasAuthority('workorder:charge:page')")
    public Result<HashMap<String, Object>> page(@Valid WorkChargeQuery query){
        HashMap<String, Object> page = workChargeService.page(query);

        return Result.ok(page);
    }

    @GetMapping("{id}")
    @Operation(summary = "信息")
    // @PreAuthorize("hasAuthority('workorder:charge:info')")
    public Result<WorkChargeVO> get(@PathVariable("id") Long id){
        WorkChargeEntity entity = workChargeService.getById(id);
        return Result.ok(WorkChargeConvert.INSTANCE.convert(entity));
    }

    @PostMapping
    @Operation(summary = "保存")
    @OperateLog(type = OperateTypeEnum.INSERT)
    // @PreAuthorize("hasAuthority('workorder:charge:save')")
    public Result<String> save(@RequestBody WorkChargeVO vo){
        workChargeService.save(vo);

        return Result.ok();
    }

    @PutMapping
    @Operation(summary = "修改")
    @OperateLog(type = OperateTypeEnum.UPDATE)
    // @PreAuthorize("hasAuthority('workorder:charge:update')")
    public Result<String> update(@RequestBody @Valid WorkChargeVO vo){
        workChargeService.update(vo);

        return Result.ok();
    }

    @GetMapping("export")
    @Operation(summary = "导出账单")
    @OperateLog(type = OperateTypeEnum.EXPORT)
    // @PreAuthorize("hasAuthority('workorder:charge:export')")
    public void export() {
        workChargeService.export();
    }

    @PostMapping("import")
    @Operation(summary = "导入")
    @OperateLog(type = OperateTypeEnum.IMPORT)
    // @PreAuthorize("hasAuthority('workorder:charge:import')")
    public Result<String> importExcel(@RequestParam("file") MultipartFile file) {
        if (file.isEmpty()) {
            return Result.error("请选择需要上传的文件");
        }
        workChargeService.importByExcel(file);

        return Result.ok();
    }

    @DeleteMapping
    @Operation(summary = "删除")
    @OperateLog(type = OperateTypeEnum.DELETE)
    // @PreAuthorize("hasAuthority('workorder:charge:delete')")
    public Result<String> delete(@RequestBody List<Long> idList){
        workChargeService.delete(idList);

        return Result.ok();
    }
}