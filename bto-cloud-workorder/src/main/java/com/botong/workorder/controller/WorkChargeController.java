package com.botong.workorder.controller;

import com.botong.framework.common.utils.Result;
import com.botong.framework.security.operatelog.annotations.OperateLog;
import com.botong.framework.security.operatelog.enums.OperateTypeEnum;
import com.botong.workorder.convert.WorkChargeConvert;
import com.botong.workorder.entity.WorkChargeEntity;
import com.botong.workorder.query.WorkChargeQuery;
import com.botong.workorder.service.PlantBaseInfoService;
import com.botong.workorder.service.WorkChargeService;
import com.botong.workorder.vo.WorkChargeVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;

/**
 * 二次增值台账表控制器
 * 提供二次增值台账信息的增删改查、导入导出等功能
 *
 * <AUTHOR>
 * @since 1.0.0 2025-02-24
 */
@RestController
@RequestMapping("charge")
@Tag(name="二次增值台账表")
@AllArgsConstructor
public class WorkChargeController {
    private final WorkChargeService workChargeService;
    private final PlantBaseInfoService plantBaseInfoService;

    /**
     * 分页查询二次增值台账
     * @param query 查询条件
     * @return 台账分页结果
     */
    @GetMapping("page")
    @Operation(summary = "分页")
    // @PreAuthorize("hasAuthority('workorder:charge:page')")
    public Result<HashMap<String, Object>> page(@Valid WorkChargeQuery query){
        HashMap<String, Object> page = workChargeService.page(query);
        return Result.ok(page);
    }

    /**
     * 根据ID获取二次增值台账信息
     * @param id 台账ID
     * @return 台账详细信息
     */
    @GetMapping("{id}")
    @Operation(summary = "信息")
    // @PreAuthorize("hasAuthority('workorder:charge:info')")
    public Result<WorkChargeVO> get(@PathVariable("id") Long id){
        WorkChargeEntity entity = workChargeService.getById(id);
        return Result.ok(WorkChargeConvert.INSTANCE.convert(entity));
    }

    /**
     * 保存二次增值台账信息
     * @param vo 台账信息
     * @return 操作结果
     */
    @PostMapping
    @Operation(summary = "保存")
    @OperateLog(type = OperateTypeEnum.INSERT)
    // @PreAuthorize("hasAuthority('workorder:charge:save')")
    public Result<String> save(@RequestBody WorkChargeVO vo){
        workChargeService.save(vo);
        return Result.ok();
    }

    /**
     * 修改二次增值台账信息
     * @param vo 台账信息
     * @return 操作结果
     */
    @PutMapping
    @Operation(summary = "修改")
    @OperateLog(type = OperateTypeEnum.UPDATE)
    // @PreAuthorize("hasAuthority('workorder:charge:update')")
    public Result<String> update(@RequestBody @Valid WorkChargeVO vo){
        workChargeService.update(vo);
        return Result.ok();
    }

    /**
     * 导出账单
     */
    @GetMapping("export")
    @Operation(summary = "导出账单")
    @OperateLog(type = OperateTypeEnum.EXPORT)
    // @PreAuthorize("hasAuthority('workorder:charge:export')")
    public void export() {
        workChargeService.export();
    }

    /**
     * 导入二次增值台账
     * @param file Excel文件
     * @return 导入操作结果
     */
    @PostMapping("import")
    @Operation(summary = "导入")
    @OperateLog(type = OperateTypeEnum.IMPORT)
    // @PreAuthorize("hasAuthority('workorder:charge:import')")
    public Result<String> importExcel(@RequestParam("file") MultipartFile file) {
        if (file.isEmpty()) {
            return Result.error("请选择需要上传的文件");
        }
        workChargeService.importByExcel(file);
        return Result.ok();
    }

    /**
     * 批量删除二次增值台账
     * @param idList 台账ID列表
     * @return 操作结果
     */
    @DeleteMapping
    @Operation(summary = "删除")
    @OperateLog(type = OperateTypeEnum.DELETE)
    // @PreAuthorize("hasAuthority('workorder:charge:delete')")
    public Result<String> delete(@RequestBody List<Long> idList){
        workChargeService.delete(idList);
        return Result.ok();
    }
}