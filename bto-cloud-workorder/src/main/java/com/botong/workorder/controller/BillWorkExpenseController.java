package com.botong.workorder.controller;

import com.botong.framework.common.page.PageResult;
import com.botong.framework.common.utils.Result;
import com.botong.workorder.query.BillWorkExpenseQuery;
import com.botong.workorder.service.BillWorkExpenseService;
import com.botong.workorder.vo.BillWorkExpenseVO;
import com.botong.workorder.vo.PageWorkExpenseVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * 工单费用账单
 *
 * <AUTHOR>
 * @since 1.0.0 2025-01-14
 */
@RestController
@RequestMapping("expense")
@Tag(name = "工单费用账单")
@AllArgsConstructor
public class BillWorkExpenseController {

    private final BillWorkExpenseService billWorkExpenseService;

    @GetMapping("page")
    @Operation(summary = "分页")
    @PreAuthorize("hasAuthority('workorder:expense:page')")
    public Result<PageResult<PageWorkExpenseVO>> page(@Valid BillWorkExpenseQuery query) {
        PageResult<PageWorkExpenseVO> page = billWorkExpenseService.page(query);
        return Result.ok(page);
    }

    @GetMapping("getListByWorkId")
    @Operation(summary = "根据工单ID获取费用明细")
    @PreAuthorize("hasAuthority('workorder:expense:detail')")
    public Result<PageResult<BillWorkExpenseVO>> getList(@Valid BillWorkExpenseQuery query) {
        PageResult<BillWorkExpenseVO> list = billWorkExpenseService.getList(query);
        return Result.ok(list);
    }
}