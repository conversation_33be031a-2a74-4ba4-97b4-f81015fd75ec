package com.botong.workorder.controller;

import com.botong.api.module.system.StorageApi;
import com.botong.api.module.system.dto.StorageDTO;
import com.botong.framework.common.page.PageResult;
import com.botong.framework.common.utils.DateUtils;
import com.botong.framework.common.utils.Result;
import com.botong.framework.security.operatelog.annotations.OperateLog;
import com.botong.framework.security.operatelog.enums.OperateTypeEnum;
import com.botong.workorder.dto.BatchApplyDTO;
import com.botong.workorder.dto.WorkOperationInfoDTO;
import com.botong.workorder.dto.WorkOrderAssignDTO;
import com.botong.workorder.dto.WorkOrderExamineDTO;
import com.botong.workorder.query.WorkOrderProcessManageQuery;
import com.botong.workorder.service.WorkOrderProcessManageService;
import com.botong.workorder.vo.CityStatsStatistics;
import com.botong.workorder.vo.WorkOrderBaseDetailsVO;
import com.botong.workorder.vo.WorkOrderInfoVO;
import com.botong.workorder.vo.WorkOrderProcessManageVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.validator.constraints.Range;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * 工单流程管理控制器
 * 提供工单流程的全生命周期管理，包括工单录入、派遣、审核、状态变更等功能
 * 支持批量操作、Excel导入导出、挂起恢复等特殊业务场景
 *
 * <AUTHOR>
 * @since 2023-08-15
 */
@Slf4j
@RestController
@RequestMapping("flow")
@Tag(name = "工单流程管理")
@Validated
@AllArgsConstructor
public class WorkOrderProcessManageController {

    private final WorkOrderProcessManageService workOrderProcessManageService;
    private final StorageApi storageApi;

    /**
     * 文件上传
     * @param file 上传的文件
     * @return 文件存储信息
     * @throws Exception 上传异常
     */
    @PostMapping("upload")
    @Operation(summary = "文件上传")
    @OperateLog(type = OperateTypeEnum.OTHER)
    public Result<StorageDTO> upload(@RequestParam("file") MultipartFile file) throws Exception {
        return storageApi.upload(file);
    }

    /**
     * 光云工单录入
     * @param vo 工单信息
     * @return 操作结果
     */
    @PostMapping
    @Operation(summary = "光云工单录入")
    @PreAuthorize("hasAuthority('workorder:flow:apply')")
    @OperateLog(type = OperateTypeEnum.INSERT)
    public Result<String> workOrderApply(@RequestBody @Valid WorkOrderProcessManageVO vo) {
        workOrderProcessManageService.workOrderApply(vo);
        return Result.ok();
    }

    /**
     * 批量工单录入
     * @param batchApplyDTO 批量工单信息列表
     * @return 操作结果
     */
    @PostMapping("batchApply")
    @Operation(summary = "批量工单录入")
    @OperateLog(type = OperateTypeEnum.INSERT)
    // @PreAuthorize("hasAuthority('workorder:flow:batchApply')")
    public Result<String> workOrderBatchApply(@RequestBody @Valid List<BatchApplyDTO> batchApplyDTO) {
        workOrderProcessManageService.batchApply(batchApplyDTO);
        return Result.ok();
    }

    /**
     * excel导入工单
     * @param file Excel文件
     * @return 导入结果
     */
    @PostMapping("import")
    @Operation(summary = "excel导入工单")
    @OperateLog(type = OperateTypeEnum.IMPORT)
    @PreAuthorize("hasAuthority('workorder:flow:import')")
    public Result<String> importExcel(@RequestParam("file") MultipartFile file) {
        if (file.isEmpty()) {
            return Result.error("请选择需要上传的文件");
        }
        workOrderProcessManageService.importByExcel(file);
        return Result.ok();
    }

    /**
     * 工单派遣
     * @param assignDTO 工单派遣信息
     * @return 操作结果
     */
    @PostMapping("assign")
    @Operation(summary = "工单派遣")
    @OperateLog(type = OperateTypeEnum.INSERT)
    @PreAuthorize("hasAuthority('workorder:flow:assign')")
    public Result<String> workOrderAssign(@RequestBody @Valid WorkOrderAssignDTO assignDTO) {
        workOrderProcessManageService.workOrderAssign(assignDTO);
        return Result.ok();
    }


    /**
     * 修改工单状态
     * @param workIdList 工单ID列表
     * @param status 工单状态：0-待派单，1-待维修，2-待审核，3-已完成
     * @param suspendExpiration 挂起到期时间（可选）
     * @return 操作结果
     */
    @PutMapping("status")
    @Operation(summary = "修改工单状态")
    @OperateLog(type = OperateTypeEnum.UPDATE)
    @PreAuthorize("hasAuthority('workorder:flow:changeStatus')")
    public Result<String> changeStatus(
            @Parameter(description = "工单ID列表", required = true)
            @RequestBody @NotNull(message = "工单id不能为空") List<Long> workIdList,
            @Parameter(description = "0->待派单，1->待维修，2->待审核，3->已完成", required = true)
            @NotNull(message = "工单状态不能为空")
            @Range(min = 0, max = 4, message = "状态只能为0~3") Integer status,
            @Parameter(description = "挂起到期时间 "+DateUtils.DATE_TIME_PATTERN)
            @DateTimeFormat(pattern = DateUtils.DATE_TIME_PATTERN)
            @RequestParam(value = "suspendExpiration", required = false) Date suspendExpiration
    ) {
        workOrderProcessManageService.changeStatus(workIdList, status,suspendExpiration);
        return Result.ok();
    }

    /**
     * excel导出工单
     * @param query 查询条件
     */
    @GetMapping("export")
    @OperateLog(type = OperateTypeEnum.EXPORT)
    @Operation(summary = "excel导出工单")
    @PreAuthorize("hasAuthority('workorder:flow:export')")
    public void export(@ParameterObject WorkOrderProcessManageQuery query) {
        workOrderProcessManageService.export(query);
    }

    /**
     * 分页查询工单信息
     * @param query 查询条件
     * @return 工单分页结果
     */
    @GetMapping("page")
    @Operation(summary = "分页")
    @PreAuthorize("hasAuthority('workorder:flow:page')")
    public Result<PageResult<WorkOrderProcessManageVO>> page(@ParameterObject @Valid WorkOrderProcessManageQuery query) {
        PageResult<WorkOrderProcessManageVO> page = workOrderProcessManageService.page(query);
        return Result.ok(page);
    }

    /**
     * 批量删除工单
     * @param idList 工单ID列表
     * @return 操作结果
     */
    @DeleteMapping
    @Operation(summary = "删除")
    @OperateLog(type = OperateTypeEnum.DELETE)
    @PreAuthorize("hasAuthority('workorder:flow:delete')")
    public Result<Void> delete(@RequestBody List<Long> idList) {
        workOrderProcessManageService.delete(idList);
        return Result.ok();
    }

    /**
     * 修改工单信息
     * @param vo 工单信息
     * @return 操作结果
     * @throws Exception 更新异常
     */
    @PutMapping
    @Operation(summary = "修改")
    @OperateLog(type = OperateTypeEnum.UPDATE)
    @PreAuthorize("hasAuthority('workorder:flow:update')")
    public Result<String> update(@RequestBody @Valid WorkOrderProcessManageVO vo) throws Exception {
        workOrderProcessManageService.update(vo);
        return Result.ok();
    }

    /**
     * 工单审核
     * @param dto 审核信息
     * @return 操作结果
     */
    @PostMapping("review")
    @Operation(summary = "审核")
    @OperateLog(type = OperateTypeEnum.UPDATE)
    @PreAuthorize("hasAuthority('workorder:flow:review')")
    public Result<String> review(@RequestBody @Valid WorkOrderExamineDTO dto) {
        workOrderProcessManageService.review(dto);
        return Result.ok();
    }

    /**
     * 获取工单详情
     * @param workId 工单ID
     * @return 工单详细信息
     */
    @GetMapping("{workId}")
    @Operation(summary = "工单详情")
    @PreAuthorize("hasAuthority('workorder:flow:details')")
    public Result<WorkOrderInfoVO> details(@PathVariable("workId") Long workId) {
        return Result.ok(workOrderProcessManageService.details(workId));
    }

    /**
     * 获取超时工单分页数据
     * @param query 查询条件
     * @return 超时工单分页结果
     */
    @GetMapping("overtime")
    @Operation(summary = "超时工单分页")
    // @UpdateOvertime
    @PreAuthorize("hasAuthority('workorder:flow:overtime')")
    public Result<PageResult<WorkOrderProcessManageVO>> overtime(@ParameterObject @Valid WorkOrderProcessManageQuery query) {
        return Result.ok(workOrderProcessManageService.overtimePage(query));
    }

    /**
     * 根据项目类型和城市统计工单
     * @return 城市统计结果列表
     */
    @GetMapping("getCityStatsStatisticsByCity")
    @Operation(summary = "根据项目类型和城市统计工单")
    public Result<List<CityStatsStatistics>> getCityStatsStatisticsByCity() {
        return Result.ok(workOrderProcessManageService.getCityStatsStatisticsByCity());
    }

    /**
     * excel导出城市监控数据
     */
    @GetMapping("exportCityStats")
    @Operation(summary = "excel导出城市监控")
    @OperateLog(type = OperateTypeEnum.EXPORT)
    // @PreAuthorize("hasAuthority('workorder:flow:exportCityStats')")
    public void exportCityStats() {
        workOrderProcessManageService.exportCityStats();
    }

    /**
     * 保存并提交工单
     * @param dto 工单信息
     * @return 操作结果
     */
    @PostMapping("saveAndSubmit")
    @Operation(summary = "保存并提交")
    @OperateLog(type = OperateTypeEnum.INSERT)
    @PreAuthorize("hasAuthority('workorder:flow:saveAndSubmit')")
    public Result<String> saveAndSubmit(@Valid @RequestBody WorkOperationInfoDTO dto) {
        workOrderProcessManageService.saveAndSubmit(dto);
        return Result.ok();
    }

    /**
     * 获取基础工单详情信息
     * @param workId 工单ID
     * @return 基础工单详情
     */
    @GetMapping("details")
    @Operation(summary = "获取基础工单详情信息")
    @PreAuthorize("hasAuthority('workorder:flow:base')")
    public Result<WorkOrderBaseDetailsVO> baseDetails(Long workId) {
        return Result.ok(workOrderProcessManageService.baseDetails(workId));
    }


    /**
     * 取消挂起状态
     * @return 操作结果
     */
    @GetMapping("unsuspend")
    @Operation(summary = "取消挂起")
    @OperateLog(type = OperateTypeEnum.UPDATE)
    public Result<String> unsuspend() {
        workOrderProcessManageService.unsuspend();
        return Result.ok();
    }

}