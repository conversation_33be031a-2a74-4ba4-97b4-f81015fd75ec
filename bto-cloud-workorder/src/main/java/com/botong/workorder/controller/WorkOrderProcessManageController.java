package com.botong.workorder.controller;

import com.botong.api.module.system.StorageApi;
import com.botong.api.module.system.dto.StorageDTO;
import com.botong.framework.common.page.PageResult;
import com.botong.framework.common.utils.DateUtils;
import com.botong.framework.common.utils.Result;
import com.botong.framework.security.operatelog.annotations.OperateLog;
import com.botong.framework.security.operatelog.enums.OperateTypeEnum;
import com.botong.workorder.dto.BatchApplyDTO;
import com.botong.workorder.dto.WorkOperationInfoDTO;
import com.botong.workorder.dto.WorkOrderAssignDTO;
import com.botong.workorder.dto.WorkOrderExamineDTO;
import com.botong.workorder.query.WorkOrderProcessManageQuery;
import com.botong.workorder.service.WorkOrderProcessManageService;
import com.botong.workorder.vo.CityStatsStatistics;
import com.botong.workorder.vo.WorkOrderBaseDetailsVO;
import com.botong.workorder.vo.WorkOrderInfoVO;
import com.botong.workorder.vo.WorkOrderProcessManageVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.validator.constraints.Range;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * 工单流程管理
 *
 * <AUTHOR> by zhb on 2023/8/15.
 */
@Slf4j
@RestController
@RequestMapping("flow")
@Tag(name = "工单流程管理")
@Validated
@AllArgsConstructor
public class WorkOrderProcessManageController {

    private final WorkOrderProcessManageService workOrderProcessManageService;
    private final StorageApi storageApi;

    @PostMapping("upload")
    @Operation(summary = "文件上传")
    @OperateLog(type = OperateTypeEnum.OTHER)
    public Result<StorageDTO> upload(@RequestParam("file") MultipartFile file) throws Exception {

        return storageApi.upload(file);
    }

    @PostMapping
    @Operation(summary = "光云工单录入")
    @PreAuthorize("hasAuthority('workorder:flow:apply')")
    @OperateLog(type = OperateTypeEnum.INSERT)
    public Result<String> workOrderApply(@RequestBody @Valid WorkOrderProcessManageVO vo) {
        workOrderProcessManageService.workOrderApply(vo);
        return Result.ok();
    }

    @PostMapping("batchApply")
    @Operation(summary = "批量工单录入")
    @OperateLog(type = OperateTypeEnum.INSERT)
    // @PreAuthorize("hasAuthority('workorder:flow:batchApply')")
    public Result<String> workOrderBatchApply(@RequestBody @Valid List<BatchApplyDTO> batchApplyDTO) {
        workOrderProcessManageService.batchApply(batchApplyDTO);
        return Result.ok();
    }

    @PostMapping("import")
    @Operation(summary = "excel导入工单")
    @OperateLog(type = OperateTypeEnum.IMPORT)
    @PreAuthorize("hasAuthority('workorder:flow:import')")
    public Result<String> importExcel(@RequestParam("file") MultipartFile file) {
        if (file.isEmpty()) {
            return Result.error("请选择需要上传的文件");
        }
        workOrderProcessManageService.importByExcel(file);

        return Result.ok();
    }

    @PostMapping("assign")
    @Operation(summary = "工单派遣")
    @OperateLog(type = OperateTypeEnum.INSERT)
    @PreAuthorize("hasAuthority('workorder:flow:assign')")
    public Result<String> workOrderAssign(@RequestBody @Valid WorkOrderAssignDTO assignDTO) {
        workOrderProcessManageService.workOrderAssign(assignDTO);
        return Result.ok();

    }


    @PutMapping("status")
    @Operation(summary = "修改工单状态")
    @OperateLog(type = OperateTypeEnum.UPDATE)
    @PreAuthorize("hasAuthority('workorder:flow:changeStatus')")
    public Result<String> changeStatus(
            @Parameter(description = "工单ID列表", required = true)
            @RequestBody @NotNull(message = "工单id不能为空") List<Long> workIdList,
            @Parameter(description = "0->待派单，1->待维修，2->待审核，3->已完成", required = true)
            @NotNull(message = "工单状态不能为空")
            @Range(min = 0, max = 4, message = "状态只能为0~3") Integer status,
            @Parameter(description = "挂起到期时间 "+DateUtils.DATE_TIME_PATTERN)
            @DateTimeFormat(pattern = DateUtils.DATE_TIME_PATTERN)
            @RequestParam(value = "suspendExpiration", required = false) Date suspendExpiration
    ) {
        workOrderProcessManageService.changeStatus(workIdList, status,suspendExpiration);
        return Result.ok();
    }

    @GetMapping("export")
    @OperateLog(type = OperateTypeEnum.EXPORT)
    @Operation(summary = "excel导出工单")
    @PreAuthorize("hasAuthority('workorder:flow:export')")
    public void export(@ParameterObject WorkOrderProcessManageQuery query) {
        workOrderProcessManageService.export(query);
    }

    @GetMapping("page")
    @Operation(summary = "分页")
    @PreAuthorize("hasAuthority('workorder:flow:page')")
    public Result<PageResult<WorkOrderProcessManageVO>> page(@ParameterObject @Valid WorkOrderProcessManageQuery query) {
        PageResult<WorkOrderProcessManageVO> page = workOrderProcessManageService.page(query);

        return Result.ok(page);
    }

    @DeleteMapping
    @Operation(summary = "删除")
    @OperateLog(type = OperateTypeEnum.DELETE)
    @PreAuthorize("hasAuthority('workorder:flow:delete')")
    public Result<Void> delete(@RequestBody List<Long> idList) {
        workOrderProcessManageService.delete(idList);

        return Result.ok();
    }

    @PutMapping
    @Operation(summary = "修改")
    @OperateLog(type = OperateTypeEnum.UPDATE)
    @PreAuthorize("hasAuthority('workorder:flow:update')")
    public Result<String> update(@RequestBody @Valid WorkOrderProcessManageVO vo) throws Exception {
        workOrderProcessManageService.update(vo);

        return Result.ok();
    }

    @PostMapping("review")
    @Operation(summary = "审核")
    @OperateLog(type = OperateTypeEnum.UPDATE)
    @PreAuthorize("hasAuthority('workorder:flow:review')")
    public Result<String> review(@RequestBody @Valid WorkOrderExamineDTO dto) {
        workOrderProcessManageService.review(dto);
        return Result.ok();
    }

    @GetMapping("{workId}")
    @Operation(summary = "工单详情")
    @PreAuthorize("hasAuthority('workorder:flow:details')")
    public Result<WorkOrderInfoVO> details(@PathVariable("workId") Long workId) {

        return Result.ok(workOrderProcessManageService.details(workId));
    }

    @GetMapping("overtime")
    @Operation(summary = "超时工单分页")
    // @UpdateOvertime
    @PreAuthorize("hasAuthority('workorder:flow:overtime')")
    public Result<PageResult<WorkOrderProcessManageVO>> overtime(@ParameterObject @Valid WorkOrderProcessManageQuery query) {

        return Result.ok(workOrderProcessManageService.overtimePage(query));
    }

    @GetMapping("getCityStatsStatisticsByCity")
    @Operation(summary = "根据项目类型和城市统计工单")
    public Result<List<CityStatsStatistics>> getCityStatsStatisticsByCity() {
        return Result.ok(workOrderProcessManageService.getCityStatsStatisticsByCity());
    }

    @GetMapping("exportCityStats")
    @Operation(summary = "excel导出城市监控")
    @OperateLog(type = OperateTypeEnum.EXPORT)
    // @PreAuthorize("hasAuthority('workorder:flow:exportCityStats')")
    public void exportCityStats() {
        workOrderProcessManageService.exportCityStats();
    }

    @PostMapping("saveAndSubmit")
    @Operation(summary = "保存并提交")
    @OperateLog(type = OperateTypeEnum.INSERT)
    @PreAuthorize("hasAuthority('workorder:flow:saveAndSubmit')")
    public Result<String> saveAndSubmit(@Valid @RequestBody WorkOperationInfoDTO dto) {
        workOrderProcessManageService.saveAndSubmit(dto);
        return Result.ok();
    }

    @GetMapping("details")
    @Operation(summary = "获取基础工单详情信息")
    @PreAuthorize("hasAuthority('workorder:flow:base')")
    public Result<WorkOrderBaseDetailsVO> baseDetails(Long workId) {
        return Result.ok(workOrderProcessManageService.baseDetails(workId));

    }


    @GetMapping("unsuspend")
    @Operation(summary = "取消挂起")
    @OperateLog(type = OperateTypeEnum.UPDATE)
    public Result<String> unsuspend() {
        workOrderProcessManageService.unsuspend();
        return Result.ok();
    }

}