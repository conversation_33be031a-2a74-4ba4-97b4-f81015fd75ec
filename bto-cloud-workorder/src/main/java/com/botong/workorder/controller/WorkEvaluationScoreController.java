package com.botong.workorder.controller;

import com.botong.framework.common.utils.Result;
import com.botong.workorder.query.WorkEvaluationScoreQuery;
import com.botong.workorder.service.WorkEvaluationScoreService;
import com.botong.workorder.vo.WorkEvaluationFlatScoreVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 工作订单评估评分控制器
 * 提供工程师评估评分的查询和导出功能
 *
 * <AUTHOR>
 * @since 1.0.0 2025-07-04
 */
@RestController
@RequestMapping("evaluation-score")
@Tag(name = "工作订单评估评分")
@AllArgsConstructor
public class WorkEvaluationScoreController {
    
    private final WorkEvaluationScoreService workEvaluationScoreService;
    
    /**
     * 获取工程师评估评分（扁平化结构）
     * @param query 查询条件，包含评估时间范围、工程师等筛选条件
     * @return 工程师评估评分扁平化数据列表
     */
    @GetMapping("engineer-scores-flat")
    @Operation(summary = "获取工程师评估评分（扁平化结构）")
    public Result<List<Map<String, Object>>> getEngineerScoresFlat(@Valid WorkEvaluationScoreQuery query) {
        WorkEvaluationFlatScoreVO result = workEvaluationScoreService.getFlatEvaluationScores(query);
        return Result.ok(result.getEngineerScores());
    }

    /**
     * 导出工程师评估评分（扁平化结构）到Excel
     * @param query 查询条件，包含评估时间范围、工程师等筛选条件
     *              设置为不分页以导出所有匹配数据
     */
    @GetMapping("engineer-scores-flat/export")
    @Operation(summary = "导出工程师评估评分（扁平化结构）到Excel")
    public void exportEngineerScoresFlat(@Valid WorkEvaluationScoreQuery query) {
        // 设置分页参数以检索所有记录，不受分页限制
        query.setPage(-1);
        query.setLimit(-1);
        workEvaluationScoreService.exportFlatEvaluationScores(query);
    }
}
