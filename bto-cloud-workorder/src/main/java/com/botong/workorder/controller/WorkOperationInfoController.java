package com.botong.workorder.controller;

import com.botong.framework.common.utils.Result;
import com.botong.framework.security.operatelog.annotations.OperateLog;
import com.botong.framework.security.operatelog.enums.OperateTypeEnum;
import com.botong.workorder.dto.WorkOperationInfoDTO;
import com.botong.workorder.service.WorkOperationInfoService;
import com.botong.workorder.vo.WorkOperationInfoVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 工单维修信息管理控制器
 * 提供工单维修信息的查询、保存、修改等功能
 * 包含超时时间更新等特殊业务接口
 *
 * <AUTHOR>
 * @since 1.0.0 2023-08-22
 */
@RestController
@RequestMapping("operation")
@Tag(name = "工单维修信息管理")
@AllArgsConstructor
@Validated
public class WorkOperationInfoController {
    private final WorkOperationInfoService workOperationInfoService;

    /**
     * 根据ID获取工单维修信息详情
     * @param id 工单ID
     * @return 工单维修详细信息
     */
    @GetMapping("{id}")
    @Operation(summary = "信息")
    @PreAuthorize("hasAuthority('workorder:operation:info')")
    public Result<WorkOperationInfoVO> get(@NotNull(message = "id不能为空") @PathVariable("id") Long id) {
        return Result.ok(workOperationInfoService.info(id));
    }

    /**
     * 保存报价与维修信息
     * @param dto 报价与维修信息DTO
     * @return 操作结果
     */
    @PostMapping
    @Operation(summary = "保存报价与维修信息")
    @OperateLog(type = OperateTypeEnum.INSERT)
    @PreAuthorize("hasAuthority('workorder:operation:save')")
    public Result<String> updateBatch(@Valid @RequestBody WorkOperationInfoDTO dto) {
        workOperationInfoService.updateBatch(dto);
        return Result.ok();
    }

    /**
     * 批量更新工单超时时间
     * @param workIdList 工单ID列表
     * @return 操作结果
     */
    @PutMapping("updateOvertime")
    @Operation(summary = "更新超时时间")
    @OperateLog(type = OperateTypeEnum.UPDATE)
    @PreAuthorize("hasAuthority('workorder:operation:overtime')")
    public Result<String> updateOvertime(@RequestBody List<Long> workIdList) {
        workOperationInfoService.updateOvertime(workIdList);
        return Result.ok();
    }
}