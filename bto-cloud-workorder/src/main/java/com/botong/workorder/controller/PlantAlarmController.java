package com.botong.workorder.controller;

import com.botong.api.module.photovoltaic.vo.PlantAlarmVO;
import com.botong.framework.common.page.PageResult;
import com.botong.framework.common.utils.Result;
import com.botong.workorder.convert.PlantAlarmConvert;
import com.botong.workorder.entity.PlantAlarmEntity;
import com.botong.workorder.query.PlantAlarmQuery;
import com.botong.workorder.service.PlantAlarmService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * 电站告警
 *
 * <AUTHOR>
 * @since 2025-01-02
 */
@RestController
@RequestMapping("alarm")
@Tag(name = "电站告警")
@AllArgsConstructor
public class PlantAlarmController {
    private final PlantAlarmService plantAlarmService;

    @GetMapping("page")
    @Operation(summary = "分页")
    // @PreAuthorize("hasAuthority('plant:alarm:page')")
    public Result<PageResult<PlantAlarmVO>> page(@Valid PlantAlarmQuery query) {
        PageResult<PlantAlarmVO> page = plantAlarmService.page(query);

        return Result.ok(page);
    }

    @GetMapping("{id}")
    @Operation(summary = "信息")
    // @PreAuthorize("hasAuthority('plant:alarm:info')")
    public Result<PlantAlarmVO> get(@PathVariable("id") String id) {
        PlantAlarmEntity entity = plantAlarmService.getById(id);

        return Result.ok(PlantAlarmConvert.INSTANCE.convert(entity));
    }
}