package com.botong.workorder.controller;

import com.botong.api.module.photovoltaic.vo.PlantAlarmVO;
import com.botong.api.module.photovoltaic.vo.ProjectTypeTreeVO;
import com.botong.framework.common.page.PageResult;
import com.botong.framework.common.utils.Result;
import com.botong.framework.security.operatelog.annotations.OperateLog;
import com.botong.framework.security.operatelog.enums.OperateTypeEnum;
import com.botong.workorder.query.LowPowerQuery;
import com.botong.workorder.query.PlantAlarmQuery;
import com.botong.workorder.query.PlantBaseInfoQuery;
import com.botong.workorder.service.PlantBaseInfoService;
import com.botong.workorder.vo.LowPowerPlantVO;
import com.botong.workorder.vo.PlantBaseInfoVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.hibernate.validator.constraints.Range;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 电站管理
 *
 * <AUTHOR> by zhb on 2023/8/16.
 */
@RestController
@RequestMapping("plant")
@Tag(name = "电站管理")
@AllArgsConstructor
@Validated
public class PlantBaseInfoController {
    private final PlantBaseInfoService plantBaseInfoService;

    @GetMapping("page")
    @Operation(summary = "分页")
    @PreAuthorize("hasAuthority('plant:base:page')")
    public Result<PageResult<PlantBaseInfoVO>> page(@ParameterObject @Valid PlantBaseInfoQuery query) {
        PageResult<PlantBaseInfoVO> page = plantBaseInfoService.page(query);

        return Result.ok(page);
    }

    @GetMapping("idsNames")
    @Operation(summary = "获取所有电站id和电站名称")
    @PreAuthorize("hasAuthority('plant:base:idsNames')")
    public Result<Map<String, List<Map<String, Object>>>> getPlantIdsAndNames(@Parameter(description = "电站平台 0->光云 1->其他") @Valid Integer platform) {
        Map<String, List<Map<String, Object>>> ids = plantBaseInfoService.getPlantIdsAndNames(platform);

        return Result.ok(ids);
    }

    @GetMapping("/info")
    @Operation(summary = "根据电站id或名称获取电站信息")
    @PreAuthorize("hasAuthority('plant:base:info')")
    public Result<PlantBaseInfoVO> getInfoByIdOrName(@RequestParam("param") @NotBlank(message = "电站id或名称不能为空") String param,
                                                     @RequestParam("platform") @Range(min = 0, max = 1, message = "平台类型不正确") Integer platform) {
        PlantBaseInfoVO plantBaseInfoVo = plantBaseInfoService.getInfoByIdOrName(param, platform);

        return Result.ok(plantBaseInfoVo);
    }

    @GetMapping("/likeInfo")
    @Operation(summary = "根据电站id或名称模糊查询电站信息")
    @PreAuthorize("hasAuthority('plant:base:likeInfo')")
    public Result<PageResult<PlantBaseInfoVO>> getInfoLikeIdOrName(@ParameterObject @Valid PlantBaseInfoQuery query) {


        PageResult<PlantBaseInfoVO> pageResult = plantBaseInfoService.getInfoLikeIdOrName(query);

        return Result.ok(pageResult);
    }

    @PostMapping
    @Operation(summary = "保存")
    @PreAuthorize("hasAuthority('plant:base:save')")
    @OperateLog(type = OperateTypeEnum.INSERT)
    public Result<String> save(@RequestBody PlantBaseInfoVO vo) {
        plantBaseInfoService.save(vo);

        return Result.ok();
    }

    @PutMapping
    @Operation(summary = "修改")
    @PreAuthorize("hasAuthority('plant:base:update')")
    @OperateLog(type = OperateTypeEnum.UPDATE)
    public Result<String> update(@RequestBody @Valid PlantBaseInfoVO vo) {
        plantBaseInfoService.update(vo);
        return Result.ok();
    }

    @DeleteMapping
    @Operation(summary = "删除")
    @PreAuthorize("hasAuthority('plant:base:delete')")
    @OperateLog(type = OperateTypeEnum.DELETE)
    public Result<String> delete(@RequestBody List<Long> idList) {
        plantBaseInfoService.delete(idList);

        return Result.ok();
    }

    @GetMapping("alarmList")
    @Operation(summary = "获取光伏系统未处理告警数据")
    @PreAuthorize("hasAuthority('plant:base:alarmList')")
    public Result<PageResult<PlantAlarmVO>> alarmList(@ParameterObject PlantAlarmQuery query) {
        PageResult<PlantAlarmVO> result = plantBaseInfoService.requestAlarmList(query);
        return Result.ok(result);
    }

    @GetMapping("projectTree")
    @Operation(summary = "获取光伏项目类型数据")
    @PreAuthorize("hasAuthority('plant:base:projectTree')")
    public Result<List<ProjectTypeTreeVO>> getProjectTypeTree() {
        List<ProjectTypeTreeVO> result = plantBaseInfoService.getProjectTypeTree();
        return Result.ok(result);
    }


    @GetMapping("diagram")
    @Operation(summary = "电器、电站图")
    // @PreAuthorize("hasAuthority('plant:base:likeInfo')")
    public Result<HashMap<String, Object>> getPlantDiagram(@RequestParam("plantId") String plantId,
                                                           @RequestParam("plantType") Integer plantType) {
        HashMap<String, Object> result = plantBaseInfoService.getPlantDiagram(plantId, plantType);
        return Result.ok(result);
    }


    @GetMapping("getLowPowerPlant")
    @Operation(summary = "低电量电站")
    public Result<PageResult<LowPowerPlantVO>> getLowPowerPlant(@Valid @ParameterObject LowPowerQuery query) {
        return Result.ok(plantBaseInfoService.getLowPowerPlant(query));
    }

    @GetMapping("exportLowPowerPlant")
    @Operation(summary = "导出低电量电站")
    @OperateLog(type = OperateTypeEnum.EXPORT)
    public Result<String> exportLowPowerPlant(@Valid @ParameterObject LowPowerQuery query) {
        plantBaseInfoService.exportLowPowerPlant(query);
        return Result.ok();
    }
}