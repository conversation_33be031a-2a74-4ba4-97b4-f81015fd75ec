package com.botong.workorder.controller;

import com.botong.framework.common.page.PageResult;
import com.botong.framework.common.utils.Result;
import com.botong.framework.security.operatelog.annotations.OperateLog;
import com.botong.framework.security.operatelog.enums.OperateTypeEnum;
import com.botong.workorder.convert.WorkToolInventoryConvert;
import com.botong.workorder.entity.WorkToolInventoryEntity;
import com.botong.workorder.query.WorkToolInventoryQuery;
import com.botong.workorder.service.WorkToolInventoryService;
import com.botong.workorder.vo.WorkToolInventoryVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 运维工具库控制器
 * 提供运维工具信息的分页查询、增删改查、导出等功能
 *
 * <AUTHOR>
 * @since 1.0.0 2025-03-11
 */
@RestController
@RequestMapping("inventory")
@Tag(name="运维工具库")
@AllArgsConstructor
public class WorkToolInventoryController {
    private final WorkToolInventoryService workToolInventoryService;

    /**
     * 分页查询运维工具信息
     * @param query 查询条件，包含工具名称、类型、状态等筛选条件
     * @return 工具分页结果
     */
    @GetMapping("page")
    @Operation(summary = "分页")
    // @PreAuthorize("hasAuthority('workorder:inventory:page')")
    public Result<PageResult<WorkToolInventoryVO>> page(@Valid WorkToolInventoryQuery query){
        PageResult<WorkToolInventoryVO> page = workToolInventoryService.page(query);

        return Result.ok(page);
    }

    /**
     * 导出运维工具信息到Excel
     * @param query 查询条件，用于导出符合条件的工具数据
     */
    @GetMapping("export")
    @Operation(summary = "导出")
    @OperateLog(type = OperateTypeEnum.EXPORT)
    // @PreAuthorize("hasAuthority('workorder:inventory:export')")
    public void export(@ParameterObject WorkToolInventoryQuery query){
        workToolInventoryService.export(query);
    }

    /**
     * 根据ID获取工具详细信息
     * @param id 工具ID
     * @return 工具详细信息
     */
    @GetMapping("{id}")
    @Operation(summary = "信息")
    // @PreAuthorize("hasAuthority('workorder:inventory:info')")
    public Result<WorkToolInventoryVO> get(@PathVariable("id") Long id){
        WorkToolInventoryEntity entity = workToolInventoryService.getById(id);

        return Result.ok(WorkToolInventoryConvert.INSTANCE.convert(entity));
    }

    /**
     * 保存运维工具信息
     * @param vo 工具信息，包含工具名称、型号、数量、状态等
     * @return 操作结果
     */
    @PostMapping
    @Operation(summary = "保存")
    @OperateLog(type = OperateTypeEnum.INSERT)
    // @PreAuthorize("hasAuthority('workorder:inventory:save')")
    public Result<String> save(@RequestBody WorkToolInventoryVO vo){
        workToolInventoryService.save(vo);

        return Result.ok();
    }

    /**
     * 修改运维工具信息
     * @param vo 工具信息，包含需要更新的工具属性
     * @return 操作结果
     */
    @PutMapping
    @Operation(summary = "修改")
    @OperateLog(type = OperateTypeEnum.UPDATE)
    // @PreAuthorize("hasAuthority('workorder:inventory:update')")
    public Result<String> update(@RequestBody @Valid WorkToolInventoryVO vo){
        workToolInventoryService.update(vo);

        return Result.ok();
    }

    /**
     * 批量删除运维工具信息
     * @param idList 工具ID列表
     * @return 操作结果
     */
    @DeleteMapping
    @Operation(summary = "删除")
    // @PreAuthorize("hasAuthority('workorder:inventory:delete')")
    @OperateLog(type = OperateTypeEnum.DELETE)
    public Result<String> delete(@RequestBody List<Long> idList){
        workToolInventoryService.delete(idList);

        return Result.ok();
    }
}