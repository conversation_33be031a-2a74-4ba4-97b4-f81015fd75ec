package com.botong.workorder.controller;

import com.botong.framework.common.page.PageResult;
import com.botong.framework.common.utils.Result;
import com.botong.framework.security.operatelog.annotations.OperateLog;
import com.botong.framework.security.operatelog.enums.OperateTypeEnum;
import com.botong.workorder.convert.WorkToolInventoryConvert;
import com.botong.workorder.entity.WorkToolInventoryEntity;
import com.botong.workorder.query.WorkToolInventoryQuery;
import com.botong.workorder.service.WorkToolInventoryService;
import com.botong.workorder.vo.WorkToolInventoryVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
* 运维工具库
*
* <AUTHOR> 
* @since 1.0.0 2025-03-11
*/
@RestController
@RequestMapping("inventory")
@Tag(name="运维工具库")
@AllArgsConstructor
public class WorkToolInventoryController {
    private final WorkToolInventoryService workToolInventoryService;

    @GetMapping("page")
    @Operation(summary = "分页")
    // @PreAuthorize("hasAuthority('workorder:inventory:page')")
    public Result<PageResult<WorkToolInventoryVO>> page(@Valid WorkToolInventoryQuery query){
        PageResult<WorkToolInventoryVO> page = workToolInventoryService.page(query);

        return Result.ok(page);
    }

    @GetMapping("export")
    @Operation(summary = "导出")
    @OperateLog(type = OperateTypeEnum.EXPORT)
    // @PreAuthorize("hasAuthority('workorder:inventory:export')")
    public void export(@ParameterObject WorkToolInventoryQuery query){
        workToolInventoryService.export(query);
    }

    @GetMapping("{id}")
    @Operation(summary = "信息")
    // @PreAuthorize("hasAuthority('workorder:inventory:info')")
    public Result<WorkToolInventoryVO> get(@PathVariable("id") Long id){
        WorkToolInventoryEntity entity = workToolInventoryService.getById(id);

        return Result.ok(WorkToolInventoryConvert.INSTANCE.convert(entity));
    }

    @PostMapping
    @Operation(summary = "保存")
    @OperateLog(type = OperateTypeEnum.INSERT)
    // @PreAuthorize("hasAuthority('workorder:inventory:save')")
    public Result<String> save(@RequestBody WorkToolInventoryVO vo){
        workToolInventoryService.save(vo);

        return Result.ok();
    }

    @PutMapping
    @Operation(summary = "修改")
    @OperateLog(type = OperateTypeEnum.UPDATE)
    // @PreAuthorize("hasAuthority('workorder:inventory:update')")
    public Result<String> update(@RequestBody @Valid WorkToolInventoryVO vo){
        workToolInventoryService.update(vo);

        return Result.ok();
    }

    @DeleteMapping
    @Operation(summary = "删除")
    // @PreAuthorize("hasAuthority('workorder:inventory:delete')")
    @OperateLog(type = OperateTypeEnum.DELETE)
    public Result<String> delete(@RequestBody List<Long> idList){
        workToolInventoryService.delete(idList);

        return Result.ok();
    }
}