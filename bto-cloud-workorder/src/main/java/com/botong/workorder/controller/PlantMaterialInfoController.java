package com.botong.workorder.controller;

import com.botong.framework.common.constant.BtoConstant;
import com.botong.framework.common.page.PageResult;
import com.botong.framework.common.utils.Result;
import com.botong.framework.security.operatelog.annotations.OperateLog;
import com.botong.framework.security.operatelog.enums.OperateTypeEnum;
import com.botong.workorder.convert.PlantMaterialInfoConvert;
import com.botong.workorder.entity.PlantMaterialInfoEntity;
import com.botong.workorder.query.PlantMaterialInfoQuery;
import com.botong.workorder.service.PlantMaterialInfoService;
import com.botong.workorder.vo.PlantMaterialInfoTree;
import com.botong.workorder.vo.PlantMaterialInfoVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 电站材料管理控制器
 * 提供电站材料信息的增删改查、树形结构展示等功能
 *
 * <AUTHOR>
 * @since 2023-08-23
 */
@RestController
@RequestMapping("material")
@Tag(name = "电站材料管理")
@AllArgsConstructor
public class PlantMaterialInfoController {
    private final PlantMaterialInfoService plantMaterialInfoService;

    /**
     * 分页查询电站材料信息
     * @param query 查询条件
     * @return 材料信息分页结果（树形结构）
     */
    @GetMapping("page")
    @Operation(summary = "分页")
    @PreAuthorize("hasAuthority('plant:material:page')")
    public Result<PageResult<PlantMaterialInfoTree>> page(@Valid PlantMaterialInfoQuery query) {
        PageResult<PlantMaterialInfoTree> page = plantMaterialInfoService.page(query);
        return Result.ok(page);
    }

    /**
     * 根据ID获取电站材料信息详情
     * @param id 材料ID
     * @return 材料详细信息，包含上级机构名称
     */
    @GetMapping("{id}")
    @Operation(summary = "信息")
    @PreAuthorize("hasAuthority('plant:material:info')")
    public Result<PlantMaterialInfoVO> get(@PathVariable("id") Long id) {
        PlantMaterialInfoEntity entity = plantMaterialInfoService.getById(id);
        if (entity == null) {
            return Result.error("找不到指定的材料信息");
        }
        PlantMaterialInfoVO vo = PlantMaterialInfoConvert.INSTANCE.convert(entity);

        // 获取上级机构名称
        if(!BtoConstant.ROOT.equals(entity.getPid())){
            PlantMaterialInfoEntity parent = plantMaterialInfoService.getById(entity.getPid());
            vo.setParentName(parent.getName());
        }

        return Result.ok(vo);
    }

    /**
     * 新增电站材料信息
     * @param vo 材料信息
     * @return 操作结果
     */
    @PostMapping
    @Operation(summary = "新增")
    @PreAuthorize("hasAuthority('plant:material:add')")
    @OperateLog(type = OperateTypeEnum.INSERT)
    public Result<String> add(@RequestBody @Valid PlantMaterialInfoVO vo) {
        plantMaterialInfoService.save(vo);
        return Result.ok();
    }

    /**
     * 修改电站材料信息
     * @param vo 材料信息
     * @return 操作结果
     */
    @PutMapping
    @Operation(summary = "修改")
    @PreAuthorize("hasAuthority('plant:material:update')")
    @OperateLog(type = OperateTypeEnum.UPDATE)
    public Result<String> update(@RequestBody @Valid PlantMaterialInfoVO vo) {
        plantMaterialInfoService.update(vo);
        return Result.ok();
    }

    /**
     * 批量删除电站材料信息
     * @param idList 材料ID列表
     * @return 操作结果
     */
    @DeleteMapping
    @Operation(summary = "删除")
    @PreAuthorize("hasAuthority('plant:material:delete')")
    @OperateLog(type = OperateTypeEnum.DELETE)
    public Result<String> delete(@RequestBody List<Long> idList) {
        plantMaterialInfoService.delete(idList);
        return Result.ok();
    }

    /**
     * 获取电站材料列表（树形结构）
     * @return 材料树形结构列表
     */
    @GetMapping("list")
    @Operation(summary = "列表")
    @PreAuthorize("hasAuthority('plant:material:list')")
    public Result<List<PlantMaterialInfoTree>> list() {
        return Result.ok(plantMaterialInfoService.materialList());
    }
}