package com.botong.workorder.controller;

import com.botong.framework.common.page.PageResult;
import com.botong.framework.common.utils.Result;
import com.botong.workorder.convert.DeviceConvert;
import com.botong.workorder.entity.DeviceEntity;
import com.botong.workorder.query.DeviceQuery;
import com.botong.workorder.service.DeviceService;
import com.botong.workorder.vo.DeviceVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * 设备控制器
 * 提供设备信息的分页查询和详情查看功能
 *
 * <AUTHOR>
 * @since 2025-01-03
 */
@RestController
@RequestMapping("device")
@Tag(name = "设备")
@AllArgsConstructor
public class DeviceController {
    private final DeviceService deviceService;

    /**
     * 分页查询设备信息
     * @param query 查询条件
     * @return 设备分页结果
     */
    @GetMapping("page")
    @Operation(summary = "分页")
    // @PreAuthorize("hasAuthority('device:manage:page')")
    public Result<PageResult<DeviceVO>> page(@Valid DeviceQuery query) {
        PageResult<DeviceVO> page = deviceService.page(query);
        return Result.ok(page);
    }

    /**
     * 根据ID获取设备详情
     * @param id 设备ID
     * @return 设备详细信息
     */
    @GetMapping("{id}")
    @Operation(summary = "信息")
    // @PreAuthorize("hasAuthority('device:manage:info')")
    public Result<DeviceVO> get(@PathVariable("id") Long id) {
        DeviceEntity entity = deviceService.getById(id);
        return Result.ok(DeviceConvert.INSTANCE.convert(entity));
    }
}