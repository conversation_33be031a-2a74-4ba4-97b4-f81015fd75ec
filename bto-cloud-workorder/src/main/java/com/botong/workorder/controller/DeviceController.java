package com.botong.workorder.controller;

import com.botong.framework.common.page.PageResult;
import com.botong.framework.common.utils.Result;
import com.botong.workorder.convert.DeviceConvert;
import com.botong.workorder.entity.DeviceEntity;
import com.botong.workorder.query.DeviceQuery;
import com.botong.workorder.service.DeviceService;
import com.botong.workorder.vo.DeviceVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * 设备
 *
 * <AUTHOR>
 * @since 2025-01-03
 */
@RestController
@RequestMapping("device")
@Tag(name = "设备")
@AllArgsConstructor
public class DeviceController {
    private final DeviceService deviceService;

    @GetMapping("page")
    @Operation(summary = "分页")
    // @PreAuthorize("hasAuthority('device:manage:page')")
    public Result<PageResult<DeviceVO>> page(@Valid DeviceQuery query) {
        PageResult<DeviceVO> page = deviceService.page(query);

        return Result.ok(page);
    }

    @GetMapping("{id}")
    @Operation(summary = "信息")
    // @PreAuthorize("hasAuthority('device:manage:info')")
    public Result<DeviceVO> get(@PathVariable("id") Long id) {
        DeviceEntity entity = deviceService.getById(id);

        return Result.ok(DeviceConvert.INSTANCE.convert(entity));
    }
}