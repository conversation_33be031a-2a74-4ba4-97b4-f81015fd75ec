package com.botong.workorder.convert;

import com.botong.workorder.dto.BatchApplyDTO;
import com.botong.workorder.vo.WorkOrderProcessManageVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 批量申请转换器
 * 提供BatchApplyDTO与WorkOrderProcessManageVO之间的转换功能
 * 支持单个对象和列表的转换
 *
 * <AUTHOR> by zhb on 2024/11/1.
 */
@Mapper
public interface BatchApplyConvert {

    BatchApplyConvert INSTANCE = Mappers.getMapper(BatchApplyConvert.class);

    WorkOrderProcessManageVO convert(BatchApplyDTO entity);

    List<WorkOrderProcessManageVO> convertList(List<BatchApplyDTO> list);

}
