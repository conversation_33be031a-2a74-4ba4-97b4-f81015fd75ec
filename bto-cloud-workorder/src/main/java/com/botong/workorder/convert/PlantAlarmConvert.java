package com.botong.workorder.convert;

import com.botong.api.module.photovoltaic.vo.PlantAlarmVO;
import com.botong.workorder.entity.PlantAlarmEntity;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
* 告警视图
*
* <AUTHOR> 
* @since 1.0.0 2024-01-24
*/
@Mapper
public interface PlantAlarmConvert {
    PlantAlarmConvert INSTANCE = Mappers.getMapper(PlantAlarmConvert.class);

    PlantAlarmEntity convert(PlantAlarmVO vo);

    @Mapping(source="id", target="alarmId")
    PlantAlarmVO convert(PlantAlarmEntity entity);

    List<PlantAlarmVO> convertList(List<PlantAlarmEntity> list);

}