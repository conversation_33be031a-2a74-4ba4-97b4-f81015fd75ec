package com.botong.workorder.convert;

import com.botong.workorder.entity.WorkOperationInfoEntity;
import com.botong.workorder.vo.WorkOperationInfoVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 工单运维信息转换器
 * 负责在工单运维信息实体（{@link WorkOperationInfoEntity}）与视图对象（{@link WorkOperationInfoVO}）之间进行双向转换。
 * 该转换器处理工单运维执行相关的信息，包括运维计划、执行状态、进度跟踪等，支持单个对象和批量集合的转换操作。
 *
 * <AUTHOR> 
 * @since 1.0.0 2023-08-22
 */
@Mapper
public interface WorkOperationInfoConvert {
    WorkOperationInfoConvert INSTANCE = Mappers.getMapper(WorkOperationInfoConvert.class);

    WorkOperationInfoEntity convert(WorkOperationInfoVO vo);
    WorkOperationInfoVO convert(WorkOperationInfoEntity entity);

    List<WorkOperationInfoVO> convertList(List<WorkOperationInfoEntity> list);

}