package com.botong.workorder.convert;

import com.botong.workorder.entity.WorkOperationInfoEntity;
import com.botong.workorder.vo.WorkOperationInfoVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
* 工单待运维表
*
* <AUTHOR> 
* @since 1.0.0 2023-08-22
*/
@Mapper
public interface WorkOperationInfoConvert {
    WorkOperationInfoConvert INSTANCE = Mappers.getMapper(WorkOperationInfoConvert.class);

    WorkOperationInfoEntity convert(WorkOperationInfoVO vo);
    WorkOperationInfoVO convert(WorkOperationInfoEntity entity);

    List<WorkOperationInfoVO> convertList(List<WorkOperationInfoEntity> list);

}