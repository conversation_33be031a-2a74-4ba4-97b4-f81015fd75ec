package com.botong.workorder.convert;

import com.botong.workorder.entity.DeviceEntity;
import com.botong.workorder.vo.DeviceVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
* 设备
*
* <AUTHOR> 
* @since  2025-01-03
*/
@Mapper
public interface DeviceConvert {
    DeviceConvert INSTANCE = Mappers.getMapper(DeviceConvert.class);

    DeviceEntity convert(DeviceVO vo);

    DeviceVO convert(DeviceEntity entity);

    List<DeviceVO> convertList(List<DeviceEntity> list);

}