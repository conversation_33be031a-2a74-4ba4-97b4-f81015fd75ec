package com.botong.workorder.convert;

import com.botong.workorder.entity.BillWorkExpenseEntity;
import com.botong.workorder.vo.BillWorkExpenseVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 工单费用账单转换器
 * 提供BillWorkExpenseEntity与BillWorkExpenseVO之间的双向转换功能
 * 支持单个对象和列表的转换
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-01-14
 */
@Mapper
public interface BillWorkExpenseConvert {
    BillWorkExpenseConvert INSTANCE = Mappers.getMapper(BillWorkExpenseConvert.class);

    BillWorkExpenseEntity convert(BillWorkExpenseVO vo);

    BillWorkExpenseVO convert(BillWorkExpenseEntity entity);

    List<BillWorkExpenseVO> convertList(List<BillWorkExpenseEntity> list);

    List<BillWorkExpenseEntity> convertEntityList(List<BillWorkExpenseVO> list);

}