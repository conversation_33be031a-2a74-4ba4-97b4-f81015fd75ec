package com.botong.workorder.convert;

import com.botong.workorder.entity.PlantMaterialInfoEntity;
import com.botong.workorder.vo.PlantMaterialInfoVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 电站材料信息转换器
 * 提供PlantMaterialInfoEntity与PlantMaterialInfoVO之间的双向转换功能
 * 支持单个对象和列表的转换
 *
 * <AUTHOR> 
 * @since  2023-08-31
 */
@Mapper
public interface PlantMaterialInfoConvert {
    PlantMaterialInfoConvert INSTANCE = Mappers.getMapper(PlantMaterialInfoConvert.class);

    PlantMaterialInfoEntity convert(PlantMaterialInfoVO vo);
    // PlantMaterialInfoEntity convert(PlantMaterialInfoTree tree);

    PlantMaterialInfoVO convert(PlantMaterialInfoEntity entity);

    List<PlantMaterialInfoVO> convertList(List<PlantMaterialInfoEntity> list);

}