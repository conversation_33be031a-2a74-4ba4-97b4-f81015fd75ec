package com.botong.workorder.convert;

import com.botong.workorder.entity.WorkEvaluatCoefficientEntity;
import com.botong.workorder.vo.WorkEvaluatCoefficientVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
* 工单考核系数表
*
* <AUTHOR> 
* @since 1.0.0 2025-07-03
*/
@Mapper
public interface WorkEvaluatCoefficientConvert {
    WorkEvaluatCoefficientConvert INSTANCE = Mappers.getMapper(WorkEvaluatCoefficientConvert.class);

    WorkEvaluatCoefficientEntity convert(WorkEvaluatCoefficientVO vo);

    WorkEvaluatCoefficientVO convert(WorkEvaluatCoefficientEntity entity);

    List<WorkEvaluatCoefficientVO> convertList(List<WorkEvaluatCoefficientEntity> list);

}