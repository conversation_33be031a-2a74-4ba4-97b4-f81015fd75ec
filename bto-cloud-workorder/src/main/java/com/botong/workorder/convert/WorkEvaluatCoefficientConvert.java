package com.botong.workorder.convert;

import com.botong.workorder.entity.WorkEvaluatCoefficientEntity;
import com.botong.workorder.vo.WorkEvaluatCoefficientVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 工单考核系数转换器
 * 负责在工单考核系数实体（{@link WorkEvaluatCoefficientEntity}）与视图对象（{@link WorkEvaluatCoefficientVO}）之间进行双向转换。
 * 该转换器处理工单绩效考核相关的系数信息，包括考核指标、权重系数、评分规则等，支持单个对象和批量集合的转换操作。
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-07-03
 */
@Mapper
public interface WorkEvaluatCoefficientConvert {
    WorkEvaluatCoefficientConvert INSTANCE = Mappers.getMapper(WorkEvaluatCoefficientConvert.class);

    WorkEvaluatCoefficientEntity convert(WorkEvaluatCoefficientVO vo);

    WorkEvaluatCoefficientVO convert(WorkEvaluatCoefficientEntity entity);

    List<WorkEvaluatCoefficientVO> convertList(List<WorkEvaluatCoefficientEntity> list);

}