package com.botong.workorder.convert;

import com.botong.workorder.entity.WorkOperationPersonInfoEntity;
import com.botong.workorder.vo.WorkOperationPersonInfoVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 工单运维人员信息转换器
 * 负责在工单运维人员信息实体（{@link WorkOperationPersonInfoEntity}）与视图对象（{@link WorkOperationPersonInfoVO}）之间进行双向转换。
 * 该转换器处理工单运维人员关联信息，包括人员分配、角色权限、执行责任等，支持单个对象和批量集合的转换操作。
 *
 * <AUTHOR> 
 * @since 1.0.0 2023-08-21
 */
@Mapper
public interface WorkOperationPersonInfoConvert {
    WorkOperationPersonInfoConvert INSTANCE = Mappers.getMapper(WorkOperationPersonInfoConvert.class);

    WorkOperationPersonInfoEntity convert(WorkOperationPersonInfoVO vo);

    WorkOperationPersonInfoVO convert(WorkOperationPersonInfoEntity entity);

    List<WorkOperationPersonInfoVO> convertList(List<WorkOperationPersonInfoEntity> list);

}