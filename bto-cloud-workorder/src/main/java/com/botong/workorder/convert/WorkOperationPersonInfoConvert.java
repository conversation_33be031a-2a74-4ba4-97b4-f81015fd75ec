package com.botong.workorder.convert;

import com.botong.workorder.entity.WorkOperationPersonInfoEntity;
import com.botong.workorder.vo.WorkOperationPersonInfoVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
* 工单运维人员关联表
*
* <AUTHOR> 
* @since 1.0.0 2023-08-21
*/
@Mapper
public interface WorkOperationPersonInfoConvert {
    WorkOperationPersonInfoConvert INSTANCE = Mappers.getMapper(WorkOperationPersonInfoConvert.class);

    WorkOperationPersonInfoEntity convert(WorkOperationPersonInfoVO vo);

    WorkOperationPersonInfoVO convert(WorkOperationPersonInfoEntity entity);

    List<WorkOperationPersonInfoVO> convertList(List<WorkOperationPersonInfoEntity> list);

}