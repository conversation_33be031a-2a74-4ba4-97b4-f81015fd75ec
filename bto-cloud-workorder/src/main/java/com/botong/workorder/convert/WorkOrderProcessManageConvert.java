package com.botong.workorder.convert;

import com.botong.workorder.entity.PlantBaseInfoEntity;
import com.botong.workorder.entity.WorkApplyInfoEntity;
import com.botong.workorder.entity.WorkBaseInfoEntity;
import com.botong.workorder.entity.WorkOrderProcessManageViewEntity;
import com.botong.workorder.vo.PlantBaseInfoVO;
import com.botong.workorder.vo.WorkOrderApplyExcelVO;
import com.botong.workorder.vo.WorkOrderBaseDetailsVO;
import com.botong.workorder.vo.WorkOrderProcessManageVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR> by zhb on 2023/8/16.
 */

@Mapper
public interface WorkOrderProcessManageConvert {

    WorkOrderProcessManageConvert INSTANCE = Mappers.getMapper(WorkOrderProcessManageConvert.class);

    WorkApplyInfoEntity convertToWorkApplyInfoEntity(WorkOrderProcessManageVO workOrderProcessManageVO);
    @Mapping(source = "workId", target = "id")
    WorkBaseInfoEntity convertToWorkBaseInfoEntity(WorkOrderProcessManageVO workOrderProcessManageVO);

    List<WorkOrderProcessManageVO> convertToWorkOrderProcessManageVos(List<WorkOrderApplyExcelVO> workOrderApplyExcelVoS);

    WorkOrderProcessManageVO convert(WorkOrderApplyExcelVO workOrderApplyExcelVO);

    List<WorkOrderApplyExcelVO> convertToWorkOrderApplyExcelVos(List<WorkOrderProcessManageViewEntity> workOrderProcessManageViewEntityList);

    List<WorkOrderProcessManageVO> convertList(List<WorkOrderProcessManageViewEntity> list);
    @Mapping(source = "orgId", target = "plantType")
    WorkOrderBaseDetailsVO convertToWorkOrderBaseDetailsVO(WorkOrderProcessManageViewEntity workOrderProcessManageViewEntity);
    @Mapping(source = "orgId", target = "plantType")
    WorkOrderProcessManageVO workOrderProcessManageViewEntityToWorkOrderProcessManageVO(WorkOrderProcessManageViewEntity workOrderProcessManageViewEntity);

    PlantBaseInfoVO convertToPlantBaseInfoVO(WorkOrderProcessManageVO workOrderProcessManageVO);

    @Mapping(target = "id",source = "plantId")
    PlantBaseInfoEntity convertToPlantBaseInfoEntity(WorkOrderProcessManageVO workOrderProcessManageVO);

}
