package com.botong.workorder.convert;

import com.botong.workorder.entity.WorkToolInventoryEntity;
import com.botong.workorder.vo.WorkToolInventoryVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
* 运维工具库
*
* <AUTHOR> 
* @since 1.0.0 2025-03-11
*/
@Mapper
public interface WorkToolInventoryConvert {
    WorkToolInventoryConvert INSTANCE = Mappers.getMapper(WorkToolInventoryConvert.class);

    WorkToolInventoryEntity convert(WorkToolInventoryVO vo);

    WorkToolInventoryVO convert(WorkToolInventoryEntity entity);

    List<WorkToolInventoryVO> convertList(List<WorkToolInventoryEntity> list);

}