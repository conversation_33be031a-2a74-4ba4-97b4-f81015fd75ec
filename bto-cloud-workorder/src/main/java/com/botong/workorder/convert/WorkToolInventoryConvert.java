package com.botong.workorder.convert;

import com.botong.workorder.entity.WorkToolInventoryEntity;
import com.botong.workorder.vo.WorkToolInventoryVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 运维工具库存转换器
 * 负责在运维工具库存实体（{@link WorkToolInventoryEntity}）与视图对象（{@link WorkToolInventoryVO}）之间进行双向转换。
 * 该转换器处理运维工具相关的库存信息，包括工具基本信息、库存状态、使用记录等，支持单个对象和批量集合的转换操作。
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-03-11
 */
@Mapper
public interface WorkToolInventoryConvert {
    WorkToolInventoryConvert INSTANCE = Mappers.getMapper(WorkToolInventoryConvert.class);

    WorkToolInventoryEntity convert(WorkToolInventoryVO vo);

    WorkToolInventoryVO convert(WorkToolInventoryEntity entity);

    List<WorkToolInventoryVO> convertList(List<WorkToolInventoryEntity> list);

}