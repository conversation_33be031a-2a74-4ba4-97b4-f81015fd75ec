package  com.botong.workorder.convert;

import com.botong.workorder.entity.WorkBaseInfoEntity;
import com.botong.workorder.vo.WorkBaseInfoVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 工单基础信息转换器
 * 负责在工单基础信息实体（{@link WorkBaseInfoEntity}）与视图对象（{@link WorkBaseInfoVO}）之间进行双向转换。
 * 该转换器处理工单的核心基础数据，包括工单基本信息、状态信息、关联信息等，支持单个对象和批量集合的转换操作。
 *
 * <AUTHOR> 
 * @since 2023-08-10
 */
@Mapper
public interface WorkBaseInfoConvert {
    WorkBaseInfoConvert INSTANCE = Mappers.getMapper(WorkBaseInfoConvert.class);

    /**
     * VO转换实体
     *
     * @param vo vo
     * @return {@link WorkBaseInfoEntity }
     * <AUTHOR>
     * @since 2023-08-14 11:01:30
     */
    WorkBaseInfoEntity convert(WorkBaseInfoVO vo);

    /**
     * 实体转换VO
     *
     * @param entity 实体
     * @return {@link WorkBaseInfoVO }
     * <AUTHOR>
     * @since 2023-08-14 11:01:32
     */
    WorkBaseInfoVO convert(WorkBaseInfoEntity entity);

    /**
     * 转换列表
     *
     * @param list 列表
     * @return {@link List }<{@link WorkBaseInfoVO }>
     * <AUTHOR>
     * @since 2023-08-14 11:01:35
     */
    List<WorkBaseInfoVO> convertList(List<WorkBaseInfoEntity> list);

}