package  com.botong.workorder.convert;

import com.botong.workorder.entity.WorkBaseInfoEntity;
import com.botong.workorder.vo.WorkBaseInfoVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
* 工单基础明细表
*
* <AUTHOR> 
* @since  2023-08-10
*/
@Mapper
public interface WorkBaseInfoConvert {
    WorkBaseInfoConvert INSTANCE = Mappers.getMapper(WorkBaseInfoConvert.class);

    /**
     * VO转换实体
     *
     * @param vo vo
     * @return {@link WorkBaseInfoEntity }
     * <AUTHOR>
     * @since 2023-08-14 11:01:30
     */
    WorkBaseInfoEntity convert(WorkBaseInfoVO vo);

    /**
     * 实体转换VO
     *
     * @param entity 实体
     * @return {@link WorkBaseInfoVO }
     * <AUTHOR>
     * @since 2023-08-14 11:01:32
     */
    WorkBaseInfoVO convert(WorkBaseInfoEntity entity);

    /**
     * 转换列表
     *
     * @param list 列表
     * @return {@link List }<{@link WorkBaseInfoVO }>
     * <AUTHOR>
     * @since 2023-08-14 11:01:35
     */
    List<WorkBaseInfoVO> convertList(List<WorkBaseInfoEntity> list);

}