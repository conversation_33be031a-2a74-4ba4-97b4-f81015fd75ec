package com.botong.workorder.convert;

import com.botong.workorder.entity.WorkExamineInfoEntity;
import com.botong.workorder.vo.WorkExamineInfoVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 工单审核信息转换器
 * 负责在工单审核信息实体（{@link WorkExamineInfoEntity}）与视图对象（{@link WorkExamineInfoVO}）之间进行双向转换。
 * 该转换器处理工单审核流程相关的信息，包括审核记录、审核意见、审核状态等，支持单个对象和批量集合的转换操作。
 *
 * <AUTHOR> 
 * @since 1.0.0 2023-09-04
 */
@Mapper
public interface WorkExamineInfoConvert {
    WorkExamineInfoConvert INSTANCE = Mappers.getMapper(WorkExamineInfoConvert.class);

    WorkExamineInfoEntity convert(WorkExamineInfoVO vo);

    WorkExamineInfoVO convert(WorkExamineInfoEntity entity);

    List<WorkExamineInfoVO> convertList(List<WorkExamineInfoEntity> list);

}