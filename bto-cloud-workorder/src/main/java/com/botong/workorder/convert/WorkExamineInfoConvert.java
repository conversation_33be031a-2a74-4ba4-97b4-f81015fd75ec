package com.botong.workorder.convert;

import com.botong.workorder.entity.WorkExamineInfoEntity;
import com.botong.workorder.vo.WorkExamineInfoVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
* 工单审核管理
*
* <AUTHOR> 
* @since 1.0.0 2023-09-04
*/
@Mapper
public interface WorkExamineInfoConvert {
    WorkExamineInfoConvert INSTANCE = Mappers.getMapper(WorkExamineInfoConvert.class);

    WorkExamineInfoEntity convert(WorkExamineInfoVO vo);

    WorkExamineInfoVO convert(WorkExamineInfoEntity entity);

    List<WorkExamineInfoVO> convertList(List<WorkExamineInfoEntity> list);

}