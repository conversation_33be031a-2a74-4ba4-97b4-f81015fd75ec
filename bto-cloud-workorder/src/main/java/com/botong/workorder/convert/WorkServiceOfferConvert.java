package com.botong.workorder.convert;

import com.botong.workorder.entity.WorkServiceOfferEntity;
import com.botong.workorder.vo.WorkServiceOfferVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
* 工单服务报价表
*
* <AUTHOR> 
* @since 1.0.0 2023-08-23
*/
@Mapper
public interface WorkServiceOfferConvert {
    WorkServiceOfferConvert INSTANCE = Mappers.getMapper(WorkServiceOfferConvert.class);

    WorkServiceOfferEntity convert(WorkServiceOfferVO vo);

    WorkServiceOfferVO convert(WorkServiceOfferEntity entity);

    List<WorkServiceOfferVO> convertList(List<WorkServiceOfferEntity> list);

}