package com.botong.workorder.convert;

import com.botong.workorder.entity.WorkServiceOfferEntity;
import com.botong.workorder.vo.WorkServiceOfferVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 工单服务报价转换器
 * 负责在工单服务报价实体（{@link WorkServiceOfferEntity}）与视图对象（{@link WorkServiceOfferVO}）之间进行双向转换。
 * 该转换器处理工单服务相关的报价信息，包括服务项目、价格明细、报价策略等，支持单个对象和批量集合的转换操作。
 *
 * <AUTHOR> 
 * @since 1.0.0 2023-08-23
 */
@Mapper
public interface WorkServiceOfferConvert {
    WorkServiceOfferConvert INSTANCE = Mappers.getMapper(WorkServiceOfferConvert.class);

    WorkServiceOfferEntity convert(WorkServiceOfferVO vo);

    WorkServiceOfferVO convert(WorkServiceOfferEntity entity);

    List<WorkServiceOfferVO> convertList(List<WorkServiceOfferEntity> list);

}