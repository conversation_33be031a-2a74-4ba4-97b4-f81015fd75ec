package com.botong.workorder.convert;

import com.botong.workorder.entity.WorkApplyInfoEntity;
import com.botong.workorder.vo.WorkApplyInfoVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 工单申请信息转换器
 * 负责在工单申请信息实体（{@link WorkApplyInfoEntity}）与视图对象（{@link WorkApplyInfoVO}）之间进行双向转换。
 * 该转换器主要用于工单申请数据的展示、编辑和持久化操作，支持单个对象和批量集合的转换。
 *
 * <AUTHOR>
 * @since 2023/8/16
 */
@Mapper
public interface WorkApplyInfoConvert {
    WorkApplyInfoConvert INSTANCE = Mappers.getMapper(WorkApplyInfoConvert.class);

    WorkApplyInfoEntity convert(WorkApplyInfoVO vo);

    WorkApplyInfoVO convert(WorkApplyInfoEntity entity);

    List<WorkApplyInfoVO> convertList(List<WorkApplyInfoEntity> list);

}