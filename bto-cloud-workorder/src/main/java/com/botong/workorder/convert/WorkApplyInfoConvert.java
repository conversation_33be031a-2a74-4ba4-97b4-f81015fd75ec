package com.botong.workorder.convert;

import com.botong.workorder.entity.WorkApplyInfoEntity;
import com.botong.workorder.vo.WorkApplyInfoVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR> by zhb on 2023/8/16.
 */
@Mapper
public interface WorkApplyInfoConvert {
    WorkApplyInfoConvert INSTANCE = Mappers.getMapper(WorkApplyInfoConvert.class);

    WorkApplyInfoEntity convert(WorkApplyInfoVO vo);

    WorkApplyInfoVO convert(WorkApplyInfoEntity entity);

    List<WorkApplyInfoVO> convertList(List<WorkApplyInfoEntity> list);

}