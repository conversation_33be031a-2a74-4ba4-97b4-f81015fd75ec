package com.botong.workorder.convert;

import com.botong.workorder.entity.WorkChargeEntity;
import com.botong.workorder.vo.WorkChargeVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 工单增值费用转换器
 * 负责在工单增值费用实体（{@link WorkChargeEntity}）与视图对象（{@link WorkChargeVO}）之间进行双向转换。
 * 该转换器处理工单相关的增值服务费用信息，包括费用明细、计算规则、收费状态等，支持单个对象和批量集合的双向转换操作。
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-02-24
 */
@Mapper
public interface WorkChargeConvert {
    WorkChargeConvert INSTANCE = Mappers.getMapper(WorkChargeConvert.class);

    WorkChargeEntity convert(WorkChargeVO vo);

    WorkChargeVO convert(WorkChargeEntity entity);

    List<WorkChargeVO> convertList(List<WorkChargeEntity> list);

    List<WorkChargeEntity> convertEntitys(List<WorkChargeVO> entitys);

}