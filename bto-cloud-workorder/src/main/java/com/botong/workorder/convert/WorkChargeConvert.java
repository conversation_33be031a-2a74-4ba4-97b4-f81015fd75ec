package com.botong.workorder.convert;

import com.botong.workorder.entity.WorkChargeEntity;
import com.botong.workorder.vo.WorkChargeVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
* 二次增值台账表
*
* <AUTHOR> 
* @since 1.0.0 2025-02-24
*/
@Mapper
public interface WorkChargeConvert {
    WorkChargeConvert INSTANCE = Mappers.getMapper(WorkChargeConvert.class);

    WorkChargeEntity convert(WorkChargeVO vo);

    WorkChargeVO convert(WorkChargeEntity entity);

    List<WorkChargeVO> convertList(List<WorkChargeEntity> list);

    List<WorkChargeEntity> convertEntitys(List<WorkChargeVO> entitys);

}