package com.botong.workorder.convert;

import com.botong.api.module.photovoltaic.dto.InverterAlarmDTO;
import com.botong.api.module.photovoltaic.vo.PlantInfoVO;
import com.botong.framework.common.utils.BusinessCalculateUtil;
import com.botong.workorder.entity.PlantBaseInfoEntity;
import com.botong.workorder.query.PlantAlarmQuery;
import com.botong.workorder.vo.PlantBaseInfoVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.math.BigDecimal;
import java.util.List;

/**
 * 电站基础信息转换器
 * 提供PlantBaseInfoEntity与各种VO/DTO之间的转换功能
 * 支持复杂字段映射、表达式计算和列表转换
 *
 * <AUTHOR> by zhb on 2023/8/16.
 */
@Mapper
public interface PlantBaseInfoConvert {
    PlantBaseInfoConvert INSTANCE = Mappers.getMapper(PlantBaseInfoConvert.class);

    @Mapping(source = "plantId", target = "id")
    PlantBaseInfoEntity convert(PlantBaseInfoVO vo);

    @Mappings({
            @Mapping(target = "todayElectricity", expression = "java(convertRealElectricity(entity.getTodayElectricity()))"),
            @Mapping(target = "totalElectricity", expression = "java(convertRealElectricity(entity.getTotalElectricity()))"),
            @Mapping(target = "plantCapacity", expression = "java(convertRealPlantCapacity(entity.getPlantCapacity()))"),
            @Mapping(source = "id", target = "plantId")
    })
    PlantBaseInfoVO convert(PlantBaseInfoEntity entity);

    List<PlantBaseInfoVO> convertList(List<PlantBaseInfoEntity> list);
    List<PlantBaseInfoEntity> convertEntityList(List<PlantInfoVO> list);

    @Mappings({
            @Mapping(source = "plantUid", target = "id"),
            @Mapping(source = "inverterSn", target = "inverterId"),
            @Mapping(source = "userName", target = "plantUserName"),
            @Mapping(source = "plantType", target = "type"),
            @Mapping(source = "warrantyTime", target = "warrantyExpireDate"),
            @Mapping(target = "createTime", ignore = true),
            @Mapping(target = "updateTime", ignore = true)
    })
    PlantBaseInfoEntity convert(PlantInfoVO plantInfoVO);


    @Mappings({
            @Mapping(source = "page", target = "currentPage"),
            @Mapping(source = "limit", target = "pageSize"),
    })
    InverterAlarmDTO convert(PlantAlarmQuery plantAlarmQuery);


    default String convertRealElectricity(Integer electricity) {
        return BusinessCalculateUtil.getRealElectricity(electricity.toString());
    }

    default String convertRealPlantCapacity(Integer capacity) {
        return BusinessCalculateUtil.getRealPlantCapacity(capacity.toString());
    }
}