package com.botong.workorder.dao;

import com.botong.workorder.entity.WorkToolInventoryEntity;
import com.github.yulichang.base.MPJBaseMapper;
import org.apache.ibatis.annotations.Mapper;

/**
 * 运维工具库数据访问接口
 * 
 * 提供运维工具库存相关数据的持久化操作，包括工具库存的增删改查等基础功能
 * 用于运维工具的管理和库存跟踪
 * 
 * <AUTHOR>
 * @since 1.0.0 2025-03-11
 */
@Mapper
public interface WorkToolInventoryDao extends MPJBaseMapper<WorkToolInventoryEntity> {
	
}