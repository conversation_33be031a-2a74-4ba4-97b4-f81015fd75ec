package com.botong.workorder.dao;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.botong.framework.mybatis.dao.BaseDao;
import com.botong.workorder.entity.WorkChargeEntity;
import com.botong.workorder.query.WorkChargeQuery;
import com.botong.workorder.vo.ChargeStatisticsVO;
import com.botong.workorder.vo.WorkChargeVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 二次增值台账表数据访问接口
 * 
 * 提供工单二次增值相关数据的持久化操作，包括增值服务的查询、分页、
 * 统计等功能
 * 
 * <AUTHOR>
 * @since 1.0.0 2025-02-24
 */
@Mapper
public interface WorkChargeDao extends BaseDao<WorkChargeEntity> {

    /**
     * 分页查询二次增值台账信息
     * 
     * @param page 分页参数对象，包含分页信息和排序规则
     * @param query 查询条件对象，包含二次增值台账的筛选条件
     * @return 二次增值台账分页结果
     */
    IPage<WorkChargeEntity> page(@Param("page") IPage<WorkChargeEntity> page, @Param("query") WorkChargeQuery query);

    /**
     * 根据工单ID获取二次增值服务列表
     * 
     * @param workId 工单ID
     * @return 二次增值服务视图列表
     */
    List<WorkChargeVO> getList(@Param("workId") Long workId);

    /**
     * 获取二次增值统计信息
     * 
     * @param query 查询条件对象，用于统计筛选
     * @return 二次增值统计结果视图
     */
    ChargeStatisticsVO getChargeStatistics(@Param("query") WorkChargeQuery query);
}