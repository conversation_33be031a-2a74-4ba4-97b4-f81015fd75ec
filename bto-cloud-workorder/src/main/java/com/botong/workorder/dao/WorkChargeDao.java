package com.botong.workorder.dao;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.botong.framework.mybatis.dao.BaseDao;
import com.botong.workorder.entity.WorkChargeEntity;
import com.botong.workorder.query.WorkChargeQuery;
import com.botong.workorder.vo.ChargeStatisticsVO;
import com.botong.workorder.vo.WorkChargeVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* 二次增值台账表
*
* <AUTHOR> 
* @since 1.0.0 2025-02-24
*/
@Mapper
public interface WorkChargeDao extends BaseDao<WorkChargeEntity> {

    IPage<WorkChargeEntity> page(@Param("page") IPage<WorkChargeEntity> page, @Param("query") WorkChargeQuery query);

    List<WorkChargeVO> getList(@Param("workId") Long workId);

    ChargeStatisticsVO getChargeStatistics(@Param("query") WorkChargeQuery query);
}