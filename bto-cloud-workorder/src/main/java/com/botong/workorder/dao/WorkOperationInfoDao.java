package com.botong.workorder.dao;

import com.botong.workorder.entity.WorkOperationInfoEntity;
import com.github.yulichang.base.MPJBaseMapper;
import org.apache.ibatis.annotations.Mapper;

/**
 * 工单待运维表数据访问接口
 * 
 * 提供工单运维相关数据的持久化操作，包括待运维工单信息的增删改查等基础功能
 * 用于工单运维流程的管理和跟踪
 * 
 * <AUTHOR>
 * @since 1.0.0 2023-08-22
 */
@Mapper
public interface WorkOperationInfoDao extends MPJBaseMapper<WorkOperationInfoEntity> {
	
}