package com.botong.workorder.dao;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.botong.api.module.workorder.entity.OrgBaseEntity;
import com.botong.workorder.entity.PlantBaseInfoEntity;
import com.botong.workorder.query.LowPowerQuery;
import com.botong.workorder.vo.LowPowerPlantVO;
import com.botong.workorder.vo.SiteDiagramVO;
import com.github.yulichang.base.MPJBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.util.HashMap;
import java.util.List;

/**
 * 电站管理数据访问接口
 * 
 * 提供电站基础信息的持久化操作，包括电站项目查询、组织架构管理、
 * 电站类型获取、合同管理、电气图和现场图查询等功能
 * 
 * <AUTHOR>
 * @since 2023-08-16
 */
@Mapper
public interface PlantBaseInfoDao extends MPJBaseMapper<PlantBaseInfoEntity> {

    /**
     * 获取所有项目信息
     * 
     * @return 组织基础实体列表，包含所有项目信息
     */
    List<OrgBaseEntity> getAllProject();

    /**
     * 根据项目名称更新项目信息
     * 
     * @param oldName 原项目名称
     * @param newName 新项目名称
     */
    @Update("update sys_org set name = #{newName} where name = #{oldName}")
    void updateProjectByName(@Param("oldName") String oldName, @Param("newName") String newName);

    /**
     * 根据组织ID列表转换组织标识
     * 
     * @param orgIds 组织ID列表
     * @return 转换后的组织标识列表
     */
    List<String> transOrgId(@Param("orgIds") List<Long> orgIds);

    /**
     * 根据组织ID获取电站类型列表
     * 
     * @param params 查询参数映射，包含组织ID等查询条件
     * @return 电站类型列表
     */
    List<String> getPlantTypeByOrgId(HashMap<String, Object> params);

    /**
     * 根据电站ID获取合同ID
     * 
     * @param plantId 电站ID
     * @return 合同ID
     */
    String getContractIdByPlantId(@Param("plantId") String plantId);

    /**
     * 获取电气图信息
     * 
     * @param electricalTable 电气图表名称
     * @param contractId 合同ID
     * @return 电气图信息列表
     */
    List<String> getElectricalDiagram(@Param("electricalTable") String electricalTable, @Param("contractId") String contractId);

    /**
     * 获取现场图遥信信息
     * 
     * @param siteTable 现场图表名称
     * @param contractId 合同ID
     * @return 现场图遥信信息列表
     */
    List<String> getSiteDiagramYx(@Param("siteTable") String siteTable, @Param("contractId") String contractId);

    /**
     * 获取现场图信息
     * 
     * @param siteTable 现场图表名称
     * @param contractId 合同ID
     * @return 现场图信息视图列表
     */
    List<SiteDiagramVO> getSiteDiagram(@Param("siteTable") String siteTable, @Param("contractId") String contractId);

    /**
     * 根据电站类型获取组织ID列表
     * 
     * @param plantType 电站类型
     * @return 组织ID列表
     */
    List<String> getOrgIdByProjectIds(@Param("plantType") Long plantType);

    /**
     * 分页获取低电量电站信息（从库查询）
     * 
     * @param scope 作用域列表
     * @param orgIds 组织ID列表
     * @param page 分页参数对象
     * @param query 低电量查询条件对象
     * @return 低电量电站信息分页结果
     */
    @DS("slave")
    Page<LowPowerPlantVO> getLowPowerPlant(@Param("scope") List<String> scope, @Param("orgIds") List<String> orgIds, Page<LowPowerPlantVO> page, @Param("query") LowPowerQuery query);

    /**
     * 根据电站ID获取告警字符串
     * 
     * @param plantId 电站ID
     * @return 告警字符串
     */
    String getAlarmStr(@Param("plantId") String plantId);
}