package com.botong.workorder.dao;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.botong.framework.security.user.UserDetail;
import com.botong.workorder.query.PlantElectricChargeAnalyzeQuery;
import com.botong.workorder.query.WorkOrderStatisticQuery;
import com.botong.workorder.vo.ElectricChargeAnalyzeVO;
import com.botong.workorder.vo.RegionCount;
import com.botong.workorder.vo.RegionVO;
import com.botong.workorder.vo.StatisticsVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 工单统计管理
 * <AUTHOR>
 * @since 2023/8/28 15:12
 */
@Mapper
public interface WorkOrderStatisticDao {
    /**
     * 工单统计
     *
     * @param query      查询
     * @param dictTypeId dict类型id
     * @param user       使用者
     * @param workIdList 工作id列表
     * @return {@link List }<{@link StatisticsVO }>
     * <AUTHOR>
     * @since 2023-10-21 08:30:22
     */
    List<StatisticsVO> statistics(@Param("query") WorkOrderStatisticQuery query, @Param("dictTypeId") Long dictTypeId, @Param("user") UserDetail user, @Param("workIdList") List<Long> workIdList, @Param("scopeList") List<Long> scopeList);

    /**
     * 获取工单的电站地区
     *
     * @param workIdList 工单id列表
     * @param user       使用者
     * @return {@link List }<{@link RegionVO }>
     * <AUTHOR>
     * @since 2023-10-21 08:30:17
     */
    List<RegionVO> region(@Param("workIdList") List<Long> workIdList, @Param("user") UserDetail user);


    /**
     * 区域统计
     *
     * @param query      查询
     * @param type       类型
     * @param user       使用者
     * @param workIdList 工作id列表
     * @param scopeList
     * @return {@link List }<{@link RegionCount }>
     * <AUTHOR>
     * @since 2023-10-21 08:30:13
     */
    List<RegionCount> regionCount(@Param("query") WorkOrderStatisticQuery query, @Param("type") String type, @Param("user") UserDetail user, @Param("workIdList") List<Long> workIdList, List<Long> scopeList);

    List<ElectricChargeAnalyzeVO> getElectricChargeAnalyze(@Param("yearMonth") String yearMonth, @Param("monthField") String monthField);

    IPage<ElectricChargeAnalyzeVO> getPlantElectricChargeAnalyze(@Param("query") PlantElectricChargeAnalyzeQuery query, Page<ElectricChargeAnalyzeVO> page);
}
