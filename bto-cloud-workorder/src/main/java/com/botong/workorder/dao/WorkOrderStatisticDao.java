package com.botong.workorder.dao;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.botong.framework.security.user.UserDetail;
import com.botong.workorder.query.PlantElectricChargeAnalyzeQuery;
import com.botong.workorder.query.WorkOrderStatisticQuery;
import com.botong.workorder.vo.ElectricChargeAnalyzeVO;
import com.botong.workorder.vo.RegionCount;
import com.botong.workorder.vo.RegionVO;
import com.botong.workorder.vo.StatisticsVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 工单统计管理数据访问接口
 * 
 * 提供工单统计相关数据的持久化操作，包括工单统计分析、电站地区查询、
 * 区域统计、电费分析等功能
 * 
 * <AUTHOR>
 * @since 2023-08-28
 */
@Mapper
public interface WorkOrderStatisticDao {
    /**
     * 工单统计
     * 
     * @param query 工单统计查询条件对象
     * @param dictTypeId 字典类型ID
     * @param user 当前用户信息
     * @param workIdList 工单ID列表
     * @param scopeList 作用域列表
     * @return 统计结果视图列表
     */
    List<StatisticsVO> statistics(@Param("query") WorkOrderStatisticQuery query, @Param("dictTypeId") Long dictTypeId, @Param("user") UserDetail user, @Param("workIdList") List<Long> workIdList, @Param("scopeList") List<Long> scopeList);

    /**
     * 获取工单的电站地区信息
     * 
     * @param workIdList 工单ID列表
     * @param user 当前用户信息
     * @return 电站地区信息列表
     */
    List<RegionVO> region(@Param("workIdList") List<Long> workIdList, @Param("user") UserDetail user);

    /**
     * 区域统计
     * 
     * @param query 工单统计查询条件对象
     * @param type 统计类型
     * @param user 当前用户信息
     * @param workIdList 工单ID列表
     * @param scopeList 作用域列表
     * @return 区域统计结果列表
     */
    List<RegionCount> regionCount(@Param("query") WorkOrderStatisticQuery query, @Param("type") String type, @Param("user") UserDetail user, @Param("workIdList") List<Long> workIdList, List<Long> scopeList);

    /**
     * 获取电费分析数据
     * 
     * @param yearMonth 年月字符串
     * @param monthField 月份字段
     * @return 电费分析视图列表
     */
    List<ElectricChargeAnalyzeVO> getElectricChargeAnalyze(@Param("yearMonth") String yearMonth, @Param("monthField") String monthField);

    /**
     * 分页获取电站电费分析数据
     * 
     * @param query 电站电费分析查询条件对象
     * @param page 分页参数对象
     * @return 电费分析视图分页结果
     */
    IPage<ElectricChargeAnalyzeVO> getPlantElectricChargeAnalyze(@Param("query") PlantElectricChargeAnalyzeQuery query, Page<ElectricChargeAnalyzeVO> page);
}
