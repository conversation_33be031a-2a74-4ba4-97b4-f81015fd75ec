package com.botong.workorder.dao;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.botong.framework.mybatis.dao.BaseDao;
import com.botong.workorder.entity.BillWorkExpenseEntity;
import com.botong.workorder.query.BillWorkExpenseQuery;
import com.botong.workorder.vo.PageWorkExpenseVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 工单费用账单数据访问接口
 * 
 * 提供工单费用相关数据的持久化操作，包括费用账单的查询、分页等功能
 * 
 * <AUTHOR>
 * @since 1.0.0 2025-01-14
 */
@Mapper
public interface BillWorkExpenseDao extends BaseDao<BillWorkExpenseEntity> {

    /**
     * 分页查询工单费用账单
     * 
     * @param query 查询条件对象，包含费用账单的筛选条件
     * @param page 分页参数对象，包含分页信息和排序规则
     * @return 工单费用账单分页数据列表
     */
    List<PageWorkExpenseVO> page(@Param("query") BillWorkExpenseQuery query, @Param("page") Page<PageWorkExpenseVO> page);
}