package com.botong.workorder.dao;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.botong.framework.mybatis.dao.BaseDao;
import com.botong.workorder.entity.BillWorkExpenseEntity;
import com.botong.workorder.query.BillWorkExpenseQuery;
import com.botong.workorder.vo.PageWorkExpenseVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* 工单费用账单
*
* <AUTHOR> 
* @since 1.0.0 2025-01-14
*/
@Mapper
public interface BillWorkExpenseDao extends BaseDao<BillWorkExpenseEntity> {

    List<PageWorkExpenseVO> page(@Param("query") BillWorkExpenseQuery query, @Param("page") Page<PageWorkExpenseVO> page);
}