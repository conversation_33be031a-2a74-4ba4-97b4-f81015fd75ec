package com.botong.workorder.dao;

import com.botong.framework.mybatis.dao.BaseDao;
import com.botong.workorder.entity.WorkBaseInfoEntity;
import com.botong.workorder.vo.WorkOrderProcessManageVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 工单基础明细表数据访问接口
 * 
 * 提供工单基础信息的持久化操作，包括工单与电站关联信息查询、
 * 电站用户联系方式获取、电站类型和维修人员信息查询等功能
 * 
 * <AUTHOR>
 * @since 2023-08-10
 */
@Mapper
public interface WorkBaseInfoDao extends BaseDao<WorkBaseInfoEntity> {

    /**
     * 根据工单ID获取电站用户电话
     * 
     * @param workIdList 工单ID集合
     * @return 电站用户电话列表
     */
    List<String> getPlantUserPhoneByWorkId(List<Long> workIdList);

    /**
     * 根据名称获取对应的ID映射
     * 
     * @param plantTypeLabel 组织名称
     * @param repairs 维修人数组
     * @return 名称与ID的映射关系
     */
    Map<String, String> trans(@Param("plantTypeLabel") String plantTypeLabel, @Param("repairs") String[] repairs);

    /**
     * 根据工单ID获取电站类型和维修人员信息
     * 
     * @param workId 工单ID
     * @return 包含电站类型和维修人员信息的映射
     */
    Map<String, String> getPlantTypeAndRepairById(@Param("workId") Long workId);

    /**
     * 根据电站ID列表获取工单状态信息
     * 
     * @param plantIds 电站ID列表
     * @return 工单流程管理视图列表
     */
    List<WorkOrderProcessManageVO> getStatus(@Param("plantIds")List<String> plantIds);

}