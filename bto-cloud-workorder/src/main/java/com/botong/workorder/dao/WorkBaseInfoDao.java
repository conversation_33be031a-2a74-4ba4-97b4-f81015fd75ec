package com.botong.workorder.dao;

import com.botong.framework.mybatis.dao.BaseDao;
import com.botong.workorder.entity.WorkBaseInfoEntity;
import com.botong.workorder.vo.WorkOrderProcessManageVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 工单基础明细表
 *
 * <AUTHOR>
 * @since 2023-08-10
 */
@Mapper
public interface WorkBaseInfoDao extends BaseDao<WorkBaseInfoEntity> {

    /**
     * 根据工单ID获取电站用户电话
     * @param workIdList 工单ID集合
     * @return 电站用户电话
     */
    List<String> getPlantUserPhoneByWorkId(List<Long> workIdList);

    /**
     * 根据名称获取对应的ID
     * @param plantTypeLabel 组织名称
     * @param repairs 维修人
     */
    Map<String, String> trans(@Param("plantTypeLabel") String plantTypeLabel, @Param("repairs") String[] repairs);

    /**
     * 根据工单ID获取电站类型和维修人员
     * @param workId 工单ID
     */
    Map<String, String> getPlantTypeAndRepairById(@Param("workId") Long workId);

    List<WorkOrderProcessManageVO> getStatus(@Param("plantIds")List<String> plantIds);

}