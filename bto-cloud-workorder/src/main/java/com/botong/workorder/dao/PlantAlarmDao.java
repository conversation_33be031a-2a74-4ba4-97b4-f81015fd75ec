package com.botong.workorder.dao;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.botong.api.module.photovoltaic.vo.PlantAlarmVO;
import com.botong.framework.mybatis.dao.BaseDao;
import com.botong.workorder.entity.PlantAlarmEntity;
import com.botong.workorder.query.PlantAlarmQuery;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 告警视图数据访问接口
 * 
 * 提供电站告警相关数据的持久化操作，包括告警信息的分页查询等功能
 * 
 * <AUTHOR>
 * @since 1.0.0 2024-01-24
 */
@Mapper
public interface PlantAlarmDao extends BaseDao<PlantAlarmEntity> {

    /**
     * 分页获取告警列表
     * 
     * @param query 告警查询条件对象，包含告警信息的筛选条件
     * @param page 分页参数对象，包含分页信息和排序规则
     * @return 告警信息分页结果
     */
    IPage<PlantAlarmVO> getPageList(@Param("query") PlantAlarmQuery query, @Param("page") Page<PlantAlarmVO> page);
}