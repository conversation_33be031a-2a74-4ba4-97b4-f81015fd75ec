package com.botong.workorder.dao;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.botong.api.module.photovoltaic.vo.PlantAlarmVO;
import com.botong.framework.mybatis.dao.BaseDao;
import com.botong.workorder.entity.PlantAlarmEntity;
import com.botong.workorder.query.PlantAlarmQuery;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
* 告警视图
*
* <AUTHOR> 
* @since 1.0.0 2024-01-24
*/
@Mapper
public interface PlantAlarmDao extends BaseDao<PlantAlarmEntity> {

    IPage<PlantAlarmVO> getPageList(@Param("query") PlantAlarmQuery query, @Param("page") Page<PlantAlarmVO> page);
}