package com.botong.workorder.dao;

import com.botong.workorder.query.WorkEvaluationScoreQuery;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 工作订单评估评分数据访问接口
 * 
 * 提供工单评估评分相关数据的持久化操作，包括工程师信息查询、
 * 报警类型字典数据获取、工程师评分数据查询等功能
 * 
 * <AUTHOR>
 * @since 1.0.0 2025-07-04
 */
@Mapper
public interface WorkEvaluationScoreDao {

    /**
     * 获取工程师基本信息
     * 
     * @param query 查询条件对象，包含工程师筛选条件
     * @return 工程师信息映射列表，包含工程师的基本信息字段
     */
    List<Map<String, Object>> getEngineerInfo(@Param("query") WorkEvaluationScoreQuery query);

    /**
     * 获取所有报警类型字典数据
     * 
     * @return 报警类型字典列表，包含报警类型的编码和名称等信息
     */
    List<Map<String, Object>> getAllAlarmTypes();

    /**
     * 获取工程师扁平化评分数据
     * 
     * @param query 查询条件对象，包含评分筛选条件
     * @return 工程师扁平化评分数据列表，包含评分的各项指标
     */
    List<Map<String, Object>> getEngineerFlatScores(@Param("query") WorkEvaluationScoreQuery query);
}
