package com.botong.workorder.dao;

import com.botong.workorder.query.WorkEvaluationScoreQuery;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 工作订单评估评分数据访问层
 *
 * <AUTHOR>
 * @since 1.0.0 2025-07-04
 */
@Mapper
public interface WorkEvaluationScoreDao {

    /**
     * 获取工程师基本信息
     *
     * @param query 查询条件
     * @return 工程师信息映射
     */
    List<Map<String, Object>> getEngineerInfo(@Param("query") WorkEvaluationScoreQuery query);

    /**
     * 获取所有报警类型字典数据
     *
     * @return 报警类型字典列表
     */
    List<Map<String, Object>> getAllAlarmTypes();

    /**
     * 获取工程师扁平化评分数据
     *
     * @param query 查询条件
     * @return 扁平化评分数据
     */
    List<Map<String, Object>> getEngineerFlatScores(@Param("query") WorkEvaluationScoreQuery query);
}
