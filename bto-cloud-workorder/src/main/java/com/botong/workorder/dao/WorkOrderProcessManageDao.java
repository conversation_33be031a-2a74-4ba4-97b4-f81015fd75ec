package com.botong.workorder.dao;

import com.botong.framework.mybatis.dao.BaseDao;
import com.botong.workorder.entity.WorkOrderProcessManageViewEntity;
import com.botong.workorder.vo.*;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 工单流程管理
 *
 * <AUTHOR> by zhb on 2023/8/21.
 */

@Mapper
public interface WorkOrderProcessManageDao extends BaseDao<WorkOrderProcessManageViewEntity> {

    /**
     * 根据工单ID查询短信参数
     *
     * @param idList 工单集合
     * @return 短信参数实体
     */
    @MapKey("workId")
    List<Map<String, String>> selectSmsParamByWorkIds(@Param("idList") List<Long> idList);

    List<SysUserVO> getWorkIdWithRepairUserName(@Param("workIdList") List<Long> workIdList);

    Map<String, Object> getWorkOrderStatistics(Map<String, Object> params);

    List<StatisticsByAlarmTimeVO> statisticsByAlarmTime(Map<String, Object> year);

    List<MonthlyOpsWorkOrderSummaryVO> monthlyOpsWorkOrderSummary(HashMap<String, Object> params);

    List<CityStatsStatistics> getPlantStatisticsInfo();

    List<CityStatsStatistics> getAlarmStatisticsInfo();

    List<CityStatsStatistics> getWorkOrderStatisticsInfo();

    List<String> getListByStatus(@Param("statusList")List<Integer> statusList);

    List<UserAlarmTypeStatisticsVO> getUserAlarmTypeStatistics(HashMap<String, Object> params);

    @MapKey("城市")
    List<Map<String, Object>> getUnfinishedWorkOrderCount();

}
