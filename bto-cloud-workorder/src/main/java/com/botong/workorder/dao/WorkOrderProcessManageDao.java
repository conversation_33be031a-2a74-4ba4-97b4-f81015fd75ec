package com.botong.workorder.dao;

import com.botong.framework.mybatis.dao.BaseDao;
import com.botong.workorder.entity.WorkOrderProcessManageViewEntity;
import com.botong.workorder.vo.*;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 工单流程管理数据访问接口
 * 
 * 提供工单流程管理的持久化操作，包括短信参数查询、工单统计、
 * 告警时间统计、月度运维工单汇总、用户告警类型统计等功能
 * 
 * <AUTHOR> by zhb on 2023/8/21.
 */
@Mapper
public interface WorkOrderProcessManageDao extends BaseDao<WorkOrderProcessManageViewEntity> {

    /**
     * 根据工单ID查询短信参数
     * 
     * @param idList 工单ID集合
     * @return 短信参数字符串映射列表，以工单ID为键
     */
    @MapKey("workId")
    List<Map<String, String>> selectSmsParamByWorkIds(@Param("idList") List<Long> idList);

    /**
     * 获取工单与维修人员关联信息
     * 
     * @param workIdList 工单ID列表
     * @return 系统用户视图列表，包含工单ID和维修人员姓名
     */
    List<SysUserVO> getWorkIdWithRepairUserName(@Param("workIdList") List<Long> workIdList);

    /**
     * 获取工单统计数据
     * 
     * @param params 查询参数映射
     * @return 工单统计结果映射
     */
    Map<String, Object> getWorkOrderStatistics(Map<String, Object> params);

    /**
     * 按告警时间统计工单数据
     * 
     * @param year 年份参数映射
     * @return 告警时间统计视图列表
     */
    List<StatisticsByAlarmTimeVO> statisticsByAlarmTime(Map<String, Object> year);

    /**
     * 获取月度运维工单汇总数据
     * 
     * @param params 查询参数映射
     * @return 月度运维工单汇总视图列表
     */
    List<MonthlyOpsWorkOrderSummaryVO> monthlyOpsWorkOrderSummary(HashMap<String, Object> params);

    /**
     * 获取电站统计信息
     * 
     * @return 城市统计信息列表，包含电站相关统计数据
     */
    List<CityStatsStatistics> getPlantStatisticsInfo();

    /**
     * 获取告警统计信息
     * 
     * @return 城市统计信息列表，包含告警相关统计数据
     */
    List<CityStatsStatistics> getAlarmStatisticsInfo();

    /**
     * 获取工单统计信息
     * 
     * @return 城市统计信息列表，包含工单相关统计数据
     */
    List<CityStatsStatistics> getWorkOrderStatisticsInfo();

    /**
     * 根据状态获取工单列表
     * 
     * @param statusList 状态值列表
     * @return 符合状态的工单标识列表
     */
    List<String> getListByStatus(@Param("statusList")List<Integer> statusList);

    /**
     * 获取用户告警类型统计信息
     * 
     * @param params 查询参数映射
     * @return 用户告警类型统计视图列表
     */
    List<UserAlarmTypeStatisticsVO> getUserAlarmTypeStatistics(HashMap<String, Object> params);

    /**
     * 获取未完工工单数量统计
     * 
     * @return 未完工工单数量映射列表，以城市名称为键
     */
    @MapKey("城市")
    List<Map<String, Object>> getUnfinishedWorkOrderCount();

}
