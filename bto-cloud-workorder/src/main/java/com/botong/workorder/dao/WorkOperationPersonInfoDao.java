package com.botong.workorder.dao;

import com.botong.workorder.entity.WorkOperationPersonInfoEntity;
import com.github.yulichang.base.MPJBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* 工单运维人员关联表
*
* <AUTHOR> 
* @since 1.0.0 2023-08-21
*/
@Mapper
public interface WorkOperationPersonInfoDao extends MPJBaseMapper<WorkOperationPersonInfoEntity> {

    List<Long> getWorkIdListByCreatorId(@Param("userId") Long userId);

}