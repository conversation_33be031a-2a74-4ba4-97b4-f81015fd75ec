package com.botong.workorder.dao;

import com.botong.workorder.entity.WorkOperationPersonInfoEntity;
import com.github.yulichang.base.MPJBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 工单运维人员关联表数据访问接口
 * 
 * 提供工单运维人员关联信息的持久化操作，包括运维人员与工单的关联关系管理
 * 用于工单运维任务的分配和管理
 * 
 * <AUTHOR>
 * @since 1.0.0 2023-08-21
 */
@Mapper
public interface WorkOperationPersonInfoDao extends MPJBaseMapper<WorkOperationPersonInfoEntity> {

    /**
     * 根据创建人ID获取工单ID列表
     * 
     * @param userId 用户ID（创建人ID）
     * @return 该用户创建的工单ID列表
     */
    List<Long> getWorkIdListByCreatorId(@Param("userId") Long userId);

}