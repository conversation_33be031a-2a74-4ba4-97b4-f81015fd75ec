package com.botong.workorder.dao;

import com.botong.framework.mybatis.dao.BaseDao;
import com.botong.workorder.entity.WorkServiceOfferEntity;
import org.apache.ibatis.annotations.Mapper;

/**
 * 工单服务报价表数据访问接口
 * 
 * 提供工单服务报价相关数据的持久化操作，包括服务报价的增删改查等基础功能
 * 用于工单服务的价格管理和报价记录
 * 
 * <AUTHOR>
 * @since 1.0.0 2023-08-23
 */
@Mapper
public interface WorkServiceOfferDao extends BaseDao<WorkServiceOfferEntity> {
	
}