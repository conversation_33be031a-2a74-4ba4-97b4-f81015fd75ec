package com.botong.workorder.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import java.util.ArrayList;

/**
 * <AUTHOR>
 * @since 2025/2/25 11:26
 */
@Data
@ToString
@Schema(description = "电站现场图片")
public class  SiteDiagramYxVO {

    @Schema(description = "图片名称")
    String imageName;

    @Schema(description = "图片地址")
    ArrayList<ImageList> imageList;

    @Data
    public static class ImageList {
        String imageAddr;
        String imageName;
    }
}
