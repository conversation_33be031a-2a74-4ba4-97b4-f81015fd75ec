package com.botong.workorder.vo;

import com.botong.framework.common.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR> by zhb on 2025/5/15.
 */
@Data
@Schema(description = "城市电费电量分析")
public class ElectricChargeAnalyzeVO {

    @Schema(description = "城市")
    private String city;

    @Schema(description = "电站数量")
    private Integer plantNumber;

    @Schema(description = "电费账单汇总电量")
    private Double electricity;

    @Schema(description = "总装机容量")
    private Double totalCapacity;

    @Schema(description = "预测月等效小时")
    private Double monthEffectiveHours;

    @Schema(description = "实际月等效小时")
    private Double monthActualEffectiveHours;

    @Schema(description = "分析结果")
    private String analyzeResult;

    @Schema(description = "结算月份")
    private String settlementPeriod;

    @Schema(description = "电站名称")
    private String plantName;

    @Schema(description = "供电局结算电量")
    private Double electricitySettlement;

    @Schema(description = "装机容量")
    private Double plantCapacity;

    @Schema(description = "并网日期")
    @JsonFormat(pattern = DateUtils.DATE_PATTERN, timezone = "GMT+8")
    private Date createTime;

    @Schema(description = "光云月等效小时")
    private Double gYEffectiveHours;

    @Schema(description = "供电月实际小时")
    private Double gDEffectiveHours;

}