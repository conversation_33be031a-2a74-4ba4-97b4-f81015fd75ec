package com.botong.workorder.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
* 工单费用账单
*
* <AUTHOR> 
* @since 1.0.0 2025-01-14
*/
@Data
@Schema(description = "工单费用账单")
public class BillWorkExpenseVO implements Serializable {
	private static final long serialVersionUID = 1L;

	@Schema(description = "工单ID")
	private Integer workId;

	@Schema(description = "费用名称")
	private String name;

	@Schema(description = "费用规格")
	private String specification;

	@Schema(description = "规格数量")
	private Long quantity;

	@Schema(description = "费用单价")
	private BigDecimal price;

	@Schema(description = "费用说明")
	private String remark;

	@Schema(description = "总价")
	private String totalPrice;

}