package com.botong.workorder.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 工程师评分详情
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-07-04
 */
@Data
@Schema(description = "工程师评分详情")
public class EngineerScoreDetailVO {
    
    @Schema(description = "工程师ID")
    private Long repairId;
    
    @Schema(description = "工程师姓名")
    private String repairName;
    
    @Schema(description = "总评估分数")
    private BigDecimal totalScore;
    
    @Schema(description = "处理的工单总数")
    private Integer totalWorkOrders;
    
    @Schema(description = "按报警类型分组的评分详情")
    private List<AlarmTypeScoreVO> alarmTypeScores;
}
