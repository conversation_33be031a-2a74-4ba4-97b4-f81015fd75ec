package com.botong.workorder.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.botong.framework.common.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.TransPojo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2025/3/4 11:46
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class LowPowerPlantVO implements TransPojo {
    /**
     * 本属性对于导出无用，只是用于翻译
     */
    @ExcelIgnore
    Map<String, String> transMap = new HashMap<>();

    @Schema(description = "城市")
    @ExcelProperty(value = "城市")
    private String city;

    @Schema(description = "项目类型")
    @ExcelIgnore
    private String projectSpecial;

    @Schema(description = "项目类型")
    @ExcelProperty(value = "项目类型")
    private String projectName;

    @Schema(description = "电站名称")
    @ExcelProperty(value = "电站名称")
    private String plantName;

    @Schema(description = "装机容量")
    @ExcelProperty(value = "装机容量")
    private String plantCapacity;

    @Schema(description = "总发电量")
    @ExcelProperty(value = "总发电量")
    private String totalElectricity;

    @Schema(description = "状态")
    @ExcelIgnore
    @Trans(type = TransType.DICTIONARY, key = "plant_status", ref = "statusLabel")
    private String status;

    @Schema(description = "状态")
    @ExcelProperty(value = "状态")
    private String statusLabel;

    @Schema(description = "创建时间")
    @ExcelProperty(value = "创建时间")
    @JsonFormat(pattern = DateUtils.DATE_PATTERN)
    private Date createTime;

    @Schema(description = "发电量")
    @ExcelProperty(value = "发电量")
    private String todayElectricity;

    @Schema(description = "等效小时")
    @ExcelProperty(value = "等效小时")
    private String equivalentHours;

    @Schema(description = "城市平均等效小时")
    @ExcelProperty(value = "城市平均等效小时")
    private String cityAvgHours;

    @Schema(description = "所在城市发电率")
    @ExcelIgnore
    private BigDecimal cityGenerationRate;

    @Schema(description = "所在城市发电率")
    @ExcelProperty(value = "所在城市发电率")
    private String cityGenerationRateLabel;


    @Schema(description = "检测结果")
    @ExcelProperty(value = "检测结果")
    private String result;

    public String getCityGenerationRateLabel() {
        if (cityGenerationRate == null) {
            return "0.00%";
        }
        return cityGenerationRate.multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP) + "%";
    }

    public String getResult() {
        if (cityGenerationRate != null) {
            if (cityGenerationRate.compareTo(BigDecimal.valueOf(0.6)) < 0) {
                return "低发电量";
            } else if (cityGenerationRate.compareTo(BigDecimal.valueOf(0.8)) < 0) {
                return "一般";
            } else if (cityGenerationRate.compareTo(BigDecimal.valueOf(1.0)) <= 0) {
                return "正常";
            } else {
                return "良好";
            }
        } else {
            return "低发电量";
        }
    }


}
