package com.botong.workorder.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.botong.framework.common.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.TransPojo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 二次增值台账表
 *
 * <AUTHOR>
 * @since 1.0.0 2025-02-24
 */
@Data
@Schema(description = "二次增值台账表")
@ContentRowHeight(40)
@HeadRowHeight(25)
@ColumnWidth(12)
public class WorkChargeVO implements Serializable, TransPojo {
    private static final long serialVersionUID = 1L;

    @ExcelProperty("序号")
    private Long id;

    @Schema(description = "电站uid")
    @ExcelIgnore
    private String plantUid;

    @Schema(description = "平台")
    @ExcelProperty("平台")
    private String platformLabel;

    @Schema(description = "电站名称")
    @ExcelProperty("电站名称")
    private String plantName;

    @Schema(description = "是否关联工单，1表示关联，0表示不关联")
    @ExcelProperty("是否关联工单")
    @ColumnWidth(25)
    private String isAssociatedLabel;

    @Schema(description = "工单ID", required = true)
    @ExcelProperty("工单ID")
    private Long workOrderId;

    @Schema(description = "收费日期")
    @ColumnWidth(15)
    @JsonFormat(pattern = DateUtils.DATE_PATTERN)
    @DateTimeFormat("yyyy年MM月dd日")
    @ExcelProperty(value = "收费日期")
    private Date chargeDate;

    @Schema(description = "收费类型")
    @ExcelProperty("收费类型")
    private String chargeTypeLabel;

    @Schema(description = "维护内容")
    @ExcelProperty("维护内容")
    private String maintenanceContent;

    @Schema(description = "数量")
    @ExcelProperty("数量")
    private BigDecimal quantity;

    @Schema(description = "单价")
    @ExcelProperty("单价")
    private BigDecimal unitPrice;

    @Schema(description = "合计")
    @ExcelProperty("合计")
    private BigDecimal totalAmount;

    @Schema(description = "收款人")
    @ExcelProperty("收款人")
    private String payee;

    @Schema(description = "备注")
    @ExcelProperty("备注")
    private String remarks;

    @Schema(description = "附件")
    @ExcelProperty("附件")
    private String attachment;

    @ExcelIgnore
    @Trans(type = TransType.DICTIONARY, key = "platform_type", ref = "platformLabel")
    private Integer platformType;

    @ExcelIgnore
    @Trans(type = TransType.DICTIONARY, key = "is_associated", ref = "isAssociatedLabel")
    private Integer isAssociated;

    @ExcelIgnore
    @Trans(type = TransType.DICTIONARY, key = "charge_type", ref = "chargeTypeLabel")
    private Integer chargeType;

}