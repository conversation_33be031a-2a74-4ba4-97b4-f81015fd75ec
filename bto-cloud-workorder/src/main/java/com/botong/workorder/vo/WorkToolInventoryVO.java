package com.botong.workorder.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.botong.framework.common.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.TransPojo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
* 运维工具库
*
* <AUTHOR> 
* @since 1.0.0 2025-03-11
*/
@Data
@EqualsAndHashCode
@ContentRowHeight(25)
@HeadRowHeight(40)
@ColumnWidth(25)
@Schema(description = "运维工具库")
public class WorkToolInventoryVO implements Serializable, TransPojo {
	private static final long serialVersionUID = 1L;

	@Schema(description = "主键ID")
	@ExcelIgnore
	private Integer id;

	@Schema(description = "类型")
	@ExcelIgnore
	@Trans(type = TransType.DICTIONARY, key = "tool_type", ref = "typeLabel")
	private Integer type;

	@Schema(description = "类型")
	@ExcelProperty(value = "类型")
	private String typeLabel;

	@Schema(description = "名称")
	@ExcelProperty(value = "名称")
	private String name;

	@Schema(description = "单位")
	@ExcelProperty(value = "单位")
	private String unit;

	@Schema(description = "数量")
	@ExcelProperty(value = "数量")
	private Integer quantity;

	@Schema(description = "入库时间")
	@JsonFormat(pattern = DateUtils.DATE_PATTERN)
	@ExcelProperty(value = "入库时间")
	@DateTimeFormat(DateUtils.DATE_PATTERN)
	private Date entryTime;

	@Schema(description = "保管责任人")
	@ExcelIgnore
	private Long custodian;

	@Schema(description = "归还或报废时间")
	@JsonFormat(pattern = DateUtils.DATE_PATTERN)
	@DateTimeFormat(DateUtils.DATE_PATTERN)
	@ExcelProperty(value = "归还或报废时间")
	private Date returnScrapTime;

	@Schema(description = "核实人员")
	@ExcelProperty(value = "核实人员")
	@ExcelIgnore
	private Long verifier;

	@Schema(description = "核实日期")
	@ExcelProperty(value = "核实日期")
	@JsonFormat(pattern = DateUtils.DATE_PATTERN)
	@DateTimeFormat(DateUtils.DATE_PATTERN)
	private Date verifyDate;

	@Schema(description = "备注")
	@ExcelProperty(value = "备注")
	private String remarks;

	@Schema(description = "附件")
	@ExcelProperty(value = "附件")
	private String attachment;

	@Schema(description = "保管责任人姓名")
	@ExcelProperty(value = "保管责任人姓名")
	private String custodianName;

	@Schema(description = "核实人员姓名")
	@ExcelProperty(value = "核实人员姓名")
	private String verifierName;

}