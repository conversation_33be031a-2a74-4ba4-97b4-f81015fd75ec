package com.botong.workorder.vo;

import com.botong.framework.common.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
* 工单考核系数表
*
* <AUTHOR> 
* @since 1.0.0 2025-07-03
*/
@Data
@Schema(description = "工单考核系数表")
public class WorkEvaluatCoefficientVO implements Serializable {
	private static final long serialVersionUID = 1L;

	@Schema(description = "主键ID")
	private Integer id;

	@Schema(description = "城市名称")
	private String city;

	@Schema(description = "问题类型")
	private Integer type;

	@Schema(description = "得分")
	private BigDecimal score;

	@Schema(description = "创建时间")
	@JsonFormat(pattern = DateUtils.DATE_TIME_PATTERN)
	private Date createTime;

	@Schema(description = "更新时间")
	@JsonFormat(pattern = DateUtils.DATE_TIME_PATTERN)
	private Date updateTime;

	@Schema(description = "版本号")
	private Integer version;

	@Schema(description = "删除标识：0：正常 1：删除")
	private Integer deleted;

	@Schema(description = "创建者")
	private Long creator;

	@Schema(description = "更新者")
	private Long updater;


}