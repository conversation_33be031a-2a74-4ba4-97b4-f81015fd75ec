package com.botong.workorder.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 * @since 2025/2/26 11:30
 */
@Data
@ToString
@Schema(description = "每月运营工单汇总")
public class MonthlyOpsWorkOrderSummaryVO {

    @Schema(description = "城市")
    String city;

    @Schema(description = "故障类型")
    String alarmType;

    @Schema(description = "数量")
    Integer count;
}
