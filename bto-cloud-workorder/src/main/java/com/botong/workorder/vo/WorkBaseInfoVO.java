package com.botong.workorder.vo;

import com.botong.framework.common.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 工单基础明细表
 *
 * <AUTHOR>
 * @since 2023-08-10
 */
@Data
@Schema(description = "工单基础明细表")
public class WorkBaseInfoVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(description = "工单id")
    private Long id;

    @Schema(description = "0->待派单，1->待维修，2->待审核，3->已完成")
    private Integer status;

    @Schema(description = "电站id")
    private String plantId;

    @Schema(description = "电站类型")
    private Integer plantType;

    @Schema(description = "工单来源")
    private Integer source;

    @Schema(description = "故障时间")
    @JsonFormat(pattern = DateUtils.DATE_TIME_PATTERN)
    private Date alarmTime;

    @Schema(description = "故障等级")
    private Integer alarmLevel;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = DateUtils.DATE_TIME_PATTERN)
    private Date createTime;

    @Schema(description = "更新时间")
    @JsonFormat(pattern = DateUtils.DATE_TIME_PATTERN)
    private Date updateTime;

    @Schema(description = "故障报修照片路径")
    private String issuePhoto;
}