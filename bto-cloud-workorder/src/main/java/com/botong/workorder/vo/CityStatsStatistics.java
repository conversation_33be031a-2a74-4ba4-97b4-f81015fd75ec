package com.botong.workorder.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "工单城市监控")
@ContentRowHeight(20)
@HeadRowHeight(20)
@ColumnWidth(12)
public class CityStatsStatistics {

    @Schema(description = "城市")
    @ExcelProperty({"城市"})
    private String city;

    @Schema(description = "告警总数")
    @ExcelProperty({"告警总数"})
    private Integer totalAlarms = 0;

    @ExcelProperty({"户租项目", "电站总数"})
    @Schema(description = "电站总数")
    private Integer rentalPlantTotal = 0;

    @ExcelProperty({"户租项目", "故障总数"})
    @Schema(description = "故障总数")
    private Integer rentalAlarmTotal = 0;

    @ExcelProperty({"户租项目", "失压开关故障"})
    @Schema(description = "失压开关故障")
    private Integer lowVoltageFaults = 0;

    @ExcelProperty({"户租项目", "市电停电"})
    @Schema(description = "市电停电")
    private Integer powerOutage = 0;

    @ExcelProperty({"户租项目", "通讯异常"})
    @Schema(description = "通讯异常")
    private Integer communicationFaults = 0;

    @ExcelProperty({"户用项目", "电站总数"})
    @Schema(description = "电站总数")
    private Integer householdPlantTotal = 0;

    @ExcelProperty({"户用项目","户用项目【在保】", "电站总数"})
    @Schema(description = "电站总数")
    private Integer householdValidTotal = 0;

    @ExcelProperty({"户用项目","户用项目【在保】", "故障总数"})
    @Schema(description = "故障总数")
    private Integer householdValidAlarms = 0;

    @ExcelProperty({"户用项目","户用项目【在保】", "业主原因"})
    @Schema(description = "业主原因")
    private Integer householdOwnerFaults = 0;

    @ExcelProperty({"户用项目","户用项目【过保】", "电站总数"})
    @Schema(description = "电站总数")
    private Integer householdExpiredTotal = 0;

    @ExcelProperty({"户用项目","户用项目【过保】", "故障总数"})
    @Schema(description = "故障总数")
    private Integer householdExpiredAlarms = 0;

    @ExcelProperty({"户用项目","户用项目【过保】", "其它故障"})
    @Schema(description = "其它故障")
    private Integer householdExpiredOther = 0;

    @ExcelProperty({"整县项目", "电站总数"})
    @Schema(description = "电站总数")
    private Integer countyPlantTotal = 0;

    @ExcelProperty({"整县项目", "故障总数"})
    @Schema(description = "故障总数")
    private Integer countyAlarmTotal = 0;

    @ExcelProperty({"工商业项目", "电站总数"})
    @Schema(description = "电站总数")
    private Integer commercialPlantTotal = 0;

    @ExcelProperty({"工商业项目", "故障总数"})
    @Schema(description = "故障总数")
    private Integer commercialAlarmTotal = 0;

    @ExcelProperty({"工单统计", "今日派单数量"})
    @Schema(description = "今日派单数量")
    private Integer todayOrders = 0;

    @ExcelProperty({"工单统计", "昨日派单数量"})
    @Schema(description = "昨日派单数量")
    private Integer yesterdayOrders = 0;

    @ExcelProperty({"工单统计", "昨日完成工单数量"})
    @Schema(description = "昨日完成工单数量")
    private Integer yesterdayCompleted = 0;

    @ExcelProperty({"工单统计", "今日工单超时数量"})
    @Schema(description = "今日工单超时数量")
    private Integer todayOvertime = 0;
}