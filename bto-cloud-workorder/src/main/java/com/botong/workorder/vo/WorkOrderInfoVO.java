package com.botong.workorder.vo;

import com.botong.api.module.system.vo.RepairUserVO;
import lombok.*;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/9/5 10:39
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ToString
public class WorkOrderInfoVO {
    WorkOrderBaseDetailsVO workBaseInfo;

    WorkOperationInfoVO workOperationInfo;

    WorkExamineInfoVO workExamineInfo;

    List<RepairUserVO> repairUsers;

    List<BillWorkExpenseVO> billWorkExpenseList;

    List<WorkChargeVO> workChargeList;
}
