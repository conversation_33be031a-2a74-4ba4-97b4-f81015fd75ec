package com.botong.workorder.vo;

import com.botong.api.module.system.vo.RepairUserVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/9/5 10:39
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ToString
@Schema(description = "工单信息合集")
public class WorkOrderInfoVO {

    @Schema(description = "工单基础信息")
    WorkOrderBaseDetailsVO workBaseInfo;

    @Schema(description = "工单运维信息")
    WorkOperationInfoVO workOperationInfo;

    @Schema(description = "工单审核信息")
    WorkExamineInfoVO workExamineInfo;

    @Schema(description = "维修人员")
    List<RepairUserVO> repairUsers;

    @Schema(description = "费用账单")
    List<BillWorkExpenseVO> billWorkExpenseList;

    @Schema(description = "二次增值费用")
    List<WorkChargeVO> workChargeList;
}