package com.botong.workorder.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 电站材料表
 *
 * <AUTHOR>
 * @since 2023-08-23
 */
@Data
@Schema(description = "电站材料管理")
public class OfferPlantMaterialInfoVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 规格/材料 id
     */
    private Long id;
    /**
     * 规格/材料 name
     */
    private String name;
    /**
     * 类型
     */
    private Integer type;

    /**
     * 供应商id
     */
    private Long supplyId;
    /**
     * 供应商name
     */
    private String supplyName;

    /**
     * 材料类型id
     */
    private Long materialTypeId;
    /**
     * 材料类型name
     */
    private String materialTypeName;

    /**
     * 厂家id
     */
    private Long manufacturerId;
    /**
     * 厂家name
     */
    private String manufacturerName;


    /**
     * 采购方式
     */
    private String purchaseWay;

    /**
     * 单价
     */
    private BigDecimal price;

    /**
     * 数量
     */
    private Integer count;
    /**
     * 总价
     */
    private BigDecimal totalPrice;

    /**
     * 审核结果
     */
    private Integer auditResult;

}