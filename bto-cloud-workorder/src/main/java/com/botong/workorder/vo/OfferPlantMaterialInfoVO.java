package com.botong.workorder.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 电站材料表
 *
 * <AUTHOR>
 * @since 2023-08-23
 */
@Data
@Schema(description = "电站材料管理")
public class OfferPlantMaterialInfoVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(description = "规格/材料")
    private Long id;

    @Schema(description = "规格/材料")
    private String name;

    @Schema(description = "类型")
    private Integer type;

    @Schema(description = "供应商id")
    private Long supplyId;

    @Schema(description = "供应商名称")
    private String supplyName;

    @Schema(description = "材料类型id")
    private Long materialTypeId;

    @Schema(description = "材料类型名称")
    private String materialTypeName;

    @Schema(description = "厂家id")
    private Long manufacturerId;

    @Schema(description = "厂家名称")
    private String manufacturerName;

    @Schema(description = "采购方式")
    private String purchaseWay;

    @Schema(description = "单价")
    private BigDecimal price;

    @Schema(description = "数量")
    private Integer count;

    @Schema(description = "总价")
    private BigDecimal totalPrice;

    @Schema(description = "审核结果")
    private Integer auditResult;

}