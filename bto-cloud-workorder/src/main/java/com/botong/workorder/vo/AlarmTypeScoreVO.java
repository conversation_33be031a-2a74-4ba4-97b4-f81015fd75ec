package com.botong.workorder.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 报警类型评分详情
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-07-04
 */
@Data
@Schema(description = "报警类型评分详情")
public class AlarmTypeScoreVO {

    @Schema(description = "工程师ID")
    private Long repairId;

    @Schema(description = "工程师姓名")
    private String repairName;

    @Schema(description = "报警类型（数值）")
    private Integer alarmType;

    @Schema(description = "报警类型名称")
    private String alarmTypeName;

    @Schema(description = "工单数量")
    private Integer count;

    @Schema(description = "系数得分")
    private BigDecimal coefficient;

    @Schema(description = "小计得分")
    private BigDecimal subtotal;

    @Schema(description = "城市")
    private String city;
}
