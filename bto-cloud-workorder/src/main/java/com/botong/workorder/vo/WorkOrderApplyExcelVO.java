package com.botong.workorder.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.botong.framework.common.utils.DateUtils;
import com.botong.workorder.dto.WorkOrderAssignDTO;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.TransPojo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * excel工单表
 *
 * <AUTHOR> by zhb on 2023/8/18.
 */
@Data
@EqualsAndHashCode
@ContentRowHeight(25)
@HeadRowHeight(40)
@ColumnWidth(25)
public class WorkOrderApplyExcelVO implements Serializable, TransPojo {
    private static final long serialVersionUID = 1L;

    @ExcelIgnore
    Map<String, String> transMap = new HashMap<>();

    @ExcelProperty("平台类型")
    private String platformLabel;

    @ExcelProperty("工单id")
    private Long workId;

    @ExcelProperty("电站编号")
    private String plantId;

    @ExcelProperty("国家")
    private String country;

    @ExcelProperty("省份")
    private String province;

    @ExcelProperty("市/州")
    private String city;

    @ExcelProperty("县/区")
    private String area;

    @ExcelProperty("电站名称")
    private String plantName;

    @ExcelProperty("电站用户")
    private String plantUserName;

    @ExcelProperty("电站地址")
    private String address;

    @ExcelProperty("逆变器编号")
    private String inverterId;

    @ExcelProperty("客户电话")
    private String userPhone;

    @ExcelProperty("电站类型")
    private String plantTypeName;

    @ExcelProperty("实际维修人员")
    private String companionUserName;

    @ExcelIgnore
    private String companion;

    @ExcelProperty("工单状态")
    private String workStatusLabel;

    @ExcelProperty(value = "故障日期")
    @DateTimeFormat(value = DateUtils.DATE_PATTERN)
    private Date alarmTime;

    @ExcelProperty("故障等级")
    private String alarmLevelLabel;

    @ExcelProperty("故障类型")
    private String alarmTypeLabel;

    @ExcelProperty("故障现象")
    private String alarmInfoLabel;

    @ExcelProperty("维修详情")
    private String opCause;

    @ExcelProperty("是否修复")
    private String repairStatusLabel;

    // @ExcelProperty("质保内容")
    // private String warrantyContent;

    @ExcelProperty(value = "质保到期时间")
    @DateTimeFormat(value = DateUtils.DATE_PATTERN)
    private Date warrantyExpireDate;

    @ExcelProperty("预计完成时间")
    @DateTimeFormat(value = DateUtils.DATE_PATTERN)
    private Date preRepairTime;

    @ExcelProperty("备注")
    private String remark;

    @ExcelIgnore
    @Trans(type = TransType.DICTIONARY, key = "timeout_warning_threshold", ref = "timeoutWarningThresholdLabel")
    private Integer timeoutWarningThreshold;

    @ExcelIgnore
    @Trans(type = TransType.DICTIONARY, key = "platform_type", ref = "platformLabel")
    private Integer platform;

    @ExcelIgnore
    @Trans(type = TransType.DICTIONARY, key = "alarm_type", ref = "alarmTypeLabel")
    private Integer alarmType;

    @ExcelIgnore
    @Trans(type = TransType.DICTIONARY, key = "work_status", ref = "workStatusLabel")
    private Integer status;

    @ExcelIgnore
    @Trans(type = TransType.DICTIONARY, key = "alarm_info", ref = "alarmInfoLabel")
    private Integer alarmInfo;

    @ExcelIgnore
    @Trans(type = TransType.DICTIONARY, key = "alarm_level", ref = "alarmLevelLabel")
    private Integer alarmLevel;

    @ExcelIgnore
    @Trans(type = TransType.DICTIONARY, key = "repair_status", ref = "repairStatusLabel")
    private Integer repairStatus;

    @ExcelIgnore
    private Integer plantType;

    @ExcelIgnore
    private WorkOrderAssignDTO workOrderAssignDTO;


}