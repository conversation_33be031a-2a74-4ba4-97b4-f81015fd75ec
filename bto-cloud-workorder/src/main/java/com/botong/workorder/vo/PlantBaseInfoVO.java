package com.botong.workorder.vo;

import com.botong.framework.common.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR> by zhb on 2023/8/16.
 */
@Data
@Schema(description = "电站明细表")
public class PlantBaseInfoVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(description = "电站id")
    private String plantId;

    @Schema(description = "电站名称")
    private String plantName;

    @Schema(description = "逆变器id")
    private String inverterId;

    @Schema(description = "地址")
    private String address;

    @Schema(description = "国家")
    private String country;

    @Schema(description = "省")
    private String province;

    @Schema(description = "市/州")
    private String city;

    @Schema(description = "县/区")
    private String area;

    @Schema(description = "镇/街道")
    private String town;

    @Schema(description = "电站用户名称")
    private String plantUserName;

    @Schema(description = "电站用户电话")
    private String userPhone;

    @Schema(description = "电站类型")
    private Integer type;

    @Schema(description = "质保内容")
    private String warrantyContent;

    @Schema(description = "质保到期日期")
    @JsonFormat(pattern = DateUtils.DATE_TIME_PATTERN)
    private Date warrantyExpireDate;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = DateUtils.DATE_TIME_PATTERN)
    private Date createTime;

    @Schema(description = "更新时间")
    @JsonFormat(pattern = DateUtils.DATE_TIME_PATTERN)
    private Date updateTime;

    @Schema(description = "电站流量")
    private Double flow;

    @Schema(description = "项目类型ID")
    private Integer projectId;

    @Schema(description = "项目类型名称")
    private String projectName;

    @Schema(description = "电站类型归属ID")
    private Long plantType;
    @Schema(description = "平台类型")
    private Integer platform;

    @Schema(description = "今日发电")
    private String todayElectricity;

    @Schema(description = "累计发电")
    private String totalElectricity;
    @Schema(description = "装机容量")
    private String plantCapacity;

    @Schema(description = "电站状态")
    private Integer plantStatus;

    @Schema(description = "电站告警信息")
    private String alarmStr;

    @Schema(description = "是否存在工单：0->不存在，1->存在")
    private Integer existWorkOrder;

}