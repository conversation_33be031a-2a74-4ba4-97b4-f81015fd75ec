package com.botong.workorder.vo;

import com.botong.api.module.system.vo.RepairUserVO;
import com.botong.framework.common.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
* 工单待运维表
*
* <AUTHOR> 
* @since 1.0.0 2023-08-22
*/
@Data
@Schema(description = "工单待运维表")
@AllArgsConstructor
@NoArgsConstructor
public class WorkOperationInfoVO implements Serializable {
	private static final long serialVersionUID = 1L;

	@Schema(description = "工单id")
	private Long workId;

	@Schema(description = "未修复原因")
	private String unrepairedCause;

	@Schema(description = "预修复时间")
	@JsonFormat(pattern = DateUtils.DATE_PATTERN)
	private Date preRepairTime;

	@Schema(description = "修复时间  未修复->1970-01-01")
	@JsonFormat(pattern = DateUtils.DATE_PATTERN)
	private Date repairTime;

	@Schema(description = "超时时间")
	private String overtime;

	@Schema(description = "故障原因")
	private Integer alarmCause;

	@Schema(description = "维修详情")
	private String opCause;

	@Schema(description = "优先级")
	private Integer priority;

	@Schema(description = "维修后照片")
	private String repairPhotoPath;

	@Schema(description = "故障现场照片")
	private String faultPhotoPath;

	@Schema(description = "创建时间")
	@JsonFormat(pattern = DateUtils.DATE_TIME_PATTERN)
	private Date createTime;

	@Schema(description = "更新时间")
	@JsonFormat(pattern = DateUtils.DATE_TIME_PATTERN)
	private Date updateTime;

	@Schema(description = "是否超时 1：是  0：否")
	private Integer overtimed;

	List<OfferPlantMaterialInfoVO> plantMaterialInfoList;

	@Schema(description = "报价审核结果")
	private Integer auditResult;

	@Schema(description = "故障现象：0->电网侧异常，1->发电侧异常，2->通讯异常，3->漏水，4->其它")
	@Range(min = 0, max = 50, message = "故障现象不正确")
	private Integer alarmInfo;

	@Schema(description = "故障设备：0->无故障，1->逆变器，2->运维器，3->断路器，4->失压开关，5->防雷器，6->结构件，7->电缆接插件，8->电表，9->电缆，10->光伏组件 11->通讯设备/模块")
	@Range(min = 0, max = 50, message = "故障设备不正确")
	private Integer alarmDevice;

	@Range(min = 0, max = 1)
	@NotNull(message = "维修状态不能为空")
	@Schema(description = "维修状态 0：已修复 1 ： 未修复")
	private Integer repairStatus;

	@Schema(description = "是否过保：0->在保，1->过保")
	private Integer warrantyStatus;

	@Schema(description = "故障类型：0->电网，1->设备，2->通讯，3->漏水，4->其他")
	@Range(min = 0, max = 50, message = "故障类型不正确")
	private Integer alarmType;

	@Schema(description = "费用账单")
	private List<BillWorkExpenseVO> billWorkExpenseList;

	@Schema(description = "二次增值费用")
	private List<WorkChargeVO> workChargeList;

	@Schema(description = "是否挂起：0->否  1->是")
	@Range(min = 0, max = 1)
	private Integer suspended;
	@Schema(description = "挂起过期时间")
	@JsonFormat(pattern = DateUtils.DATE_TIME_PATTERN)
	private Date suspendExpiration;

	@Schema(description = "是否有同行人：0->否  1->是")
	@Range(min = 0, max = 1)
	private Integer hasCompanion;

	@Schema(description = "同行人")
	List<RepairUserVO> companionUsers;
	public WorkOperationInfoVO(Long workId, Date preRepairTime, Integer overtimed) {
		this.workId = workId;
		this.preRepairTime = preRepairTime;
		this.overtimed = overtimed;
	}
}