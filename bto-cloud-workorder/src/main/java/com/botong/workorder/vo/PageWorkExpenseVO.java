package com.botong.workorder.vo;

import com.botong.framework.common.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 工单费用账单
 *
 * <AUTHOR>
 * @since 1.0.0 2025-01-14
 */
@Data
@Schema(description = "工单费用账单")
public class PageWorkExpenseVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(description = "工单ID")
    private Integer workId;

    @Schema(description = "电站名称")
    private String plantName;

    @Schema(description = "项目组织")
    private Integer projectOrg;

    @Schema(description = "账单日期")
    @JsonFormat(pattern = DateUtils.DATE_PATTERN)
    private Date billDate;

    @Schema(description = "工单状态")
    private Integer workStatus;

    @Schema(description = "合计金额")
    private BigDecimal totalPrice;

}