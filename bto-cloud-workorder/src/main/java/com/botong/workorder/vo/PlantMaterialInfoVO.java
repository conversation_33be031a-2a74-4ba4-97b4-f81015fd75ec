package com.botong.workorder.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Schema(description = "电站材料表")
public class PlantMaterialInfoVO implements Serializable {
    private static final long serialVersionUID = 1L;
    @Schema(description = "id")
    private Long id;

    @Schema(description = "上级ID")
    private Long pid;

    @Schema(description = "类型：0->厂家；1->供应商；2->材料类型；3->规格/材质")
    private Integer type;

    @Schema(description = "采购方式")
    private String purchaseWay;

    @Schema(description = "价格")
    private BigDecimal price;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "上级名称")
    private String parentName;
}