package com.botong.workorder.vo;

import com.botong.framework.common.utils.TreeNode;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@Builder
@RequiredArgsConstructor
@Schema(description = "电站材料表")
public class PlantMaterialInfoTree extends TreeNode<PlantMaterialInfoTree> implements Serializable,Cloneable {
    private static final long serialVersionUID = 1L;
    @Schema(description = "id")
    private Long id;
    /**
     * 上级ID
     */
    @Schema(description = "上级ID")
    private Long pid;

    @Schema(description = "类型：0->厂家；1->供应商；2->材料类型；3->规格/材质")
    private Integer type;

    @Schema(description = "采购方式")
    private String purchaseWay;

    @Schema(description = "价格")
    private BigDecimal price;

    @Schema(description = "名称")
    private String name;

    /**
     * 数量
     */
    private Integer count;
    /**
     * 总价
     */
    private BigDecimal totalPrice;


    @Override
    public PlantMaterialInfoTree clone() {
        try {
            return (PlantMaterialInfoTree) super.clone();
        } catch (CloneNotSupportedException e) {
            // 处理异常
            return null;
        }
    }
}