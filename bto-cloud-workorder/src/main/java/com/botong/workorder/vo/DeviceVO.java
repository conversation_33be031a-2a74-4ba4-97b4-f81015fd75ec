package com.botong.workorder.vo;

import com.botong.framework.common.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 设备
 *
 * <AUTHOR>
 * @since 2025-01-03
 */
@Data
@Schema(description = "设备")
public class DeviceVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(description = "电站id")
    private String plantId;

    @Schema(description = "设备编号（设备SN码）||逆变器和非三晶运维器")
    private String deviceId;

    @Schema(description = "运维器通讯模块imei || 等同于三晶 wisdom_device_sn")
    private String imei;

    @Schema(description = "厂家(XK:新科101 ， ZT：正泰， TH：泰和安科技 QH:齐辉电气)")
    private String manufacturer;

    @Schema(description = "型号")
    private String module;

    @Schema(description = "数据创建时间")
    @JsonFormat(pattern = DateUtils.DATE_TIME_PATTERN)
    private Date createTime;

    @Schema(description = "项目专项(1:户用，2：整县-河源)")
    private Integer projectSpecial;

    @Schema(description = "设备类型 1:逆变器 2:运维器")
    private Integer deviceType;

}