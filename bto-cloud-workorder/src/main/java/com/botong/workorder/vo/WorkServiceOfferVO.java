package com.botong.workorder.vo;

import com.botong.framework.common.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
* 工单服务报价表
*
* <AUTHOR> 
* @since 1.0.0 2023-08-23
*/
@Data
@Schema(description = "工单服务报价表")
public class WorkServiceOfferVO implements Serializable {
	private static final long serialVersionUID = 1L;

	@Schema(description = "id主键")
	private Integer id;

	@Schema(description = "工单id")
	private Long workId;

	@Schema(description = "材料id")
	private Long materialId;

	@Schema(description = "材料数量")
	private Integer count;

	@Schema(description = "总价格")
	private BigDecimal totalPrice;

	@Schema(description = "审核结果")
	private Integer auditResult;

	@Schema(description = "创建时间")
	@JsonFormat(pattern = DateUtils.DATE_TIME_PATTERN)
	private Date createTime;

	@Schema(description = "更新时间")
	@JsonFormat(pattern = DateUtils.DATE_TIME_PATTERN)
	private Date updateTime;


}