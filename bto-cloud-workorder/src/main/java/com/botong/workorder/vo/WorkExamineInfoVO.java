package com.botong.workorder.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import java.io.Serializable;
import com.botong.framework.common.utils.DateUtils;
import java.util.Date;

/**
* 工单审核管理
*
* <AUTHOR> 
* @since 1.0.0 2023-09-04
*/
@Data
@Schema(description = "工单审核管理")
public class WorkExamineInfoVO implements Serializable {
	private static final long serialVersionUID = 1L;

	@Schema(description = "id")
	private Integer id;

	@Schema(description = "工单id")
	private String workId;

	@Schema(description = "创建人id")
	private Long creator;

	@Schema(description = "审核意见")
	private String examineOpinion;

	@Schema(description = "审核评价")
	private Double evaluate;

	@Schema(description = "审核结果")
	private Integer auditResult;

	@Schema(description = "创建时间")
	@JsonFormat(pattern = DateUtils.DATE_TIME_PATTERN)
	private Date createTime;

	@Schema(description = "更新时间")
	@JsonFormat(pattern = DateUtils.DATE_TIME_PATTERN)
	private Date updateTime;

	@Schema(description = "更新人id")
	private Long updater;


}