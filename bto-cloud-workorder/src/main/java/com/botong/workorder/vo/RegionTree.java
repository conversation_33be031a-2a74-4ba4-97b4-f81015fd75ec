package com.botong.workorder.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class RegionTree {
    private Integer id;
    private String name;
    private Integer count;
    private String layer;
    private List<RegionTree> children = new ArrayList<>();

    public RegionTree(String name) {
        this.name = name;
    }
}