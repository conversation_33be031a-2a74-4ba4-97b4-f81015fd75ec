package com.botong.workorder.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Schema(description = "地区树结构数据")
public class RegionTree {

    @Schema(description = "id")
    private Integer id;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "数量")
    private Integer count;

    @Schema(description = "层级")
    private String layer;

    @Schema(description = "子节点")
    private List<RegionTree> children = new ArrayList<>();

    public RegionTree(String name) {
        this.name = name;
    }
}