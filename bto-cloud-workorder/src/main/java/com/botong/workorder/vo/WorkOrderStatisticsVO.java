package com.botong.workorder.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/8/11 9:47
 */
@Data
@NoArgsConstructor
@Builder
@Schema(description = "工单统计分析")
public class WorkOrderStatisticsVO<T> {

    @Schema(description = "统计结果")
    List<T> statistics;

    @Schema(description = "总数")
    Integer total;

    public WorkOrderStatisticsVO(List<T> statistics, Integer total) {
        this.statistics = statistics;
        this.total = total;
    }
}
