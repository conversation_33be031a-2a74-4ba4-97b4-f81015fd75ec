package com.botong.workorder.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/8/11 9:47
 */
@Data
@NoArgsConstructor
@Builder
public class WorkOrderStatisticsVO<T> {
    List<T> statistics;
    Integer total;

    public WorkOrderStatisticsVO(List<T> statistics, Integer total) {
        this.statistics = statistics;
        this.total = total;
    }
}
