package com.botong.workorder.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "地区工单数")
public class RegionCount {

    @Schema(description = "名称")
    private String dictLabel;

    @Schema(description = "数量")
    private Integer count;

    @Schema(description = "类型")
    private String type;
}