package com.botong.workorder.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import java.io.Serializable;
import com.botong.framework.common.utils.DateUtils;
import java.util.Date;

/**
* 工单运维人员关联表
*
* <AUTHOR> 
* @since 1.0.0 2023-08-21
*/
@Data
@Schema(description = "工单运维人员关联表")
public class WorkOperationPersonInfoVO implements Serializable {
	private static final long serialVersionUID = 1L;

	@Schema(description = "工单id")
	private Long workId;

	@Schema(description = "运维人员id")
	private Long repairId;

	@Schema(description = "状态")
	private Integer status;

	@Schema(description = "创建时间")
	@JsonFormat(pattern = DateUtils.DATE_TIME_PATTERN)
	private Date createTime;

	@Schema(description = "更新时间")
	@JsonFormat(pattern = DateUtils.DATE_TIME_PATTERN)
	private Date updateTime;

	@Schema(description = "id")
	private Integer id;


}