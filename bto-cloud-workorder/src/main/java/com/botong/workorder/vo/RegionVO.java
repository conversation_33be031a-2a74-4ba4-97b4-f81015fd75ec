package com.botong.workorder.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2023/8/15 8:50
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class RegionVO {
    private String id;
    /**
     * 国家
     */
    private String country;

    /**
     * 省
     */
    private String province;

    /**
     * 市/州
     */
    private String city;

    /**
     * 县/区
     */
    private String area;

    /**
     * 镇/街道
     */
    private String town;
}
