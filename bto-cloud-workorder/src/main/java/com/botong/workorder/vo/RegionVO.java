package com.botong.workorder.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2023/8/15 8:50
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Schema(description = "地区属性")
public class RegionVO {

    @Schema(description = "id")
    private String id;

    @Schema(description = "国家")
    private String country;

    @Schema(description = "省")
    private String province;

    @Schema(description = "市/州")
    private String city;

    @Schema(description = "县/区")
    private String area;

    @Schema(description = "镇/街道")
    private String town;
}
