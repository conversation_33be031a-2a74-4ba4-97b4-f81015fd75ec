package com.botong.workorder.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2025/3/20 16:26
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "工程师出来告警类型统计")
public class UserAlarmTypeStatisticsVO {

    @Schema(description = "用户名")
    private String username;

    @Schema(description = "岗位")
    private String postName;

    @Schema(description = "报警类型")
    private String alarmType;

    @Schema(description = "工单数量")
    private Integer count;
}
