package com.botong.workorder.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 工作订单评估评分扁平化响应
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-07-04
 */
@Data
@Schema(description = "工作订单评估评分扁平化响应")
public class WorkEvaluationFlatScoreVO {
    
    @Schema(description = "查询时间范围开始")
    private String startTime;
    
    @Schema(description = "查询时间范围结束")
    private String endTime;
    
    @Schema(description = "报警类型字段列表")
    private List<String> alarmTypeFields;
    
    @Schema(description = "工程师评分扁平化数据列表")
    private List<Map<String, Object>> engineerScores;
    
    @Schema(description = "查询总记录数")
    private Long totalRecords;
}
