package com.botong.workorder.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 工单审核管理
 *
 * <AUTHOR>
 * @since 1.0.0 2023-09-04
 */
@Data
@TableName("work_examine_info")
@AllArgsConstructor
@NoArgsConstructor
public class WorkExamineInfoEntity {

    /**
     * 工单id
     */
    private Long workId;
    /**
     * id
     */
    @TableId
    private Long id;


    /**
     * 审核意见
     */

    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String examineOpinion;

    /**
     * 审核评价
     */

    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Double evaluate;

    /**
     * 审核结果
     */

    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Integer auditResult;

    /**
     * 创建者
     */
    @TableField(fill = FieldFill.INSERT)
    private Long creator;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新者
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updater;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**
     * 删除标记
     */
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    private Integer deleted;

    public WorkExamineInfoEntity(Long workId, String examineOpinion, Double evaluate, Integer auditResult) {
        this.workId = workId;
        this.examineOpinion = examineOpinion;
        this.evaluate = evaluate;
        this.auditResult = auditResult;
    }
}