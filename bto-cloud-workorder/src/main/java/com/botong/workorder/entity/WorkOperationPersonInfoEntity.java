package com.botong.workorder.entity;

import com.botong.framework.mybatis.entity.BaseEntity;
import lombok.*;
import com.baomidou.mybatisplus.annotation.*;

import java.util.Date;

/**
 * 工单运维人员关联表
 *
 * <AUTHOR>
 * @since 1.0.0 2023-08-21
 */

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("work_operation_person_info")
public class WorkOperationPersonInfoEntity {
    @TableId
    private Long id;
    /**
     * 工单id
     */
    private Long workId;

    /**
     * 运维人员id
     */
    private Long repairId;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**
     * 删除标记
     */
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    private Integer deleted;

    public WorkOperationPersonInfoEntity(Long workId, Long repairId) {
        this.workId = workId;
        this.repairId = repairId;
    }
}