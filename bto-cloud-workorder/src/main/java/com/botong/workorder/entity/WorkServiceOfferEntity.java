package com.botong.workorder.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 工单服务报价表
 *
 * <AUTHOR> 
 * @since 1.0.0 2023-08-23
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("work_service_offer")
public class WorkServiceOfferEntity {
	/**
	* id主键
	*/
	@TableId
	private Integer id;

	/**
	* 工单id
	*/
	private Long workId;

	/**
	* 材料id
	*/
	private Long materialId;

	/**
	* 材料数量
	*/
	private Integer count;

	/**
	* 总价格
	*/
	private BigDecimal totalPrice;



	/**
	 * 创建时间
	 */
	@TableField(fill = FieldFill.INSERT)
	private Date createTime;

	/**
	 * 更新时间
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private Date updateTime;

	/**
	 * 删除标记
	 */
	@TableLogic
	@TableField(fill = FieldFill.INSERT)
	private Integer deleted;
	public WorkServiceOfferEntity(Long workId, Long materialId, Integer count) {
		this.workId = workId;
		this.materialId = materialId;
		this.count = count;
	}
}