package com.botong.workorder.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.botong.framework.mybatis.entity.BaseEntity;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 二次增值台账表
 *
 * <AUTHOR>
 * @since 1.0.0 2025-02-24
 */

@Data
@TableName("work_charge")
public class WorkChargeEntity extends BaseEntity {

    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 电站uid
     */
    private String plantUid;

    /**
     * 平台
     */
    private String platformType;

    /**
     * 电站名称
     */
    private String plantName;

    /**
     * 是否关联工单，1表示关联，0表示不关联
     */
    private Integer isAssociated;

    /**
     * 工单ID
     */
    private Integer workOrderId;

    /**
     * 收费日期
     */
    private Date chargeDate;

    /**
     * 收费类型
     */
    private Integer chargeType;

    /**
     * 维护内容
     */
    private String maintenanceContent;

    /**
     * 数量
     */
    private BigDecimal quantity;

    /**
     * 单价
     */
    private BigDecimal unitPrice;

    /**
     * 合计
     */
    private BigDecimal totalAmount;

    /**
     * 收款人
     */
    private String payee;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 附件
     */
    private String attachment;

}