package  com.botong.workorder.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.botong.framework.mybatis.entity.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 工单基础明细表
 *
 * <AUTHOR> 
 * @since  2023-08-10
 */
@EqualsAndHashCode(callSuper=false)
@Data
@TableName("work_base_info")
public class WorkBaseInfoEntity extends BaseEntity {

	/**
	* 工单状态
	*/
	private Integer status;

	/**
	* 电站id
	*/
	private String plantId;

	/**
	* 电站类型
	*/
	private Integer plantType;

	/**
	* 工单来源
	*/
	private Integer source;

	/**
	* 故障类型
	*/
	private Integer alarmType;

	/**
	* 故障时间
	*/
	private Date alarmTime;

	/**
	* 故障等级
	*/
	private Integer alarmLevel;

	/**
	* 故障报修照片路径
	*/
	@Schema(description = "故障报修照片路径")
	private String issuePhoto;
}