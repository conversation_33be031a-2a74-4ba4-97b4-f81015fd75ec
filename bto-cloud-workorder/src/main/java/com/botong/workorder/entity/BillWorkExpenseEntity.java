package com.botong.workorder.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.botong.framework.mybatis.entity.BaseEntity;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 工单费用账单
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-01-14
 */

@Data
@TableName("bill_work_expense")
public class BillWorkExpenseEntity extends BaseEntity {

	/**
	* 工单ID
	*/
	private Integer workId;

	/**
	* 费用名称
	*/
	private String name;

	/**
	* 费用规格
	*/
	private String specification;

	/**
	* 规格数量
	*/
	private Long quantity;

	/**
	* 费用单价
	*/
	private BigDecimal price;

	/**
	* 费用说明
	*/
	private String remark;

	/**
	 * 总价
	 */
	private String totalPrice;

}