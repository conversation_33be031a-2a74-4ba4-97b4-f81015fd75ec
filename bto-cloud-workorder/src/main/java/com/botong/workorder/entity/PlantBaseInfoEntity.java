package com.botong.workorder.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 电站信息
 * <AUTHOR> by zhb on 2023/8/16.
 */

@Data
@NoArgsConstructor
@TableName("plant_base_info")
public class PlantBaseInfoEntity {
    /**
     * 电站id
     */
    @TableId
    private String id;

    /**
     * 电站名称
     */
    private String plantName;

    /**
     * 逆变器id
     */
    private String inverterId;

    /**
     * 地址
     */
    private String address;

    /**
     * 国家
     */
    private String country;

    /**
     * 省
     */
    private String province;

    /**
     * 市/州
     */
    private String city;

    /**
     * 县/区
     */
    private String area;

    /**
     * 镇/街道
     */
    private String town;

    /**
     * 电站用户名称
     */
    private String plantUserName;

    /**
     * 电站用户电话
     */
    private String userPhone;

    /**
     * 电站类型
     */
    private Integer type;

    /**
     * 质保内容
     */
    private String warrantyContent;

    /**
     * 质保到期日期
     */
    private Date warrantyExpireDate;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 电站流量
     */
    private Double flow;

    /**
     * 是否过保：0->在保，1->过保
     */
    private Integer warrantyStatus;

    /**
     * 项目类型ID
     */
    private Integer projectId;

    /**
     * 项目类型名称
     */
    private String projectName;

    /**
     * 平台类型：0->光云，1->其它
     */
    private Integer platform;
    /**
     * 电站状态
     */
    private Integer plantStatus;

    /**
     * 装机容量
     */
    private Integer plantCapacity;

    /**
     * 今日发电
     */
    private Integer todayElectricity;
    /**
     * 累计发电
     */
    private Integer totalElectricity;

    /**
     * 是否存在工单：0->不存在，1->存在
     */
    @TableField(exist = false)
    private Integer existWorkOrder;

}