package com.botong.workorder.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 电站材料表
 *
 * <AUTHOR> 
 * @since  2023-08-23
 */

@Data
@TableName("plant_material_info")
public class PlantMaterialInfoEntity {
	/**
	 * id
	 */
	@TableId
	private Long id;

	/**
	 * 类型：0->厂家；1->供应商；2->材料类型；3->规格/材质
	 */
	private Integer type;

	/**
	 * 父id
	 */
	private Long pid;

	/**
	 * 采购方式
	 */
	private String purchaseWay;
	/**
	 * 价格
	 */
	private BigDecimal price;

	/**
	 * 创建时间
	 */
	private Date createTime;

	/**
	 * 更新时间
	 */
	private Date updateTime;

	/**
	 * 名称
	 */
	private String name;

}