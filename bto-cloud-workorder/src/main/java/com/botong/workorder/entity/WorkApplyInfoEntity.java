package com.botong.workorder.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.botong.framework.mybatis.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 工单申请
 * <AUTHOR> by zhb on 2023/8/16.
 */

@Data
@TableName("work_apply_info")
@EqualsAndHashCode(callSuper = false)
public class WorkApplyInfoEntity extends BaseEntity {

    /**
     * 提单人名称
     */
    private String upUserName;

    /**
     * 工单id
     */
    private Long workId;

    /**
     * 光云告警信息
     */
    private String alarmStr;

    /**
     * 备注
     */
    private String remark;
    /**
     * 备注类型 0:文字 1:文件
     */
    private Integer remarkType;

}