package com.botong.workorder.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.botong.framework.mybatis.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 工单考核系数表
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-07-03
 */
@EqualsAndHashCode(callSuper=false)
@Data
@TableName("work_evaluat_coefficient")
public class WorkEvaluatCoefficientEntity extends BaseEntity {

	/**
	* 城市名称
	*/
	private String city;

	/**
	* 问题类型{
表：sys_dict_data(dict_type_id = 15)
}
	*/
	private Integer type;

	/**
	* 得分
	*/
	private BigDecimal score;







}