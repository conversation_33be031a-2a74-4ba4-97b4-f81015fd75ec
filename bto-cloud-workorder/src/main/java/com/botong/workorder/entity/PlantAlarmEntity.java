package com.botong.workorder.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 告警视图
 *
 * <AUTHOR> 
 * @since 1.0.0 2024-01-24
 */

@Data
@TableName("v_plant_alarm")
public class PlantAlarmEntity {

	/**
	 * 告警ID
	 */
	@TableId
	private String id;

	/**
	 * 电站名称
	 */
	private String plantName;

	/**
	 * 电站id
	 */
	private String plantUid;

	/**
	 * 告警等级
	 */
	private Long alarmLevel;

	/**
	 * 设备编号
	 */
	private String deviceId;

	/**
	 * 设备类型: 1:逆变器 2:运维器
	 */
	private Long deviceType;

	/**
	 * 告警开始时间
	 */
	private Date startTime;

	/**
	 * 告警内容
	 */
	private String alarmMean;

	/**
	 * 告警状态: 0:未处理 1:已处理 2:状态失效 -1:状态异常
	 */
	private Integer status;

	/**
	 * 数据来源：0:三晶 1:博通
	 */
	private Integer source;

	/**
	 * 市/州
	 */
	private String city;

	/**
	 * 项目专项
	 */
	private Long projectSpecial;

}