package com.botong.workorder.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 告警视图
 *
 * <AUTHOR> 
 * @since 1.0.0 2024-01-24
 */

@Data
@TableName("v_plant_alarm")
public class PlantAlarmEntity {

	@TableId
	private String id;

	private String plantName;

	private String plantUid;

	private Long alarmLevel;

	private String deviceId;

	private Long deviceType;

	private Date startTime;

	private String alarmMean;

	private Integer status;

	private Integer source;

	private String city;

	private Long projectSpecial;

}