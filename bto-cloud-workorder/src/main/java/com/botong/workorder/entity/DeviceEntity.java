package com.botong.workorder.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.*;

import java.util.Date;

/**
 * 设备
 *
 * <AUTHOR>
 * @since 2025-01-03
 */

@Data
@TableName("v_device")
public class DeviceEntity {
    /**
     * 电站uid
     */
    @TableField("plant_uid")
    private String plantId;

    /**
     * 设备编号（设备SN码）||逆变器和非三晶运维器
     */
    private String deviceId;

    /**
     * 运维器通讯模块imei || 等同于三晶 wisdom_device_sn
     */
    private String imei;

    /**
     * 厂家(
     * XK:新科101 ，
     * ZT：正泰，
     * TH：泰和安科技
     * QH:齐辉电气
     * )
     */
    private String manufacturer;

    /**
     * 型号
     */
    private String module;

    /**
     * 数据创建时间
     */
    private Date createTime;

    /**
     * 项目专项(1:户用，2：整县-河源)
     */
    private Integer projectSpecial;
    private Integer deviceType;

}