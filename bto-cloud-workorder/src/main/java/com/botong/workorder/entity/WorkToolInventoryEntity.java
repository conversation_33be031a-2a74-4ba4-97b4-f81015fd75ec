package com.botong.workorder.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.botong.framework.mybatis.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 运维工具库
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-03-11
 */
@EqualsAndHashCode(callSuper=false)
@Data
@TableName("work_tool_inventory")
public class WorkToolInventoryEntity extends BaseEntity {

	/**
	* 类型
	*/
	private Integer type;

	/**
	* 名称
	*/
	private String name;

	/**
	* 单位
	*/
	private String unit;

	/**
	* 数量
	*/
	private Integer quantity;

	/**
	* 入库时间
	*/
	private Date entryTime;

	/**
	* 保管责任人
	*/
	private Long custodian;

	/**
	* 归还或报废时间
	*/
	private Date returnScrapTime;

	/**
	* 核实人员
	*/
	private Long verifier;

	/**
	* 核实日期
	*/
	private Date verifyDate;

	/**
	* 备注
	*/
	private String remarks;

	/**
	* 附件
	*/
	private String attachment;

}