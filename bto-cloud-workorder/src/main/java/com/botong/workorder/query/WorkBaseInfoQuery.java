package com.botong.workorder.query;

import com.botong.framework.common.query.Query;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 工单基础明细表查询
 *
 * <AUTHOR>
 * @since 2023-08-10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Schema(description = "工单基础明细表查询")
public class WorkBaseInfoQuery extends Query {
    /**
     * 故障类型集合
     */
    @Schema(description = "故障类型集合  0->电网，1->设备，2->通讯，3->漏水")
    private List<Integer> alarmTypeList;
    /**
     * 工单状态集合
     */
    @Schema(description = "0->待派单，1->待维修，2->待审核，3->已完成")
    private List<Integer> statusList;

    /**
     * 国家
     */
    @Schema(description = "国家")
    private String country;

    /**
     * 省
     */
    @Schema(description = "省")
    private String province;

    /**
     * 市/州
     */
    @Schema(description = "市/州")
    private String city;

    /**
     * 县/区
     */
    @Schema(description = "县/区")
    private String area;

    /**
     * 镇/街道
     */
    @Schema(description = "镇/街道")
    private String town;
    /**
     * 是否超时  0:否  1:是
     */
    @Schema(description = "是否超时  0:否  1:是")
    private Integer overtimed;

    /**
     * 开始时间
     * 年:yyyy
     * 年月:yyyy-MM
     * 年月日:yyyy-MM-dd
     */
    @Schema(description = "开始时间 格式：年:yyyy  " + "年月:yyyy-MM  " +"年月日:yyyy-MM-dd  ")
    private String startTime;

    /**
     * 结束时间
     * 年:yyyy
     * 年月:yyyy-MM
     * 年月日:yyyy-MM-dd
     */
    @Schema(description = "结束时间 格式：年:yyyy  " + "年月:yyyy-MM  " +"年月日:yyyy-MM-dd  ")
    private String endTime;
}