package com.botong.workorder.query;

import com.botong.framework.common.query.Query;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> by zhb on 2025/5/16.
 */

@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "根据月份获取电站电费电量分析")
public class PlantElectricChargeAnalyzeQuery extends Query {

    @Schema(description = "查询年月，格式：yyyy-MM")
    private String yearMonth;

    @Schema(description = "分析结果")
    private String analyzeResult;

    @Schema(description = "电站名称")
    private String plantName;

    @Schema(description = "城市")
    private String city;

}