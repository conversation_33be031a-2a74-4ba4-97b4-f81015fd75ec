package com.botong.workorder.query;

import com.botong.framework.common.query.Query;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
* 工单考核系数表查询
*
* <AUTHOR>
* @since 1.0.0 2025-07-03
*/
@Data
@EqualsAndHashCode(callSuper = false)
@Schema(description = "工单考核系数表查询")
public class WorkEvaluatCoefficientQuery extends Query {

    @Schema(description = "城市名称")
    private String city;

    @Schema(description = "问题类型")
    private Integer type;
}