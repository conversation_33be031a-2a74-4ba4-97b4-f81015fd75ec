package com.botong.workorder.query;

import com.botong.framework.common.query.Query;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
* 运维工具库查询
*
* <AUTHOR> 
* @since 1.0.0 2025-03-11
*/
@Data
@EqualsAndHashCode(callSuper = false)
@Schema(description = "运维工具库查询")
public class WorkToolInventoryQuery extends Query {

    @Schema(description = "名称")
    private String name;

    @Schema(description = "保管责任人姓名")
    private String custodianName;

    @Schema(description = "保管责任人ID")
    private Long custodian;

}