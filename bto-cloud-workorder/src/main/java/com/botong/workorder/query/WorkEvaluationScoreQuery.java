package com.botong.workorder.query;

import com.botong.framework.common.query.Query;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 工作订单评估评分查询
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-07-04
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Schema(description = "工作订单评估评分查询")
public class WorkEvaluationScoreQuery extends Query {
    
    @Schema(description = "开始时间")
    private String startTime;
    
    @Schema(description = "结束时间")
    private String endTime;
    
    @Schema(description = "工程师ID")
    private Long repairId;
    
    @Schema(description = "工程师姓名")
    private String repairName;
    
    @Schema(description = "城市名称")
    private String city;
}
