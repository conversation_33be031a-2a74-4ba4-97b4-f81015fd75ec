package com.botong.workorder.query;

import com.botong.framework.common.query.Query;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
* 工单审核管理查询
*
* <AUTHOR>
* @since 1.0.0 2023-09-04
*/
@Data
@EqualsAndHashCode(callSuper = false)
@Schema(description = "工单审核管理查询")
public class WorkExamineInfoQuery extends Query {

    @Schema(description = "工单id")
    private Long workId;
}