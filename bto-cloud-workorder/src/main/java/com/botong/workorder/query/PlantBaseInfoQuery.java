package com.botong.workorder.query;

import com.botong.framework.common.query.Query;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 电站信息查询参数
 * <AUTHOR> by zhb on 2023/8/16.
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Schema(description = "电站明细表查询")
public class PlantBaseInfoQuery extends Query {

    @Schema(description = "电站id或名称")
    private String param;

    @Schema(description = "电站平台 0->光云 1->其他 ")
    private Integer platform;

    @Schema(description = "电站状态")
    private List<Integer> plantStatus;

    @Schema(description = "项目类型ID")
    private List<Long> projectId;
}