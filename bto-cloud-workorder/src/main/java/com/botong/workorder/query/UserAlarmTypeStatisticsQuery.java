package com.botong.workorder.query;

import com.botong.framework.common.utils.DateUtils;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2025/3/20 16:19
 */
@Data
@Schema(description = "个人工单汇总查询条件")
public class UserAlarmTypeStatisticsQuery {
    @Schema(description = "开始日期" + DateUtils.DATE_PATTERN)
    private String startTime;

    @Schema(description = "结束日期" + DateUtils.DATE_PATTERN)
    private String endTime;

    @Schema(description = "用户名")
    private String username;

}