package com.botong.workorder.query;

import com.botong.framework.common.query.Query;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Range;
import org.springframework.validation.annotation.Validated;

import java.util.List;

/**
 * 工单流程管理查询
 * <AUTHOR> by zhb on 2023/8/21.
 */

@Data
@EqualsAndHashCode(callSuper = false)
@Validated
@Schema(description = "工单流程管理查询")
public class WorkOrderProcessManageQuery extends Query {
    @Schema(description = "提单人姓名")
    private String upUserName;
    @Schema(description = "工单id")
    private Long workId;
    @Schema(description = "工单状态")
    private List<Integer> status;

    @Schema(description = "故障类型：0->电网，1->设备，2->通讯，3->漏水 4->其他")
    @Range(min = 0, max = 50, message = "故障类型不正确")
    private Integer alarmType;

    @Schema(description = "电站编号或电站名称")
    private String plantIdOrName;

    @Schema(description = "工单开始时间：yyyy-MM-dd")
    private String startTime;

    @Schema(description = "工单结束时间：yyyy-MM-dd")
    private String endTime;

    @Schema(description = "是否超时  0->未超时  1->超时")
    private List<Integer> overtimed;

    @Schema(description = "电站地址")
    private String address;

    @Schema(description = "国家")
    private List<String> country;

    @Schema(description = "省")
    private List<String> province;

    @Schema(description = "市/州")
    private List<String> city;

    @Schema(description = "县/区")
    private List<String> area;

    @Schema(description = "镇/街道")
    private List<String> town;
    @Schema(description = "电站类型")
    private List<Long> plantTypeList;
    @Schema(description = "是否查询个人 0->否 1->是")
    private Integer personal;

    @Schema(description = "维修人")
    private String maintenancePerson;

    @Schema(description = "平台类型")
    private Integer platform;

    {
        personal = 0;
    }
}
