package com.botong.workorder.query;

import com.botong.framework.common.query.Query;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
* 二次增值台账表查询
*
* <AUTHOR> 
* @since 1.0.0 2025-02-24
*/
@Data
@EqualsAndHashCode(callSuper = false)
@Schema(description = "二次增值台账表查询")
public class WorkChargeQuery extends Query {

    @Schema(description = "工单ID")
    private Integer workOrderId;

    @Schema(description = "电站名称")
    private String plantName;

    @Schema(description = "是否关联工单，1表示关联，0表示不关联")
    private Integer isAssociated;

    @Schema(description = "收费类型")
    private Integer chargeType;

    @Schema(description = "开始时间：yyyy-MM-dd")
    private String startTime;

    @Schema(description = "结束时间：yyyy-MM-dd")
    private String endTime;

}