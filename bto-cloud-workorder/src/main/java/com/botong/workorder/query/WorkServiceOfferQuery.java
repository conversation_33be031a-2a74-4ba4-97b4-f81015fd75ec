package com.botong.workorder.query;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.botong.framework.common.query.Query;

import java.util.Date;

/**
* 工单服务报价表查询
*
* <AUTHOR> 
* @since 1.0.0 2023-08-23
*/
@Data
@EqualsAndHashCode(callSuper = false)
@Schema(description = "工单服务报价表查询")
public class WorkServiceOfferQuery extends Query {
}