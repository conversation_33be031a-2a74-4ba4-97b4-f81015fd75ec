package com.botong.workorder.query;

import com.botong.framework.common.query.Query;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
* 工单费用账单查询
*
* <AUTHOR> 
* @since 1.0.0 2025-01-14
*/
@Data
@EqualsAndHashCode(callSuper = false)
@Schema(description = "工单费用账单查询")
public class BillWorkExpenseQuery extends Query {

    @Schema(description = "工单ID")
    private Integer workId;

}