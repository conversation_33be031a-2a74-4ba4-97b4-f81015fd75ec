package com.botong.workorder.query;

import com.botong.framework.common.query.Query;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 设备查询
 *
 * <AUTHOR>
 * @since 2025-01-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Schema(description = "设备查询")
public class DeviceQuery extends Query {
    @Schema(description = "电站id")
    private String plantId;
    @Schema(description = "设备编号（设备SN码）||逆变器和非三晶运维器")
    private String deviceId;
    @Schema(description = "设备类型 1:逆变器 2:运维器")
    private Integer deviceType;
}