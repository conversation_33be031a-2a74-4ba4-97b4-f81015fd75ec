package com.botong.workorder.query;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Range;
import org.springframework.validation.annotation.Validated;

import javax.validation.constraints.Pattern;
import java.util.List;

/**
 * 工单基础明细表查询
 *
 * <AUTHOR>
 * @since 2023-08-10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Schema(description = "工单基础明细表查询")
@Validated
public class WorkOrderStatisticQuery {
    /**
     * 故障类型集合
     */
    @Schema(description = "故障类型集合  0->电网，1->设备，2->通讯，3->漏水")
    private List<Integer> alarmType;
    /**
     * 工单状态集合
     */
    @Schema(description = "0->待派单，1->待维修，2->待审核，3->已完成")
    private List<Integer> status;

    @Schema(description = "国家")
    private List<String> country;

    @Schema(description = "省")
    private List<String> province;

    @Schema(description = "市/州")
    private List<String> city;

    @Schema(description = "县/区")
    private List<String> area;

    @Schema(description = "镇/街道")
    private List<String> town;

    @Schema(description = "区域查询条件")
    private String regionCondition;

    /**
     * 是否超时  0:否  1:是
     */
    @Schema(description = "是否超时  0:否  1:是 2:即将超时")
    private List<Integer> overtimed;

    /**
     * 开始时间
     * 年:yyyy
     * 年月:yyyy-MM
     * 年月日:yyyy-MM-dd
     */
    @Schema(description = "开始时间 格式：年:yyyy  " + "年月:yyyy-MM  " + "年月日:yyyy-MM-dd  ")
    private String startTime;

    /**
     * 结束时间
     * 年:yyyy
     * 年月:yyyy-MM
     * 年月日:yyyy-MM-dd
     */
    @Schema(description = "结束时间 格式：年:yyyy  " + "年月:yyyy-MM  " + "年月日:yyyy-MM-dd  ")
    private String endTime;

    @Range(min = 0, max = 1, message = "personal错误")
    @Schema(description = "是否查询个人 0->否 1->是")
    private Integer personal;

    @Schema(description = "字典类型ID 15->按故障状态统计  16->按工单状态统计", example = "15", required = true)
    @Range(min = 15, max = 16, message = "dictTypeId错误")
    Long dictTypeId;

    @Schema(description = "统计类型 country->按各province展示，province->按各city展示,city->按各area展示,area->按各town展示", example = "province")
    @Pattern(regexp = "^(country|province|city|area)$", message = "type错误")
    String type;

    @Schema(description = "平台类型")
    private Integer platform;
    @Schema(description = "电站id")
    private String plantId;
    @Schema(description = "项目类型")
    private List<Long> plantType;

    {
        personal = 0;
        regionCondition = "";
    }
}