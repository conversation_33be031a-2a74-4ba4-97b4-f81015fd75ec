package com.botong.workorder.query;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.botong.framework.common.query.Query;
import com.botong.framework.common.utils.DateUtils;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/3/4 15:13
 */
@Data
public class LowPowerQuery extends Query {
    private static final long serialVersionUID = -6677164462786731673L;
    @Schema(description = "开始日期" + DateUtils.DATE_PATTERN)
    @NotNull
    private String startTime;

    @Schema(description = "结束日期" + DateUtils.DATE_PATTERN)
    @NotNull
    private String endTime;

    @Schema(description = "项目类型ID")
    private List<Long> projectId;

    @Schema(description = "城市")
    private String city;

    @Schema(description = "电站名称")
    private String plantName;

    @Schema(description = "状态")
    private Integer status;

    @Schema(description = "所在城市发电率 大于")
    private BigDecimal greaterThan;

    @Schema(description = "所在城市发电率 小于")
    private BigDecimal lessThan;

}
