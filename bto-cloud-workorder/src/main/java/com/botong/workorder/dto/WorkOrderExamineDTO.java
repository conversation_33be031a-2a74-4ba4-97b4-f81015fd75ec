package com.botong.workorder.dto;

import com.botong.framework.common.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Range;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/8/21 15:36
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "工单审核")
public class WorkOrderExamineDTO {

    @NotNull(message = "订单集合不能为空")
    @Schema(description = "工单集合")
    List<Long> workIdList;


    @Schema(description = "审核意见")
    private String examineOpinion;

    @Schema(description = "审核评价")
    private Double evaluate;

    @NotNull(message = "审核结果不能为空")
    @Range(min = 0, max = 2)
    @Schema(description = "审核结果   0->未审核 1->已驳回 2-> 已通过")
    private Integer auditResult;

    @NotNull(message = "工单状态不能为空")
    @Range(min = 0, max = 4)
    @Schema(description = "0->待派单，1->待维修，2->待审核，3->已完成 4->挂起")
    private Integer status;

    @Schema(description = "是否挂起：0->否  1->是")
    @Range(min = 0, max = 1)
    @NotNull
    private Integer suspended;

    @Schema(description = "挂起过期时间")
    @DateTimeFormat(pattern = DateUtils.DATE_TIME_PATTERN)
    @JsonFormat(pattern = DateUtils.DATE_TIME_PATTERN)
    private Date suspendExpiration;

}

