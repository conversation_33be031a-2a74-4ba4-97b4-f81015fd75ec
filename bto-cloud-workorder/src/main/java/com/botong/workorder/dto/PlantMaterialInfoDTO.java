package com.botong.workorder.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "电站材料")
public class PlantMaterialInfoDTO implements Serializable {
	private static final long serialVersionUID = 1L;

	@NotNull(message = "材料id不能为空")
	@Schema(description = "材料id")
	private Long id;

	@NotNull(message = "材料数量不能为空")
	@Schema(description = "材料数量")
	private Integer count;

}