package com.botong.workorder.dto;

import com.botong.workorder.vo.BillWorkExpenseVO;
import com.botong.workorder.vo.WorkChargeVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Range;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/8/23 10:52
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@Schema(description = "工单维修信息")
public class WorkOperationInfoDTO {

    @NotNull(message = "工单id不能为空")
    @Schema(description = "工单id")
    private Long workId;

    @Schema(description = "未修复原因")
    private String unrepairedCause;

    @Schema(description = "故障原因")
    @NotNull
    private Integer alarmCause;

    @Schema(description = "维修详情")
    private String opCause;

    @Schema(description = "优先级")
    private Integer priority;

    @Schema(description = "维修后照片")
    private String repairPhotoPath;

    @Schema(description = "故障现场照片")
    private String faultPhotoPath;

    @Schema(description = "故障现象：0->电网侧异常，1->发电侧异常，2->通讯异常，3->漏水，4->其它")
    @Range(min = 0, max = 50, message = "故障现象不正确")
    private Integer alarmInfo;

    @Schema(description = "故障设备：0->无故障，1->逆变器，2->运维器，3->断路器，4->失压开关，5->防雷器，6->结构件，7->电缆接插件，8->电表，9->电缆，10->光伏组件 11->通讯设备/模块")
    @Range(min = 0, max = 50, message = "故障设备不正确")
    private Integer alarmDevice;

    @Valid
    List<PlantMaterialInfoDTO> plantMaterialInfoList;

    @Range(min = 0, max = 3)
    @Schema(description = "0->待派单，1->待维修，2->待审核，3->已完成")
    private Integer status;

    @NotNull(message = "维修状态不能为空")
    @Range(min = 0, max = 1)
    @Schema(description = "维修状态 0：已修复 1 ： 未修复")
    private Integer repairStatus;

    @Schema(description = "是否过保：0->在保，1->过保")
    private Integer warrantyStatus;

    @Schema(description = "故障类型：0->电网，1->设备，2->通讯，3->漏水，4->其他")
    @Range(min = 0, max = 50, message = "故障类型不正确")
    private Integer alarmType;

    @Schema(description = "额外费用")
    private List<BillWorkExpenseVO> billWorkExpenseList;

    @Schema(description = "是否挂起：0->否  1->是")
    @Range(min = 0, max = 1)
    private Integer suspended;

    @Schema(description = "二次增值台账表")
    private List<WorkChargeVO> workChargeList;

    @Schema(description = "是否有同行人：0->否  1->是")
    @Range(min = 0, max = 1)
    private Integer hasCompanion;

    @Schema(description = "同行人")
    private List<Long> companion;

}
