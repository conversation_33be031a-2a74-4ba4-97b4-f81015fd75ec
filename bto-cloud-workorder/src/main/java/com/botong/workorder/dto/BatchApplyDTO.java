package com.botong.workorder.dto;

import com.botong.framework.common.utils.DateUtils;
import com.botong.workorder.annotation.TimeoutDays;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> by zhb on 2024/10/30.
 */
@Data
@Schema(description = "批量申请工单")
public class BatchApplyDTO {

    @Schema(description = "电站编号")
    @NotNull(message = "电站编号不能为空")
    private String plantId;

    @Schema(description = "提单人姓名", hidden = true)
    private String upUserName;

    @Schema(description = "提单时间", hidden = true)
    @JsonFormat(pattern = DateUtils.DATE_PATTERN)
    private Date upWorkTime;

    @Schema(description = "工单来源：0->光伏系统，1->人工导入，2->客户投诉", hidden = true)
    @Range(min = 0, max = 2, message = "工单来源不正确")
    private Integer source;

    @Schema(description = "电站归属机构ID")
    private Integer plantType;

    @Schema(description = "故障现象：0->电网侧异常，1->发电侧异常，2->通讯异常，3->漏水，4->其它")
    @Range(min = 0, max = 50, message = "故障现象不正确")
    private Integer alarmInfo;

    @Schema(description = "故障类型：0->电网，1->设备，2->通讯，3->漏水，4->其他")
    @Range(min = 0, max = 50, message = "故障类型不正确")
    private Integer alarmType;

    @Schema(description = "故障设备：0->无故障，1->逆变器，2->运维器，3->断路器，4->失压开关，5->防雷器，6->结构件，7->电缆接插件，8->电表，9->电缆，10->光伏组件 11->通讯设备/模块")
    @Range(min = 0, max = 50, message = "故障设备不正确")
    private Integer alarmDevice;

    @Schema(description = "故障时间")
    @NotNull(message = "故障时间不能为空")
    @JsonFormat(pattern = DateUtils.DATE_PATTERN)
    private Date alarmTime;

    @Schema(description = "故障等级：0->一般故障，1->严重故障，2->重大故障")
    @Range(min = 0, max = 2, message = "故障等级不正确")
    private Integer alarmLevel;

    @Schema(description = "平台类型 0->光云 1->其他", hidden = true)
    @Range(min = 0, max = 1)
    private Integer platform;

    @NotNull(message = "维修人员集合不能为空")
    @Schema(description = "维修人员集合")
    List<Long> staffList;

    @JsonFormat(pattern = DateUtils.DATE_PATTERN)
    @NotNull(message = "计划修复时间不能为空")
    @Schema(description = "计划修复时间，格式: yyyy-MM-dd")
    Date preRepairTime;

    @TimeoutDays
    @NotNull(message = "超时警告阈值不能为空")
    @Schema(description = "超时警告阈值：1->超时1天，3->超时3天，5->超时5天，7->超时7天")
    Integer timeoutWarningThreshold;

    @NotNull(message = "备注类型不能为空")
    @Range(min = 0, max = 1, message = "备注类型不正确")
    @Schema(description = "备注类型 0->文字 1->文件")
    private Integer remarkType;

    @Schema(description = "备注")
    private String remark;


}