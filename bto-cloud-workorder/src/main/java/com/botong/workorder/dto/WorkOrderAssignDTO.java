package com.botong.workorder.dto;

import com.botong.framework.common.utils.DateUtils;
import com.botong.workorder.annotation.TimeoutDays;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/8/21 15:36
 */
@Data
@AllArgsConstructor
@Schema(description = "工单派遣")
public class WorkOrderAssignDTO {

    @NotNull(message = "工单集合不能为空")
    @Schema(description = "工单集合")
    List<Long> workIdList;

    @NotNull(message = "维修人员集合不能为空")
    @Schema(description = "维修人员集合")
    List<Long> staffList;

    @JsonFormat(pattern = DateUtils.DATE_PATTERN)
    @NotNull(message = "计划修复时间不能为空")
    @Schema(description = "计划修复时间，格式: yyyy-MM-dd")
    Date preRepairTime;

    @TimeoutDays
    @NotNull(message = "超时警告阈值不能为空")
    @Schema(description = "超时警告阈值：1->超时1天，3->超时3天，5->超时5天，7->超时7天")
    Integer timeoutWarningThreshold;


}

