package com.botong.workorder.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.nacos.shaded.com.google.common.collect.ArrayListMultimap;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.botong.api.module.message.SmsApi;
import com.botong.api.module.quartz.RunJobApi;
import com.botong.api.module.quartz.dto.RunSmsJobDTO;
import com.botong.api.module.system.SysParamsApi;
import com.botong.api.module.system.SysUserApi;
import com.botong.api.module.system.vo.OrgWithUserVO;
import com.botong.api.module.system.vo.RepairUserVO;
import com.botong.framework.common.cache.RedisCache;
import com.botong.framework.common.cache.RedisKeys;
import com.botong.framework.common.constant.BtoConstant;
import com.botong.framework.common.constant.SmsTemplateCode;
import com.botong.framework.common.excel.ExcelFinishCallBack;
import com.botong.framework.common.exception.ServerException;
import com.botong.framework.common.page.PageResult;
import com.botong.framework.common.utils.*;
import com.botong.framework.mybatis.service.InitDictionaryTransService;
import com.botong.framework.mybatis.service.impl.BaseServiceImpl;
import com.botong.framework.security.user.SecurityUser;
import com.botong.framework.security.user.UserDetail;
import com.botong.workorder.convert.*;
import com.botong.workorder.dao.WorkBaseInfoDao;
import com.botong.workorder.dao.WorkOrderProcessManageDao;
import com.botong.workorder.dto.BatchApplyDTO;
import com.botong.workorder.dto.WorkOperationInfoDTO;
import com.botong.workorder.dto.WorkOrderAssignDTO;
import com.botong.workorder.dto.WorkOrderExamineDTO;
import com.botong.workorder.entity.*;
import com.botong.workorder.enums.WorkOrderOvertimeEnum;
import com.botong.workorder.enums.WorkOrderPlatformEnum;
import com.botong.workorder.enums.WorkOrderStatusEnum;
import com.botong.workorder.query.UserAlarmTypeStatisticsQuery;
import com.botong.workorder.query.WorkOrderProcessManageQuery;
import com.botong.workorder.service.*;
import com.botong.workorder.utils.PageUtils;
import com.botong.workorder.vo.*;
import com.fhs.trans.service.impl.TransService;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.jetbrains.annotations.Nullable;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> by zhb on 2023/8/15.
 */
@Slf4j
@Service
@AllArgsConstructor
public class WorkOrderProcessManageServiceImpl extends BaseServiceImpl<WorkOrderProcessManageDao, WorkOrderProcessManageViewEntity> implements WorkOrderProcessManageService, InitializingBean {

    private final WorkBaseInfoService workBaseInfoService;
    private final WorkApplyInfoService workApplyInfoService;
    private final PlantBaseInfoService plantBaseInfoService;
    private final WorkServiceOfferService workServiceOfferService;
    private final WorkBaseInfoDao workBaseInfoDao;
    private final SysUserApi sysUserApi;
    private final SmsApi smsApi;
    private final SysParamsApi sysParamsApi;
    private final RunJobApi runJobApi;
    private final WorkOperationInfoService workOperationInfoService;
    private final WorkOperationPersonInfoService workOperationPersonInfoService;
    private final InitDictionaryTransService initDictionaryTransService;
    private final TransService transService;
    private final WorkExamineInfoService workExamineInfoService;
    private final RedisCache redisCache;
    private final BillWorkExpenseService billWorkExpenseService;
    private final WorkChargeService workChargeService;

    private final String COUNT_SQL = "COUNT(*) AS totalCount," +
            "COUNT(CASE WHEN YEAR(create_time) = YEAR('%s') AND MONTH(create_time) = MONTH('%s') THEN 1 END) AS monthCount, " +
            "COUNT(CASE WHEN YEAR(create_time) = YEAR('%s') THEN 1 END) AS yearCount";


    @Override
    public List<Map<String, Object>> getUserAlarmTypeStatistics(UserAlarmTypeStatisticsQuery query) {
        HashMap<String, Object> params = new HashMap<>();
        UserDetail user = SecurityUser.getUser();
        // params.put(BtoConstant.DATA_SCOPE, getWorkOrderDataScope("w4", null, "up_user_id"));
        params.put("query", query);
        params.put("scope", user.getDataScopeList());
        params.put("creator", user.getId());
        List<UserAlarmTypeStatisticsVO> list = baseMapper.getUserAlarmTypeStatistics(params);
        List<Map<String, Object>> maps = processAlarmStatistics(list);

        return maps;
    }


    public List<Map<String, Object>> processAlarmStatistics(List<UserAlarmTypeStatisticsVO> list) {
        // 按用户名分组
        List<Map<String, Object>> res = list.stream()
                .collect(Collectors.groupingBy(UserAlarmTypeStatisticsVO::getUsername))
                .entrySet().stream()
                .map(entry -> {
                    // 创建每个人的数据
                    Map<String, Object> userData = new LinkedHashMap<>();  // 使用 LinkedHashMap 保证插入顺序
                    String username = entry.getKey();
                    UserAlarmTypeStatisticsVO sample = entry.getValue().get(0);  // 获取第一个对象作为示例
                    userData.put("用户名", username);
                    userData.put("岗位", sample.getPostName());

                    // 按报警类型统计数量
                    Map<String, Integer> alarmTypeCounts = entry.getValue().stream()
                            .collect(Collectors.toMap(
                                    UserAlarmTypeStatisticsVO::getAlarmType,
                                    UserAlarmTypeStatisticsVO::getCount
                            ));
                    // 替换 "全部" 为 "未完成"
                    if (alarmTypeCounts.containsKey("全部")) {
                        alarmTypeCounts.put("未完成", alarmTypeCounts.remove("全部"));
                    }
                    // 将报警类型数据添加到 userData 中，确保报警类型按照顺序添加
                    alarmTypeCounts.forEach(userData::put);

                    // 计算总计
                    int totalCount = alarmTypeCounts.values().stream()
                            .mapToInt(Integer::intValue)
                            .sum();
                    userData.put("合计", totalCount);

                    return userData;
                })
                .filter(userData -> (int) userData.get("合计") > 0)  // 过滤掉合计为0的数据
                .sorted(Comparator.comparingInt((Map<String, Object> map) -> (int) map.get("合计")).reversed())  // 按总计降序
                .collect(Collectors.toList());

        if(res.size() > 1){
            // 计算所有字段的合计
            Map<String, Object> totalRow = new LinkedHashMap<>();
            totalRow.put("用户名", "合计");

            // 获取所有字段（报警类型）的键
            Set<String> allKeys = res.stream()
                    .flatMap(map -> map.keySet().stream())
                    .filter(key -> !"用户名".equals(key) && !"岗位".equals(key) && !"合计".equals(key))  // 排除用户名、岗位和总计字段
                    .collect(Collectors.toSet());

            // 计算合计行的数据
            for (String key : allKeys) {
                int sum = res.stream()
                        .mapToInt(map -> (int) map.getOrDefault(key, 0))
                        .sum();
                totalRow.put(key, sum);
            }

            // 计算总计字段的合计
            int totalSum = res.stream()
                    .mapToInt(map -> (int) map.getOrDefault("合计", 0))
                    .sum();
            totalRow.put("合计", totalSum);

            // 添加总计行到结果中
            res.add(totalRow);
        }
        return res;
    }

    @Override
    public List<Map<String, Object>> monthlyOpsWorkOrderSummary(DateTime start, DateTime end) {

        HashMap<String, Object> params = new HashMap<>();
        params.put("startTime", start);
        params.put("endTime", end);
        List<MonthlyOpsWorkOrderSummaryVO> list = baseMapper.monthlyOpsWorkOrderSummary(params);
        List<Map<String, Object>> res = list.stream()
                .collect(Collectors.groupingBy(MonthlyOpsWorkOrderSummaryVO::getCity))
                .entrySet().stream()
                .map(entry -> {
                    Map<String, Object> cityData = new LinkedHashMap<>();
                    cityData.put("城市", entry.getKey());

                    Map<String, Integer> alarmTypeCounts = entry.getValue().stream()
                            .collect(Collectors.toMap(
                                    MonthlyOpsWorkOrderSummaryVO::getAlarmType,
                                    MonthlyOpsWorkOrderSummaryVO::getCount
                            ));

                    // **替换 "全部" 为 "未完成"**
                    if (alarmTypeCounts.containsKey("全部")) {
                        alarmTypeCounts.put("未完成", alarmTypeCounts.remove("全部"));
                    }
                    cityData.putAll(alarmTypeCounts);

                    int totalCount = entry.getValue().stream()
                            .mapToInt(MonthlyOpsWorkOrderSummaryVO::getCount)
                            .sum();

                    cityData.put("合计", totalCount);

                    return cityData;
                })
                // 过滤掉 0 的数据
                .filter(cityData -> (int) cityData.get("合计") > 0)
                // 降序
                .sorted(Comparator.comparingInt((Map<String, Object> map) -> (int) map.get("合计")).reversed())
                .collect(Collectors.toList());

        // 统计未完成
        List<Map<String, Object>> unfinishedWorkOrderCount = baseMapper.getUnfinishedWorkOrderCount();
        Map<String, Long> unfinishedMap = unfinishedWorkOrderCount.stream()
                .collect(Collectors.toMap(
                        m -> (String) m.get("城市"),
                        m -> (Long) m.get("数量")
                ));
        // 遍历 res 设置“目前未完成工单”
        res.forEach(cityData -> {
            String cityName = (String) cityData.get("城市");
            Long count = unfinishedMap.getOrDefault(cityName, 0L);
            cityData.put("目前未完成工单", count);
        });

        // **计算所有字段的合计**
        Map<String, Object> totalRow = new HashMap<>();
        totalRow.put("城市", "合计");

        // **获取所有字段**
        Set<String> allKeys = res.stream()
                .flatMap(map -> map.keySet().stream())
                .filter(key -> !"城市".equals(key))  // 排除 "城市" 字段
                .collect(Collectors.toSet());

        for (String key : allKeys) {
            int sum = res.stream()
                    .mapToInt(map -> {
                        Object value = map.getOrDefault(key, 0);
                        if (value instanceof Number) {
                            return ((Number) value).intValue();
                        } else {
                            return 0;
                        }
                    })
                    .sum();
            totalRow.put(key, sum);
        }


        // **添加总计行**
        res.add(totalRow);
        res.replaceAll(cityData -> moveKeyToFirst(cityData, "目前未完成工单"));
        return res;
    }

    public static Map<String, Object> moveKeyToFirst(Map<String, Object> map, String keyToMoveFirst) {
        LinkedHashMap<String, Object> result = new LinkedHashMap<>();
        if (map.containsKey(keyToMoveFirst)) {
            result.put(keyToMoveFirst, map.get(keyToMoveFirst));
        }
        map.forEach((key, value) -> {
            if (!key.equals(keyToMoveFirst)) {
                result.put(key, value);
            }
        });
        return result;
    }

    @Override
    public List<CityStatsStatistics> getCityStatsStatisticsByCity() {
        // 获取电站统计信息
        List<CityStatsStatistics> plantStatisticsInfos = baseMapper.getPlantStatisticsInfo();
        // 获取告警统计信息
        List<CityStatsStatistics> alarmStatisticsInfos = baseMapper.getAlarmStatisticsInfo();
        for (CityStatsStatistics plantStatisticsInfo : plantStatisticsInfos) {
            for (CityStatsStatistics alarmStatisticsInfo : alarmStatisticsInfos) {
                if (plantStatisticsInfo.getCity().equals(alarmStatisticsInfo.getCity())) {
                    plantStatisticsInfo.setLowVoltageFaults(alarmStatisticsInfo.getLowVoltageFaults());
                    plantStatisticsInfo.setPowerOutage(alarmStatisticsInfo.getPowerOutage());
                    plantStatisticsInfo.setCommunicationFaults(alarmStatisticsInfo.getCommunicationFaults());
                }
            }
        }
        // 获取工单统计信息
        List<CityStatsStatistics> workOrderStatisticsInfos = baseMapper.getWorkOrderStatisticsInfo();
        for (CityStatsStatistics plantStatisticsInfo : plantStatisticsInfos) {
            for (CityStatsStatistics workOrderStatisticsInfo : workOrderStatisticsInfos) {
                if (plantStatisticsInfo.getCity().equals(workOrderStatisticsInfo.getCity())) {
                    plantStatisticsInfo.setTodayOrders(workOrderStatisticsInfo.getTodayOrders());
                    plantStatisticsInfo.setYesterdayOrders(workOrderStatisticsInfo.getYesterdayOrders());
                    plantStatisticsInfo.setYesterdayCompleted(workOrderStatisticsInfo.getYesterdayCompleted());
                    plantStatisticsInfo.setTodayOvertime(workOrderStatisticsInfo.getTodayOvertime());
                }
            }
        }
        return plantStatisticsInfos;
    }

    @Override
    public void exportCityStats() {
        List<CityStatsStatistics> cityStatsStatisticsByCity = this.getCityStatsStatisticsByCity();
        ExcelUtils.excelExport(CityStatsStatistics.class, "city_stats" + DateUtils.format(new Date()), null, cityStatsStatisticsByCity);
    }

    @Override
    public void unsuspend() {
        List<WorkOrderProcessManageViewEntity> orders = this.lambdaQuery()
                .eq(WorkOrderProcessManageViewEntity::getStatus, WorkOrderStatusEnum.SUSPENDED.getValue())
                .and(w -> {
                    w.eq(WorkOrderProcessManageViewEntity::getPlantStatus, 1)
                            .or()
                            .lt(WorkOrderProcessManageViewEntity::getSuspendExpiration, new Date());
                })
                .list();

        if (CollUtil.isEmpty(orders)) {
            return;
        }
        List<Long> ids = orders.stream().map(WorkOrderProcessManageViewEntity::getWorkId).collect(Collectors.toList());
        workBaseInfoService.lambdaUpdate()
                .in(WorkBaseInfoEntity::getId, ids)
                .set(WorkBaseInfoEntity::getStatus, WorkOrderStatusEnum.TO_BE_REPAIR.getValue())
                .update();

    }

    @Override
    public List<StatisticsByAlarmTimeVO> statisticsByAlarmTime(String year, String plantId) {
        Map<String, Object> params = new HashMap<>();
        // 数据权限
        params.put(BtoConstant.DATA_SCOPE, getWorkOrderDataScope(null, null, "up_user_id"));
        params.put("year", year);
        params.put("plantId", plantId);
        List<StatisticsByAlarmTimeVO> result = baseMapper.statisticsByAlarmTime(params);
        return result;
    }

    @Override
    public Map<String, Object> getWorkOrderStatistics(List<Long> scopeList, String startTime, String endTime) {
        Map<String, Object> params = new HashMap<>();
        // 数据权限
        params.put(BtoConstant.DATA_SCOPE, getWorkOrderDataScope(null, null, "up_user_id"));
        params.put("scopeList", scopeList);
        params.put("startTime", startTime);
        params.put("endTime", endTime);
        return baseMapper.getWorkOrderStatistics(params);
    }

    @Override
    public Map<String, Object> count(String date, List<Long> scopeList) {
        QueryWrapper<WorkOrderProcessManageViewEntity> wrapper = new QueryWrapper<>();
        wrapper.select(
                String.format(COUNT_SQL, date, date, date)
        );
        wrapper.lambda()
                .le(WorkOrderProcessManageViewEntity::getCreateTime, date)
                .in(CollUtil.isNotEmpty(scopeList), WorkOrderProcessManageViewEntity::getOrgId, scopeList)
        ;

        getWorkOrderDataScopeWrapper(wrapper.lambda(), null, null, "up_user_id");
        return this.getMap(wrapper);
    }

    /**
     * @param vo 报修实体
     * @return
     */

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long workOrderApply(WorkOrderProcessManageVO vo) {
        PlantBaseInfoEntity plantBaseInfo = getPlantBaseInfoEntity(vo);
        List<Integer> status = WorkOrderStatusEnum.getAllValue();
        status.remove(WorkOrderStatusEnum.COMPLETED.getValue());
        List<String> plantIds = baseMapper.getListByStatus(status);
        // 查询所有已创建工单且未完成的电站
        if (plantIds.contains(plantBaseInfo.getId())) {
            throw new ServerException(" 存在未完成工单，不允许重复创建工单，请重新操作" + " 电站名：" + plantBaseInfo.getPlantName() + "电站ID：" + plantBaseInfo.getId());
        }

        if (StringUtils.isNotEmpty(vo.getWarrantyContent())) {
            plantBaseInfo.setWarrantyContent(vo.getWarrantyContent());
        }
        if (ObjectUtil.isEmpty(vo.getWarrantyExpireDate())) {
            Date createTime = plantBaseInfo.getCreateTime();
            if (createTime != null) {
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(createTime);
                calendar.add(Calendar.YEAR, 5);
                Date newDate = calendar.getTime();
                vo.setWarrantyExpireDate(newDate);
            } else {
                // 非光云电站默认过期
                vo.setWarrantyExpireDate(new Date());
            }
        }
        // 检查WarrantyExpireDate是否为null
        if (plantBaseInfo.getWarrantyExpireDate() != null) {
            // 更新质保状态
            if (new Date().getTime() > plantBaseInfo.getWarrantyExpireDate().getTime()) {
                plantBaseInfo.setWarrantyStatus(1);
            } else {
                plantBaseInfo.setWarrantyStatus(0);
            }
        }
        plantBaseInfoService.updateById(plantBaseInfo);

        WorkBaseInfoEntity workBaseInfoEntity = WorkOrderProcessManageConvert.INSTANCE.convertToWorkBaseInfoEntity(vo);
        workBaseInfoEntity.setStatus(0);
        // 保存工单基础信息
        workBaseInfoService.save(workBaseInfoEntity);

        Long workId = workBaseInfoEntity.getId();
        WorkApplyInfoEntity workApplyInfoEntity = WorkOrderProcessManageConvert.INSTANCE.convertToWorkApplyInfoEntity(vo);
        workApplyInfoEntity.setWorkId(workId);
        workApplyInfoEntity.setRemark(vo.getRemark());
        workApplyInfoEntity.setUpUserName(SecurityUser.getUsername());
        // 保存工单申请信息
        workApplyInfoService.save(workApplyInfoEntity);

        // 初始化运维数据
        WorkOperationInfoEntity workOperationInfoEntity = new WorkOperationInfoEntity();
        workOperationInfoEntity.setWorkId(workBaseInfoEntity.getId());
        workOperationInfoService.save(workOperationInfoEntity);

        // 初始化审核数据
        WorkExamineInfoEntity workExamineInfoEntity = new WorkExamineInfoEntity();
        workExamineInfoEntity.setWorkId(workBaseInfoEntity.getId());
        workExamineInfoService.save(workExamineInfoEntity);

        // 派单
        WorkOrderAssignDTO assignDTO = vo.getWorkOrderAssignDTO();
        if (ObjectUtil.isNotNull(assignDTO)) {
            assignDTO.setWorkIdList(Collections.singletonList(workId));
            try {
                ((WorkOrderProcessManageService) AopContext.currentProxy()).workOrderAssign(assignDTO);
            } catch (Exception e) {
                throw new ServerException(e.getMessage());
            }
        }
        return workId;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchApply(List<BatchApplyDTO> batchApplyDTO) {
        String username = SecurityUser.getUsername();
        for (BatchApplyDTO applyDTO : batchApplyDTO) {
            applyDTO.setUpUserName(username);
            applyDTO.setUpWorkTime(DateUtil.date());
            applyDTO.setSource(0);
            applyDTO.setPlatform(0);
            WorkOrderProcessManageVO convert = BatchApplyConvert.INSTANCE.convert(applyDTO);
            WorkOrderAssignDTO workOrderAssignDTO = new WorkOrderAssignDTO(null, applyDTO.getStaffList(), applyDTO.getPreRepairTime(), applyDTO.getTimeoutWarningThreshold());
            convert.setWorkOrderAssignDTO(workOrderAssignDTO);
            workOrderApply(convert);
        }
    }

    @Nullable
    private PlantBaseInfoEntity getPlantBaseInfoEntity(WorkOrderProcessManageVO vo) {
        String plantId = vo.getPlantId();
        boolean plantIdNotBlank = StrUtil.isNotBlank(plantId);
        PlantBaseInfoEntity plantBaseInfo = null;
        // 判断平台类型
        if (vo.getPlatform().equals(WorkOrderPlatformEnum.LIGHT_CLOUD.getValue())) {
            // 判断plantId是否为空
            if (plantIdNotBlank) {
                // 查询电站信息
                plantBaseInfo = plantBaseInfoService.lambdaQuery()
                        .eq(PlantBaseInfoEntity::getPlatform, WorkOrderPlatformEnum.LIGHT_CLOUD.getValue())
                        .eq(PlantBaseInfoEntity::getId, plantId)
                        .one();
                // 判断电站信息是否为空
                if (Objects.isNull(plantBaseInfo)) {
                    throw new ServerException("光云平台不存在电站 id {}", vo.getPlantId());
                }
            }
        }

        // 如果vo.getPlatform() == 1  创建新电站or修改电站信息
        if (vo.getPlatform().equals(WorkOrderPlatformEnum.OTHER.getValue())) {
            PlantBaseInfoEntity lightPlant = plantBaseInfoService.getByInfo(WorkOrderPlatformEnum.LIGHT_CLOUD.getValue(), plantId, vo.getPlantName());
            // 判断电站是否属于光云
            if (Objects.nonNull(lightPlant)) {
                throw new ServerException("电站 id {} 属于光云平台电站，请检查是否填写错误或修改平台类型后重试 ", vo.getPlantId());
            }
            // 判断电站信息是否为空
            PlantBaseInfoEntity baseInfo = plantBaseInfoService.getByInfo(WorkOrderPlatformEnum.OTHER.getValue(), plantId, vo.getPlantName());
            if (ObjectUtils.isNull(baseInfo)) {
                // 生成一个未知的id
                plantId = IdUtils.timestampIdWithPrefix("unknown-");
            } else {
                plantId = baseInfo.getId();
            }

            // 设置电站id
            vo.setPlantId(plantId);
            // 将WorkOrderProcessManageVO转换为PlantBaseInfoEntity
            plantBaseInfo = WorkOrderProcessManageConvert.INSTANCE.convertToPlantBaseInfoEntity(vo);
            // 保存电站信息
            plantBaseInfoService.saveOrUpdate(plantBaseInfo);
        }
        return plantBaseInfo;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void workOrderAssign(WorkOrderAssignDTO assignDTO) {
        // 参数校验
        AssertUtils.isArrayEmpty(assignDTO.getWorkIdList().toArray(), "工单id");
        AssertUtils.isArrayEmpty(assignDTO.getStaffList().toArray(), "维修人员");
        AssertUtils.isNull(assignDTO.getPreRepairTime(), "计划修复时间");
        AssertUtils.isNull(assignDTO.getTimeoutWarningThreshold(), "超时警告阈值");

        List<Long> workIdList = new ArrayList<>(new HashSet<>(assignDTO.getWorkIdList()));
        List<Long> staffIdList = new ArrayList<>(new HashSet<>(assignDTO.getStaffList()));

        if (staffIdList.size() > 10) {
            throw new ServerException("维修人员数量超过限制，请减少后再操作");
        }

        // 创建工单待运维data
        List<WorkOperationInfoVO> workOperationInfoList = new ArrayList<>();
        Date preRepairTime = assignDTO.getPreRepairTime();
        workIdList
                .forEach(workId -> workOperationInfoList
                        .add(new WorkOperationInfoVO(workId, preRepairTime, WorkOrderOvertimeEnum.NORMAL.getValue())));
        workOperationInfoService.updateList(workOperationInfoList);


        // 更新工单运维人员data
        workIdList.forEach(workId -> workOperationPersonInfoService.saveOrUpdate(workId, staffIdList));
        workBaseInfoService.lambdaUpdate()
                .in(WorkBaseInfoEntity::getId, workIdList)
                .set(WorkBaseInfoEntity::getStatus, WorkOrderStatusEnum.TO_BE_REPAIR.getValue())
                .update();

        // 开发环境关闭短信通知避免产生过多的费用
        if (Boolean.TRUE.equals(sysParamsApi.smsNotifyEnabled())) {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            ThreadUtil.execAsync(() -> sendSmsAsync(assignDTO, authentication));
        }
    }


    public void sendSmsAsync(WorkOrderAssignDTO assignDTO, Authentication authentication) {
        List<Long> workIdList = assignDTO.getWorkIdList();
        Date preRepairTime = assignDTO.getPreRepairTime();
        // 获取派单短信参数
        List<Map<String, String>> list = selectSmsParamByWorkIds(assignDTO.getWorkIdList());
        SecurityContextHolder.getContext().setAuthentication(authentication);
        List<RepairUserVO> infoByIds = sysUserApi.getInfoByIds(assignDTO.getStaffList());

        for (RepairUserVO repairUser : infoByIds) {
            String mobile = repairUser.getMobile();
            // 手机号码不为空
            if (StringUtils.isNotBlank(mobile)) {
                for (Map<String, String> params : list) {
                    params.put("completionTime", DateUtils.format(preRepairTime));
                    SecurityContextHolder.getContext().setAuthentication(authentication);
                    // 维修人员短信通知
                    smsApi.sendByTemplateCode(SmsTemplateCode.DISPATCH_NOTICE_CODE, mobile, params);

                }
            }
        }

        // 客户短信通知
        List<String> phoneNumbers = workBaseInfoService.getPlantUserPhoneByWorkId(workIdList);
        SecurityContextHolder.getContext().setAuthentication(authentication);
        phoneNumbers.forEach(phone -> {
            smsApi.sendByTemplateCode(SmsTemplateCode.CUSTOMER_NOTICE_CODE, phone, new HashMap<>());
        });

        // 超时警告阈值
        Integer timeoutWarningThreshold = assignDTO.getTimeoutWarningThreshold();
        workIdList.forEach(workId -> redisCache.set(RedisKeys.getTimeoutWarningThresholdKey(workId.toString()), timeoutWarningThreshold, RedisCache.NOT_EXPIRE));
        UserDetail user = SecurityUser.getUser();
        for (Long workId : workIdList) {
            SecurityContextHolder.getContext().setAuthentication(authentication);
            List<String> repairUsers = sysUserApi.repairUser(workId).getData().stream().map(RepairUserVO::getName).collect(Collectors.toList());
            SecurityContextHolder.clearContext();
            RunSmsJobDTO runSmsJobDTO = new RunSmsJobDTO();
            HashMap<String, String> params = new HashMap<>();
            params.put("workId", workId.toString());
            params.put("maintainers", repairUsers.toString());
            params.put("mobile", user.getMobile());
            // test
            // params.put("mobile", "mobile");
            params.put("timeout", DateUtils.format(preRepairTime));
            runSmsJobDTO.setParams(params);

            // 计算发送短信时间
            LocalDateTime localDateTime = preRepairTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
            LocalDate nextDay = localDateTime.toLocalDate().plusDays(assignDTO.getTimeoutWarningThreshold());
            LocalTime nineAm = LocalTime.of(9, 0, 0);
            LocalDateTime sendingSmsTime = LocalDateTime.of(nextDay, nineAm);
            runSmsJobDTO.setScheduledTime(sendingSmsTime);

            // test
            // LocalDateTime testDate = LocalDateTime.ofEpochSecond(1696991624L, 0, ZoneOffset.UTC);
            // runSmsJobDTO.setScheduledTime(testDate);

            try {
                SecurityContextHolder.getContext().setAuthentication(authentication);
                // 定时短信通知
                runJobApi.runSmsJob(runSmsJobDTO);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void changeStatus(List<Long> workIdList, Integer status, Date suspendExpiration) {

        // 获取工单操作信息
        List<WorkOperationInfoEntity> list = workOperationInfoService.lambdaQuery()
                .in(WorkOperationInfoEntity::getWorkId, workIdList)
                .list();

        // 设置修复时间
        Date repairTime = new Date();
        for (WorkOperationInfoEntity entity : list) {
            // 如果是待审核，更新修复时间
            if (status.equals(WorkOrderStatusEnum.TO_BE_AUDIT.getValue())) {
                workOperationInfoService.lambdaUpdate()
                        .eq(WorkOperationInfoEntity::getWorkId, entity.getWorkId())
                        .set(WorkOperationInfoEntity::getRepairTime, repairTime)
                        .update();
            } else if (status.equals(WorkOrderStatusEnum.SUSPENDED.getValue())) {
                workOperationInfoService.lambdaUpdate()
                        .eq(WorkOperationInfoEntity::getWorkId, entity.getWorkId())
                        .set(WorkOperationInfoEntity::getSuspended, 1)

                        .set(WorkOperationInfoEntity::getOvertimed, 0)
                        .set(WorkOperationInfoEntity::getPreRepairTime, null)
                        .set(WorkOperationInfoEntity::getRepairStatus, 1)
                        .set(suspendExpiration != null, WorkOperationInfoEntity::getSuspendExpiration, suspendExpiration)
                        .update();
            }
        }

        // 更新工单状态
        workBaseInfoService.lambdaUpdate()
                .in(CollUtil.isNotEmpty(workIdList), WorkBaseInfoEntity::getId, workIdList)
                .set(WorkBaseInfoEntity::getStatus, status)
                .set(WorkBaseInfoEntity::getUpdateTime, new Date())
                .update();
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void importByExcel(MultipartFile file) {
        ExcelUtils.readAnalysis(file, WorkOrderApplyExcelVO.class, new ExcelFinishCallBack<WorkOrderApplyExcelVO>() {
            @Override
            public void doAfterAllAnalysed(List<WorkOrderApplyExcelVO> result) {
                saveWorkOrderApplication(result);
            }

            @Override
            public void doSaveBatch(List<WorkOrderApplyExcelVO> result) {
                saveWorkOrderApplication(result);
            }

            private void saveWorkOrderApplication(List<WorkOrderApplyExcelVO> result) {
                ExcelUtils.parseDict(result);
                excelDataConversion(result, ExcelUtils.IMPORT);
                List<WorkOrderProcessManageVO> list = WorkOrderProcessManageConvert.INSTANCE.convertToWorkOrderProcessManageVos(result);
                list.forEach(workOrderApplicationDTO -> {
                    // Excel导入默认值
                    workOrderApplicationDTO.setSource(1);
                    workOrderApplicationDTO.setRemarkType(0);
                    workOrderApplicationDTO.setCountry("中国");
                    // 防止事务失效
                    try {
                        WorkOrderProcessManageService workOrderProcessManageService = (WorkOrderProcessManageService) AopContext.currentProxy();
                        workOrderProcessManageService.workOrderApply(workOrderApplicationDTO);
                    } catch (Exception e) {
                        throw new ServerException(e.getMessage());
                    }
                });
            }
        });

    }

    @Override
    @SneakyThrows
    public void export(WorkOrderProcessManageQuery query) {
        LambdaQueryWrapper<WorkOrderProcessManageViewEntity> wrapper = getWrapper(query);
        if (wrapper == null) {
            throw new ServerException("当前用户没有工单");
        }
        List<WorkOrderProcessManageViewEntity> workOrderProcessManageViewEntities = baseMapper.selectList(wrapper);

        List<WorkOrderApplyExcelVO> workOrderApplyExcelVos = WorkOrderProcessManageConvert.INSTANCE.convertToWorkOrderApplyExcelVos(workOrderProcessManageViewEntities);
        // 数据字段翻译
        transService.transBatch(workOrderApplyExcelVos);
        excelDataConversion(workOrderApplyExcelVos, ExcelUtils.EXPORT);
        // 写到浏览器打开
        ExcelUtils.excelExport(WorkOrderApplyExcelVO.class, "work_order_excel" + DateUtils.format(new Date()), null, workOrderApplyExcelVos);
    }

    private void fillCompanionNames(List<WorkOrderApplyExcelVO> rows) {
        if (rows == null || rows.isEmpty()) return;

        // 1) 一次性获取所有维修人员
        List<RepairUserVO> allUserInfo = sysUserApi.getAllUserInfo();
        if (allUserInfo == null || allUserInfo.isEmpty()) {
            // 没有人员信息，全部置空即可
            for (WorkOrderApplyExcelVO vo : rows) {
                vo.setCompanionUserName("");
            }
            return;
        }

        // 2) 建立 id -> 姓名 的查找表（重复ID取第一个即可）
        Map<Long, String> id2name = allUserInfo.stream()
                .filter(u -> u.getId() != null && u.getName() != null)
                .collect(java.util.stream.Collectors.toMap(
                        RepairUserVO::getId, RepairUserVO::getName, (a, b) -> a));

        // 3) 回填（按原 ID 顺序，用中文顿号连接；找不到的就回填成原ID字符串）
        for (WorkOrderApplyExcelVO vo : rows) {
            String raw = vo.getCompanion();
            if (raw == null || raw.trim().isEmpty()) {
                vo.setCompanionUserName("");
                continue;
            }
            java.util.List<Long> ids = parseIdsLenient(raw);
            if (ids.isEmpty()) {
                vo.setCompanionUserName("");
                continue;
            }
            String names = ids.stream()
                    .map(id -> id2name.getOrDefault(id, String.valueOf(id)))
                    .collect(java.util.stream.Collectors.joining("、"));
            vo.setCompanionUserName(names);
        }
    }
    private static java.util.List<Long> parseIdsLenient(String raw) {
        String s = raw == null ? "" : raw.trim();
        if (s.isEmpty()) return java.util.Collections.emptyList();
        // 去掉方括号和空格、引号
        if (s.startsWith("[") && s.endsWith("]")) s = s.substring(1, s.length() - 1);
        s = s.replace("\"", "").replace("'", "").trim();
        if (s.isEmpty()) return java.util.Collections.emptyList();

        java.util.List<Long> ids = new java.util.ArrayList<>();
        // 同时支持英文逗号和中文逗号
        for (String part : s.split("[,，]")) {
            String v = part.trim();
            if (v.isEmpty()) continue;
            try {
                ids.add(Long.valueOf(v));
            } catch (NumberFormatException ignore) {
                // 非法ID忽略
            }
        }
        return ids;
    }

    @Override
    public PageResult<WorkOrderProcessManageVO> page(WorkOrderProcessManageQuery query) {
        LambdaQueryWrapper<WorkOrderProcessManageViewEntity> wrapper = getWrapper(query);
        String repairUser = query.getMaintenancePerson();
        if (wrapper == null) {
            // 返回一个空的 PageResult
            return new PageResult<>(Collections.emptyList(), 0);
        }
        // 1. 如果传入了维修人员姓名，先查出匹配的工单ID，再加到 wrapper 中筛选
        if (StrUtil.isNotEmpty(repairUser)) {
            List<Long> matchedWorkIds = workOperationInfoService.selectWorkIdsByUsernameLike(repairUser);
            if (matchedWorkIds.isEmpty()) {
                return new PageResult<>(Collections.emptyList(), 0);
            } else {
                wrapper.in(CollUtil.isNotEmpty(matchedWorkIds), WorkOrderProcessManageViewEntity::getWorkId, matchedWorkIds);
            }
        }
        // 2. 分页查出视图数据
        IPage<WorkOrderProcessManageViewEntity> page = baseMapper.selectPage(getPage(query), wrapper);
        List<WorkOrderProcessManageVO> list = WorkOrderProcessManageConvert.INSTANCE.convertList(page.getRecords());
        // 如果没有结果，提前返回
        if (CollUtil.isEmpty(list)) {
            return new PageResult<>(Collections.emptyList(), 0);
        }
        // 3. 提取工单ID列表
        List<Long> workIds = list.stream()
                .map(WorkOrderProcessManageVO::getWorkId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
        // 4. 批量查询工单对应的同行人ID映射
        Map<Long, List<Long>> workRepairMap = workOperationInfoService.getRepairMapByWorkIds(workIds);

        // 5. 查询所有 repairId 对应的用户名
        Set<Long> allRepairIds = workRepairMap.values().stream()
                .flatMap(Collection::stream)
                .collect(Collectors.toSet());

        Map<Long, String> repairUserMap = sysUserApi.getUsernameMapByIds(allRepairIds).getData();

        // 6. 拼接用户名，并填充进 VO
        for (WorkOrderProcessManageVO vo : list) {
            List<Long> repairIds = workRepairMap.getOrDefault(vo.getWorkId(), Collections.emptyList());
            String names = repairIds.stream()
                    .map(repairUserMap::get)
                    .filter(StrUtil::isNotBlank)
                    .distinct()
                    .collect(Collectors.joining("、"));
            vo.setMaintenancePerson(names);
        }

        // 7. 补充审核人姓名
        if (query.getStatus() != null && query.getStatus().contains(WorkOrderStatusEnum.TO_BE_AUDIT.getValue())) {
            list.forEach(vo -> {
                if (vo.getExamineUserId() != null) {
                    vo.setExamineUserName(sysUserApi.getUserNameById(vo.getExamineUserId()));
                }
            });
        }
        return new PageResult<>(list, page.getTotal());
    }

    private ArrayListMultimap<Long, String> getWorkIdWithNameMap(List<SysUserVO> userList) {
        ArrayListMultimap<Long, String> multimap = ArrayListMultimap.create();

        // 遍历用户列表
        for (SysUserVO user : userList) {
            // 获取用户工号
            Long workId = user.getWorkId();
            // 获取用户真实姓名
            String realName = user.getRealName();

            // 如果工号和真实姓名都不为空，则将它们添加到多映射中
            if (workId != null && realName != null) {
                multimap.put(workId, realName);
            }
        }

        return multimap;
    }

    private LambdaQueryWrapper<WorkOrderProcessManageViewEntity> getWrapper(WorkOrderProcessManageQuery query) {
        LambdaQueryWrapper<WorkOrderProcessManageViewEntity> wrapper = Wrappers.lambdaQuery();

        if (query != null) {
            String endTime = query.getEndTime();
            if (StrUtil.isNotEmpty(endTime)) {
                endTime = DateUtil.offsetDay(DateUtil.parseDate(endTime), 1).toString();
            }
            wrapper
                    .like(StrUtil.isNotBlank(query.getUpUserName()), WorkOrderProcessManageViewEntity::getUpUserName, query.getUpUserName())
                    .like(query.getWorkId() != null, WorkOrderProcessManageViewEntity::getWorkId, query.getWorkId())
                    .in(CollUtil.isNotEmpty(query.getOvertimed()), WorkOrderProcessManageViewEntity::getOvertimed, query.getOvertimed())
                    .in(CollUtil.isNotEmpty(query.getStatus()), WorkOrderProcessManageViewEntity::getStatus, query.getStatus())
                    .in(CollUtil.isNotEmpty(query.getPlantTypeList()), WorkOrderProcessManageViewEntity::getOrgId, query.getPlantTypeList())
                    .eq(query.getAlarmType() != null, WorkOrderProcessManageViewEntity::getAlarmType, query.getAlarmType())
                    .eq(query.getPlatform() != null, WorkOrderProcessManageViewEntity::getPlatform, query.getPlatform())
                    .like(StrUtil.isNotBlank(query.getAddress()), WorkOrderProcessManageViewEntity::getAddress, query.getAddress())
                    .between(StrUtil.isNotBlank(query.getStartTime()) && StrUtil.isNotBlank(query.getEndTime()), WorkOrderProcessManageViewEntity::getUpWorkTime,
                            query.getStartTime(), endTime);

            wrapper
                    .in(CollUtil.isNotEmpty(query.getCountry()), WorkOrderProcessManageViewEntity::getCountry, query.getCountry())
                    .in(CollUtil.isNotEmpty(query.getProvince()), WorkOrderProcessManageViewEntity::getProvince, query.getProvince())
                    .in(CollUtil.isNotEmpty(query.getCity()), WorkOrderProcessManageViewEntity::getCity, query.getCity());

            List<Long> mergedWorkIdList = null;
            if (query.getPersonal() == 1) {
                Long userId = SecurityUser.getUserId();
                List<Long> workIdList = workOperationPersonInfoService.getWorkIdsByUser(userId);
                mergedWorkIdList = workIdList.stream()
                        .distinct()
                        .collect(Collectors.toList());
                if (CollUtil.isEmpty(mergedWorkIdList)) {
                    return null;
                }
                wrapper.in(CollUtil.isNotEmpty(mergedWorkIdList), WorkOrderProcessManageViewEntity::getWorkId, workIdList);

            }

            String plantIdOrName = query.getPlantIdOrName();
            if (StrUtil.isNotBlank(plantIdOrName)) {
                wrapper.and(w -> w.like(StringUtils.isNotBlank(plantIdOrName), WorkOrderProcessManageViewEntity::getPlantId, plantIdOrName).or()
                        .like(StringUtils.isNotBlank(plantIdOrName), WorkOrderProcessManageViewEntity::getPlantName, plantIdOrName));
            }

        }

        getWorkOrderDataScopeWrapper(wrapper, null, null, "up_user_id");
        // DataScope dataScope = getWorkOrderDataScope(null, null, "up_user_id", wrapper.isEmptyOfWhere());
        // if (dataScope != null) {
        //     String sqlFilter = dataScope.getSqlFilter();
        //     wrapper.last(StringUtils.isNotBlank(sqlFilter), sqlFilter);
        // }
        return wrapper;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(List<Long> idList) {
        workBaseInfoService.removeByIds(idList);
        workApplyInfoService.deleteByWorkIds(idList);
        workOperationInfoService.deleteByWorkIds(idList);
        workOperationPersonInfoService.deleteByWorkIds(idList);
        workExamineInfoService.deleteByWorkIds(idList);
        workServiceOfferService.deleteByWorkIds(idList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(WorkOrderProcessManageVO vo) {
        // 获取工厂信息实体
        PlantBaseInfoEntity plantBaseInfo = getPlantBaseInfoEntity(vo);
        // 将WorkOrderProcessManageVO转换为WorkBaseInfoEntity
        WorkBaseInfoEntity workBaseInfoEntity = WorkOrderProcessManageConvert.INSTANCE.convertToWorkBaseInfoEntity(vo);
        // 将WorkOrderProcessManageVO转换为WorkApplyInfoEntity
        WorkApplyInfoEntity workApplyInfoEntity = WorkOrderProcessManageConvert.INSTANCE.convertToWorkApplyInfoEntity(vo);
        // 获取保修内容
        String warrantyContent = vo.getWarrantyContent();
        // 如果保修内容不为空，则更新保修内容
        if (StrUtil.isNotBlank(warrantyContent)) {
            plantBaseInfo.setWarrantyContent(warrantyContent);
            plantBaseInfoService.updateById(plantBaseInfo);
        }

        // 更新工厂信息实体
        workBaseInfoService.updateById(workBaseInfoEntity);
        // 更新工单申请信息实体
        workApplyInfoService.updateByWorkId(workApplyInfoEntity);
        // 获取工单ID
        Long workId = workBaseInfoEntity.getId();
        // 获取工单分配信息
        WorkOrderAssignDTO assignDTO = vo.getWorkOrderAssignDTO();
        // 如果工单分配信息不为空，则更新工单分配信息
        if (ObjectUtil.isNotNull(assignDTO)) {
            assignDTO.setWorkIdList(Collections.singletonList(workId));
            this.workOrderAssign(assignDTO);
        }
    }

    public List<Map<String, String>> selectSmsParamByWorkIds(List<Long> idList) {
        return baseMapper.selectSmsParamByWorkIds(idList);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void review(WorkOrderExamineDTO dto) {
        // 获取审核意见
        String examineOpinion = Optional.ofNullable(dto.getExamineOpinion()).orElse("");
        // 获取评估分数
        Double evaluate = dto.getEvaluate();
        // 获取审核结果
        Integer auditResult = dto.getAuditResult();
        // 获取工作id列表
        List<Long> workIdList = dto.getWorkIdList();

        // 挂起不删除定时任务
        Integer suspended = dto.getSuspended();

        if (auditResult != null && auditResult == 2 && suspended == 0) {
            log.info("不是挂起 执行删除任务");
            // 审核通过
            workIdList.forEach(workId -> {
                try {
                    // 删除短信任务
                    runJobApi.deleteSmsJobByWorkId(workId.toString());
                    // 删除超时警告阈值
                    redisCache.delete(RedisKeys.getTimeoutWarningThresholdKey(workId.toString()));
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            });
        }
        // 修改工单状态为挂起
        if (suspended == 1) {
            dto.setStatus(WorkOrderStatusEnum.SUSPENDED.getValue());
        }

        ArrayList<WorkExamineInfoEntity> examineList = new ArrayList<>();
        // 遍历工作id列表，创建审核信息实体
        workIdList.forEach(workId -> {
            WorkExamineInfoEntity entity = new WorkExamineInfoEntity(workId, examineOpinion, evaluate, auditResult);
            examineList.add(entity);
        });
        // 更改工作状态
        changeStatus(dto.getWorkIdList(), dto.getStatus(), dto.getSuspendExpiration());
        // 删除审核信息实体
        workExamineInfoService.deleteByWorkIds(workIdList);
        // 保存审核信息实体
        workExamineInfoService.saveBatch(examineList);
    }

    @Override
    public void afterPropertiesSet() {
        initDictionaryTransService.refreshTransCache();
    }


    @Override
    public WorkOrderInfoVO details(Long workId) {
        // 创建WorkOrderInfoVO对象
        WorkOrderInfoVO vo = new WorkOrderInfoVO();
        // 根据workId查询WorkOrderProcessManageViewEntity实体
        WorkOrderProcessManageViewEntity entity = this.lambdaQuery().eq(WorkOrderProcessManageViewEntity::getWorkId, workId).one();
        // 如果实体不为空
        if (ObjectUtil.isNotNull(entity)) {
            // 调用baseDetails方法查询WorkOrderBaseDetailsVO实体
            WorkOrderBaseDetailsVO workOrderBaseDetailsVO = this.baseDetails(workId);
            workOrderBaseDetailsVO.setPlantStatus(entity.getPlantStatus());
            if (entity.getExamineUserId() != null) {
                workOrderBaseDetailsVO.setExamineUserId(entity.getExamineUserId());
                workOrderBaseDetailsVO.setExamineUserName(sysUserApi.getUserNameById(entity.getExamineUserId()));
            }
            // 设置WorkOrderBaseDetailsVO实体
            vo.setWorkBaseInfo(workOrderBaseDetailsVO);
        }

        // 调用sysUserApi查询workId对应的维修用户
        List<RepairUserVO> repairUser = sysUserApi.repairUser(workId).getData();
        // 如果查询到的维修用户不为空
        if (ObjectUtil.isNotNull(repairUser)) {
            // 设置维修用户
            vo.setRepairUsers(repairUser);
        }

        // 调用workExamineInfoService查询workId对应的工单审核信息
        WorkExamineInfoEntity workExamineInfoEntity = workExamineInfoService.info(workId);
        // 如果工单审核信息不为空
        if (ObjectUtil.isNotNull(workExamineInfoEntity)) {
            // 调用WorkExamineInfoConvert转换实体
            WorkExamineInfoVO workExamineInfo = WorkExamineInfoConvert.INSTANCE.convert(workExamineInfoEntity);
            // 设置工单审核信息
            vo.setWorkExamineInfo(workExamineInfo);
        }

        // 调用workOperationInfoService查询workId对应的工单操作信息
        WorkOperationInfoVO workOperationInfo = workOperationInfoService.info(workId);

        // 同行人
        if (entity.getHasCompanion() == 1) {
            // 调用sysUserApi查询workId对应的同行人员
            List<RepairUserVO> companionUser = sysUserApi.companionUser(workId).getData();
            // 如果查询到的同行人员不为空
            if (ObjectUtil.isNotNull(companionUser)) {
                // 设置维修用户
                workOperationInfo.setCompanionUsers(companionUser);
            }
        }

        // 如果工单操作信息不为空
        if (ObjectUtil.isNotNull(workOperationInfo.getAlarmCause()) || workOperationInfo.getSuspended() != null) {
            // 设置工单操作信息
            vo.setWorkOperationInfo(workOperationInfo);
        }
        LambdaQueryWrapper<BillWorkExpenseEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BillWorkExpenseEntity::getWorkId, workId);
        List<BillWorkExpenseEntity> list = billWorkExpenseService.list(queryWrapper);
        if (CollUtil.isNotEmpty(list)) {
            List<BillWorkExpenseVO> billWorkExpenseVOList = BillWorkExpenseConvert.INSTANCE.convertList(list);
            vo.setBillWorkExpenseList(billWorkExpenseVOList);
        }

        List<WorkChargeVO> workChargeList = workChargeService.listByWorkId(workId);
        if (CollUtil.isNotEmpty(workChargeList)) {
            vo.setWorkChargeList(workChargeList);
        }
        // 返回WorkOrderInfoVO对象
        return vo;
    }

    @Override
    public PageResult<WorkOrderProcessManageVO> overtimePage(WorkOrderProcessManageQuery query) {
        // 获取查询条件
        LambdaQueryWrapper<WorkOrderProcessManageViewEntity> wrapper = getWrapper(query);
        if (wrapper == null) {
            // 返回一个空的 PageResult
            return new PageResult<>(Collections.emptyList(), 0);
        }
        // 查询数据
        List<WorkOrderProcessManageViewEntity> list = baseMapper.selectList(wrapper);
        // 转换查询结果
        List<WorkOrderProcessManageVO> voList = WorkOrderProcessManageConvert.INSTANCE.convertList(list);
        // 写入维修人员
        setRepairUserName(voList);
        // 排序
        Comparator<WorkOrderProcessManageVO> comparator = getComparator();
        voList.sort(comparator);

        return PageUtils.page(voList, query);
    }

    private void setRepairUserName(List<WorkOrderProcessManageVO> list) {
        // 如果list为空，则直接返回
        if (list.isEmpty()) {
            return;
        }
        // 获取list中每个WorkOrderProcessManageVO的workId
        List<Long> workIdList = list.stream()
                .map(WorkOrderProcessManageVO::getWorkId)
                .collect(Collectors.toList());

        // 根据workId获取对应的 repairUserName
        List<SysUserVO> repairUserList = baseMapper.getWorkIdWithRepairUserName(workIdList);
        // 如果 repairUserList为空，则直接返回
        if (repairUserList.isEmpty()) {
            return;
        }

        // 获取workId与 repairUserName的映射关系
        ArrayListMultimap<Long, String> workIdWithRepairUserNameMap = getWorkIdWithNameMap(repairUserList);
        // 遍历list，将每个WorkOrderProcessManageVO的repairUserName设置为workIdWithRepairUserNameMap中对应的 repairUserName
        list.forEach(vo -> vo.setRepairUserNameList(workIdWithRepairUserNameMap.get(vo.getWorkId())));
    }

    private static Comparator<WorkOrderProcessManageVO> getComparator() {
        return Comparator.comparing(wo -> {
            String overtime = wo.getOvertime();
            // 比较超时时间
            if (StringUtils.MINUS_SIGN.equals(overtime) || StrUtil.isEmpty(overtime) || WorkOrderOvertimeEnum.ABOUT_TO_TIMEOUT.getName().equals(overtime)) {
                // 如果超时时间为负数或者为空或者为超时状态，则返回最小值
                return Integer.MIN_VALUE;
            } else {
                // 否则返回减去单位后的整数
                return Integer.parseInt(overtime.substring(0, overtime.length() - 1));
            }
        }, Comparator.nullsLast(Comparator.reverseOrder()));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveAndSubmit(WorkOperationInfoDTO dto) {
        if (dto.getStatus() == null) {
            throw new ServerException("工单状态不能为空");
        }
        // 同行人默认新增提交人
        List<Long> companion = dto.getCompanion();
        if (companion == null) {
            companion = new ArrayList<>();
        }
        companion.add(SecurityUser.getUserId());
        Long workId = dto.getWorkId();
        dto.setCompanion(companion);
        workOperationInfoService.updateBatch(dto);
        List<Long> workIdList = Collections.singletonList(workId);
        WorkExamineInfoEntity newEntity = new WorkExamineInfoEntity();
        newEntity.setExamineOpinion(null);
        newEntity.setEvaluate(null);
        newEntity.setAuditResult(null);
        LambdaQueryWrapper<WorkExamineInfoEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WorkExamineInfoEntity::getWorkId, workId);
        workExamineInfoService.update(newEntity, queryWrapper);
        this.changeStatus(workIdList, dto.getStatus(), null);
        List<BillWorkExpenseVO> billWorkExpenseList = dto.getBillWorkExpenseList();
        if (Objects.nonNull(billWorkExpenseList)) {
            LambdaQueryWrapper<BillWorkExpenseEntity> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(BillWorkExpenseEntity::getWorkId, workId);
            List<BillWorkExpenseEntity> entities = BillWorkExpenseConvert.INSTANCE.convertEntityList(billWorkExpenseList);
            billWorkExpenseService.removeBatchByIds(CollectionUtils.subtract(billWorkExpenseService.list(wrapper), entities));
            billWorkExpenseService.saveBatch(BillWorkExpenseConvert.INSTANCE.convertEntityList(billWorkExpenseList));
        }
        List<WorkChargeVO> workChargeList = dto.getWorkChargeList();
        if (Objects.nonNull(workChargeList)) {
            LambdaQueryWrapper<WorkChargeEntity> wrapper = new LambdaQueryWrapper<>();
            String workOrderId = String.valueOf(workId);
            wrapper.eq(WorkChargeEntity::getWorkOrderId, workOrderId);
            List<WorkChargeEntity> list = workChargeService.list(wrapper);
            workChargeService.removeBatchByIds(CollectionUtils.subtract(list, WorkChargeConvert.INSTANCE.convertEntitys(workChargeList)));
            workChargeService.saveBatch(WorkChargeConvert.INSTANCE.convertEntitys(workChargeList));
        }
    }

    @Override
    public WorkOrderBaseDetailsVO baseDetails(Long workId) {
        // 创建LambdaQueryWrapper对象，用于查询WorkOrderProcessManageViewEntity实体
        LambdaQueryWrapper<WorkOrderProcessManageViewEntity> wrapper = Wrappers.lambdaQuery();
        // 设置查询条件：workId等于传入的参数
        wrapper.eq(WorkOrderProcessManageViewEntity::getWorkId, workId);
        // 查询实体
        WorkOrderProcessManageViewEntity entity = this.baseMapper.selectOne(wrapper);
        // 断言查询结果是否为空
        AssertUtils.isNull(entity, "工单");
        // 将实体转换为WorkOrderBaseDetailsVO对象
        WorkOrderBaseDetailsVO vo = WorkOrderProcessManageConvert.INSTANCE.convertToWorkOrderBaseDetailsVO(entity);
        // 获取工单处理人列表
        List<RepairUserVO> repairUserList = sysUserApi.repairUser(entity.getWorkId()).getData();
        // 创建WorkOrderAssignDTO对象，用于存储工单处理人信息
        WorkOrderAssignDTO assignDTO = new WorkOrderAssignDTO(Collections.singletonList(workId),
                repairUserList.stream().map(RepairUserVO::getId).collect(Collectors.toList()),
                vo.getPreRepairTime(),
                (Integer) redisCache.get(RedisKeys.getTimeoutWarningThresholdKey(workId.toString())));
        // 将WorkOrderAssignDTO对象添加到WorkOrderBaseDetailsVO对象中
        vo.setWorkOrderAssignDTO(assignDTO);
        // 获取工单组织信息
        List<OrgWithUserVO> orgWithUsers = sysUserApi.getOrgWithUserByWorkId(workId).getData();
        // 将工单组织信息添加到WorkOrderBaseDetailsVO对象中
        vo.setOrgWithUsers(orgWithUsers);
        // 判断工单处理人名称是否为空，如果为空，则设置为“暂无”
        if (StrUtil.isBlank(vo.getPlantUserName())) {
            vo.setPlantUserName("暂无");
        }
        // 返回WorkOrderBaseDetailsVO对象
        return vo;
    }

    public void excelDataConversion(List<WorkOrderApplyExcelVO> workOrderApplyExcelVos, String type) {
        if (ExcelUtils.IMPORT.equals(type)) {
            workOrderApplyExcelVos.forEach(vo -> {
                String platformLabel = vo.getPlatformLabel();
                if (StringUtils.isEmpty(platformLabel) || Objects.isNull(WorkOrderPlatformEnum.getValueByName(platformLabel))) {
                    throw new ServerException("缺少平台类型或填写错误");
                }

                if (StringUtils.isAnyEmpty(vo.getProvince(), vo.getCity(), vo.getArea())) {
                    throw new ServerException("缺少电站区域");
                }

                String plantTypeLabel = vo.getPlantTypeName();
                String[] repairs = vo.getCompanionUserName().split("、");
                Map<String, String> map = workBaseInfoDao.trans(plantTypeLabel, repairs);
                String plantType = String.valueOf(map.get("plant_type"));
                if (Objects.isNull(plantType)) {
                    throw new ServerException("电站类型不正确：" + plantTypeLabel);
                }
                vo.setPlantType(Integer.parseInt(plantType));
                String repairString = String.valueOf(map.get("repairs"));
                if (Objects.isNull(map.get("repairs"))) {
                    throw new ServerException("维修人员不正确：" + repairString);
                }
                String[] split = repairString.split(",");
                List<Long> staffList = new ArrayList<>();
                for (String s : split) {
                    long num = Long.parseLong(s);
                    staffList.add(num);
                }
                vo.setWorkOrderAssignDTO(new WorkOrderAssignDTO(null, staffList, vo.getPreRepairTime(), vo.getTimeoutWarningThreshold()));
            });
        } else if (ExcelUtils.EXPORT.equals(type)) {
            fillCompanionNames(workOrderApplyExcelVos);
        } else {
            throw new ServerException("工单Excel数据转换类型错误");
        }
    }

}
