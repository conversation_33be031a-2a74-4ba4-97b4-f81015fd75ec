package com.botong.workorder.service;

import com.botong.framework.common.page.PageResult;
import com.botong.framework.mybatis.service.BaseService;
import com.botong.workorder.dto.WorkOperationInfoDTO;
import com.botong.workorder.entity.WorkOperationInfoEntity;
import com.botong.workorder.query.WorkOperationInfoQuery;
import com.botong.workorder.vo.WorkOperationInfoVO;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 工单待运维服务接口
 * 提供工单待运维相关的业务操作，包括分页查询、保存、更新、删除、批量更新等功能
 *
 * <AUTHOR>
 * @since 2023-08-24 17:31:01
 */
public interface WorkOperationInfoService extends BaseService<WorkOperationInfoEntity> {

    /**
     * 分页查询
     *
     * @param query 查询条件
     * @return PageResult
     */
    PageResult<WorkOperationInfoVO> page(WorkOperationInfoQuery query);

    /**
     * 新增
     *
     * @param vo vo
     */
    void save(WorkOperationInfoVO vo);

    /**
     * 修改
     *
     * @param vo vo
     */
    void update(WorkOperationInfoVO vo);

    /**
     * 删除
     *
     * @param idList id集合
     */
    void delete(List<Long> idList);

    /**
     * 信息
     *
     * @param id id
     * @return WorkOperationInfoVO
     */
    WorkOperationInfoVO info(Long id);

    /**
     * 保存报价与维修信息
     *
     * @param dto dto
     */
    void updateBatch(WorkOperationInfoDTO dto);

    /**
     * 更新超时时间
     *
     * @param workIdList 工单id列表
     * <AUTHOR>
     * @since 2023-09-11 16:51:08
     */
    void updateOvertime(List<Long> workIdList);

    /**
     * 根据工单id删除运维关联记录
     *
     * @param idList 工单id集合
     */
    void deleteByWorkIds(List<Long> idList);

    /**
     * 保存或更新列表
     *
     * @param list 列表
     * @return boolean
     * <AUTHOR>
     * @since 2023-10-25 12:00:16
     */
    void updateList(Collection<WorkOperationInfoVO> list);

    /**
     * 根据工单id查询关联的维修id列表
     *
     * @param workIds 工单id列表
     * @return Map<Long, List < Long>>
     */
    Map<Long, List<Long>> getRepairMapByWorkIds(List<Long> workIds);

    /**
     * 根据用户名模糊查询工单ID
     * 根据维修人员用户名模糊匹配，获取包含该人员的工单ID列表
     *
     * @param repairUser 维修人员用户名（模糊匹配）
     * @return 匹配的工单ID列表
     */
    List<Long> selectWorkIdsByUsernameLike(String repairUser);
}