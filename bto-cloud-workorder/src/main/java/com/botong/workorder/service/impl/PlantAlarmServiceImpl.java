package com.botong.workorder.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.botong.api.module.photovoltaic.vo.PlantAlarmVO;
import com.botong.framework.common.page.PageResult;
import com.botong.framework.mybatis.service.impl.BaseServiceImpl;
import com.botong.workorder.convert.PlantAlarmConvert;
import com.botong.workorder.dao.PlantAlarmDao;
import com.botong.workorder.entity.PlantAlarmEntity;
import com.botong.workorder.query.PlantAlarmQuery;
import com.botong.workorder.service.PlantAlarmService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 告警视图服务实现类
 * 实现告警信息相关的业务操作，包括分页查询、保存、更新、删除等功能
 *
 * <AUTHOR>
 * @since 1.0.0 2024-01-24
 */
@Service
@AllArgsConstructor
public class PlantAlarmServiceImpl extends BaseServiceImpl<PlantAlarmDao, PlantAlarmEntity> implements PlantAlarmService {

    /**
     * 分页查询告警信息列表
     * 
     * @param query 查询条件参数
     * @return 分页查询结果，包含告警信息列表数据
     */
    @Override
    public PageResult<PlantAlarmVO> page(PlantAlarmQuery query) {
        LambdaQueryWrapper<PlantAlarmEntity> wrapper = Wrappers.lambdaQuery();
        getWorkOrderDataScopeWrapper(wrapper, null, "project_special", "project_special");
        wrapper
                .eq(StrUtil.isNotBlank(query.getPlantId()), PlantAlarmEntity::getPlantUid, query.getPlantId())
                .eq(query.getStatus()!=null, PlantAlarmEntity::getStatus, query.getStatus())
                .orderByDesc(PlantAlarmEntity::getStartTime);
        IPage<PlantAlarmEntity> page = baseMapper.selectPage(getPage(query),wrapper);

        return new PageResult<>(PlantAlarmConvert.INSTANCE.convertList(page.getRecords()), page.getTotal());
    }

    /**
     * 根据分页参数获取告警信息列表
     * 
     * @param query 查询条件参数
     * @param page 分页参数
     * @return 分页查询结果，包含告警信息列表数据
     */
    @Override
    public IPage<PlantAlarmVO> getPageList(PlantAlarmQuery query, Page<PlantAlarmVO> page) {
        return baseMapper.getPageList(query, page);
    }

    /**
     * 构建查询条件包装器
     * 
     * @param query 查询条件参数
     * @return 查询条件包装器
     */
    private LambdaQueryWrapper<PlantAlarmEntity> getWrapper(PlantAlarmQuery query) {
        LambdaQueryWrapper<PlantAlarmEntity> wrapper = Wrappers.lambdaQuery();
        wrapper.groupBy(PlantAlarmEntity::getPlantName);
        wrapper.orderByDesc(PlantAlarmEntity::getStartTime);
        return wrapper;
    }

    /**
     * 保存告警信息
     * 
     * @param vo 告警信息数据对象
     */
    @Override
    public void save(PlantAlarmVO vo) {
        PlantAlarmEntity entity = PlantAlarmConvert.INSTANCE.convert(vo);

        baseMapper.insert(entity);
    }

    /**
     * 更新告警信息
     * 
     * @param vo 告警信息数据对象，包含需要更新的信息
     */
    @Override
    public void update(PlantAlarmVO vo) {
        PlantAlarmEntity entity = PlantAlarmConvert.INSTANCE.convert(vo);

        updateById(entity);
    }

    /**
     * 批量删除告警信息
     * 
     * @param idList 需要删除的告警信息ID列表
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(List<Long> idList) {
        removeByIds(idList);
    }

}