package com.botong.workorder.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.botong.api.module.system.SysOrgApi;
import com.botong.framework.common.constant.BtoConstant;
import com.botong.framework.common.exception.ServerException;
import com.botong.framework.common.page.PageResult;
import com.botong.framework.common.utils.AssertUtils;
import com.botong.framework.common.utils.DateUtils;
import com.botong.framework.common.utils.ExcelUtils;
import com.botong.framework.security.user.SecurityUser;
import com.botong.framework.security.user.UserDetail;
import com.botong.workorder.dao.WorkOrderStatisticDao;
import com.botong.workorder.entity.WorkOrderProcessManageViewEntity;
import com.botong.workorder.query.PlantElectricChargeAnalyzeQuery;
import com.botong.workorder.query.UserAlarmTypeStatisticsQuery;
import com.botong.workorder.query.WorkOrderStatisticQuery;
import com.botong.workorder.service.*;
import com.botong.workorder.vo.*;
import lombok.AllArgsConstructor;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.time.format.TextStyle;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/8/18 17:29
 */
@Service
@AllArgsConstructor
public class WorkOrderStatisticServiceImpl implements WorkOrderStatisticService {
    private final WorkOrderStatisticDao workOrderStatisticDao;
    private final WorkOrderProcessManageService workOrderProcessManageService;
    private final WorkOperationPersonInfoService workOperationPersonInfoService;
    private final WorkOperationInfoService workOperationInfoService;
    private final SysOrgApi sysOrgApi;
    private final PlantBaseInfoService plantBaseInfoService;


    @Override
    public void exportUserAlarmTypeStatistics(UserAlarmTypeStatisticsQuery query, HttpServletResponse response) {
        List<Map<String, Object>> maps = workOrderProcessManageService.getUserAlarmTypeStatistics(query);
        ExcelUtils.monthlyOpsWorkOrderSummary(response, "用户工单运维报表", maps);
    }

    @Override
    public List<ElectricChargeAnalyzeVO> getElectricChargeAnalyze(String yearMonth) {
        if (StrUtil.isBlank(yearMonth)) {
            yearMonth = DateUtil.format(DateUtil.offsetMonth(new Date(), -2), "yyyy-MM");
        }
        String monthField = getMonthName(yearMonth);
        return workOrderStatisticDao.getElectricChargeAnalyze(yearMonth, monthField);
    }

    @Override
    public PageResult<ElectricChargeAnalyzeVO> getPlantElectricChargeAnalyze(PlantElectricChargeAnalyzeQuery query) {
        Page<ElectricChargeAnalyzeVO> page = new Page<>(query.getPage(), query.getLimit());
        IPage<ElectricChargeAnalyzeVO> iPage = workOrderStatisticDao.getPlantElectricChargeAnalyze(query, page);
        return new PageResult<>(iPage.getRecords(), page.getTotal());
    }

    @Override
    public List<Map<String, Object>> getUserAlarmTypeStatistics(UserAlarmTypeStatisticsQuery query) {
        return workOrderProcessManageService.getUserAlarmTypeStatistics(query);
    }

    @Override
    public List<Map<String, Object>> monthlyOpsWorkOrderSummary(Date date) {
        DateTime start = DateUtil.beginOfMonth(date);
        DateTime end = DateUtil.endOfMonth(date);

        return workOrderProcessManageService.monthlyOpsWorkOrderSummary(start, end);

    }

    @Override
    public void exportMonthlyOpsWorkOrderSummary(Date date, HttpServletResponse response) {
        DateTime start = DateUtil.beginOfMonth(date);
        DateTime end = DateUtil.endOfMonth(date);
        List<Map<String, Object>> maps = workOrderProcessManageService.monthlyOpsWorkOrderSummary(start, end);
        ExcelUtils.monthlyOpsWorkOrderSummary(response, "月度工单运维报表", maps);
    }

    @Override
    public List<StatisticsByAlarmTimeVO> statisticsByAlarmTime(String year, String plantId) {

        return workOrderProcessManageService.statisticsByAlarmTime(year, plantId);
    }

    @Override
    public Map<String, Object> statisticPlantAndWorkOrder(List<Long> plantType, String startTime, String endTime) {
        List<Long> scopeList = CollUtil.isNotEmpty(plantType) ? sysOrgApi.getSubOrgIdList(plantType) : Collections.emptyList();

        SecurityContext securityContext = SecurityContextHolder.getContext();
        CompletableFuture<Map<String, Object>> workTask = CompletableFuture.supplyAsync(() -> {
            SecurityContextHolder.setContext(securityContext);
            return this.count(scopeList);
        });
        CompletableFuture<Map<String, Object>> plantTask = CompletableFuture.supplyAsync(() -> {
            SecurityContextHolder.setContext(securityContext);
            return plantBaseInfoService.getPlantNumInfo(scopeList);
        });

        CompletableFuture<Map<String, Object>> rateTask = CompletableFuture.supplyAsync(() -> {
            SecurityContextHolder.setContext(securityContext);
            return workOrderProcessManageService.getWorkOrderStatistics(scopeList, startTime, endTime);
        });

        // 等待所有任务完成
        CompletableFuture<Void> allTasks = CompletableFuture.allOf(workTask, plantTask);

        // 组合结果
        return allTasks.thenApply(v -> {
            Map<String, Object> workOrderMap = null;
            Map<String, Object> plantMap = null;
            Map<String, Object> rateMap = null;
            try {
                workOrderMap = workTask.get();
                plantMap = plantTask.get();
                rateMap = rateTask.get();
            } catch (Exception e) {
                throw new ServerException("操作失败");
            }
            HashMap<String, Object> map = new HashMap<>();
            map.put("workOrder", workOrderMap);
            map.put("plant", plantMap);
            map.put("rate", rateMap);
            return map;
        }).join();
    }


    @Override
    public Map<String, Object> count(List<Long> scopeList) {
        DateTime dateTime = new DateTime();
        String date = DateUtils.format(dateTime, DateUtils.DATE_TIME_PATTERN);
        return workOrderProcessManageService.count(date, scopeList);
    }


    @Override
    public List<StatisticsVO> statistics(WorkOrderStatisticQuery query) {
        // 获取当前用户
        UserDetail user = SecurityUser.getUser();
        // 抛出异常
        if (CollUtil.isEmpty(user.getDataScopeList()) && !user.getSuperAdmin().equals(BtoConstant.SUPER_ADMIN)) {
            throw new ServerException("数据权限未配置");
        }
        // 获取当前用户的工单id
        List<Long> mergedWorkIdList = new ArrayList<>();
        if (query.getPersonal() == 1) {
            List<Long> workIdList = workOperationPersonInfoService.getWorkIdsByUser(user.getId());
            mergedWorkIdList.addAll(workIdList);
            if (CollUtil.isEmpty(mergedWorkIdList)) {
                return Collections.emptyList();
            }
        }

        // 去重
        List<Long> newList = mergedWorkIdList.stream().distinct().collect(Collectors.toList());

        List<Long> scopeList = CollUtil.isNotEmpty(query.getPlantType()) ? sysOrgApi.getSubOrgIdList(query.getPlantType()) : Collections.emptyList();

        // 返回统计结果
        return workOrderStatisticDao.statistics(query, query.getDictTypeId(), user, newList, scopeList);
    }

    @Override
    public List<RegionCount> regionCount(WorkOrderStatisticQuery query) {
        // 校验参数
        AssertUtils.isNull(query.getType(), "type");
        UserDetail user = SecurityUser.getUser();
        // 抛出异常
        if (CollUtil.isEmpty(user.getDataScopeList()) && !user.getSuperAdmin().equals(BtoConstant.SUPER_ADMIN)) {
            throw new ServerException("数据权限未配置");
        }

        List<RegionCount> regionCount;
        List<Long> workIdList = new ArrayList<>();
        // 判断是否为个人查询
        if (query.getPersonal() == 1) {
            // 获取用户ID
            workIdList = workOperationPersonInfoService.getWorkIdsByUser(user.getId());
            // 判断是否为空
            if (CollUtil.isEmpty(workIdList)) {
                return Collections.emptyList();
            }
        }
        // 去重
        List<Long> newList = workIdList.stream().distinct().collect(Collectors.toList());
        // 查询数据
        List<Long> scopeList = CollUtil.isNotEmpty(query.getPlantType()) ? sysOrgApi.getSubOrgIdList(query.getPlantType()) : Collections.emptyList();

        regionCount = workOrderStatisticDao.regionCount(query, query.getType(), user, newList, scopeList);
        // 设置类型
        setType(query.getType(), regionCount);
        return regionCount;
    }


    private static void setType(String type, List<RegionCount> regionCount) {
        // 根据type参数设置类型
        switch (type) {
            case "country":
                type = "province";
                break;
            case "province":
                type = "city";
                break;
            case "city":
                type = "area";
                break;
            case "area":
                type = "town";
                break;
            default:
                break;
        }

        String finalType = type;
        // 遍历设置类型
        regionCount.forEach(item -> item.setType(finalType));
    }

    @Override
    public List<RegionTree> regionTree(WorkOrderStatisticQuery query) {
        UserDetail user = SecurityUser.getUser();
        // 抛出异常
        if (CollUtil.isEmpty(user.getDataScopeList()) && !user.getSuperAdmin().equals(BtoConstant.SUPER_ADMIN)) {
            throw new ServerException("数据权限未配置");
        }
        LambdaQueryWrapper<WorkOrderProcessManageViewEntity> wrapper = new LambdaQueryWrapper<>();
        List<Long> workIdList = new ArrayList<>();
        if (query.getPersonal() == 1) {
            workIdList = workOperationPersonInfoService.getWorkIdsByUser(user.getId());
            if (CollUtil.isEmpty(workIdList)) {
                return Collections.emptyList();
            }
            List<Long> newList = workIdList.stream().distinct().collect(Collectors.toList());
            wrapper.in(CollUtil.isNotEmpty(newList), WorkOrderProcessManageViewEntity::getWorkId, newList);
        }

        // 数据范围
        if (user.getSuperAdmin().equals(0)) {
            wrapper.and(w -> {
                w
                        .in(CollUtil.isNotEmpty(user.getDataScopeList()), WorkOrderProcessManageViewEntity::getOrgId, user.getDataScopeList())
                        .or()
                        .eq(ObjectUtil.isNotNull(user.getId()), WorkOrderProcessManageViewEntity::getUpUserId, user.getId());

            });
        }


        wrapper
                .in(CollUtil.isNotEmpty(query.getOvertimed()), WorkOrderProcessManageViewEntity::getOvertimed, query.getOvertimed())
                .eq(query.getPlatform() != null, WorkOrderProcessManageViewEntity::getPlatform, query.getPlatform());

        List<Long> scopeList = CollUtil.isNotEmpty(query.getPlantType()) ? sysOrgApi.getSubOrgIdList(query.getPlantType()) : Collections.emptyList();
        wrapper.in(CollUtil.isNotEmpty(scopeList), WorkOrderProcessManageViewEntity::getOrgId, scopeList);


        List<Long> list = workOrderProcessManageService
                .list(wrapper)
                .stream()
                .distinct()
                .map(WorkOrderProcessManageViewEntity::getWorkId)
                .collect(Collectors.toList());

        if (list.isEmpty()) {
            return Collections.emptyList();
        }
        List<RegionVO> region = workOrderStatisticDao.region(list, user);
        return buildRegionTree(region);
    }


    private static List<RegionTree> buildRegionTree(List<RegionVO> allData) {
        // 初始化id
        AtomicInteger id = new AtomicInteger(0); // 使用 AtomicInteger 保证线程安全

        // 计算初始容量
        int initialCapacity = Math.max((int) (allData.size() / 0.75) + 1, 16);
        // 创建一个map，用于存储名称节点
        Map<String, RegionTree> nameNode = new HashMap<>(initialCapacity);
        // 创建一个list，用于存储根节点
        List<RegionTree> rootNodes = new ArrayList<>();
        // 遍历所有数据
        for (RegionVO data : allData) {
            // 如果数据为空，则跳过
            if (data == null) {
                continue;
            }
            // 获取国家节点
            RegionTree countryNode = handleData(null, nameNode.get(data.getCountry()), "country", data.getCountry(), nameNode, rootNodes, id.incrementAndGet());

            // 获取省份节点
            RegionTree provinceNode = handleData(countryNode, nameNode.get(data.getProvince()), "province", data.getProvince(), nameNode, rootNodes, id.incrementAndGet());

            // 获取城市节点
            RegionTree cityNode = handleData(provinceNode, nameNode.get(data.getCity()), "city", data.getCity(), nameNode, rootNodes, id.incrementAndGet());


            // 更新叶子节点数量
            updateLeafCount(cityNode, null);
            updateLeafCount(provinceNode, cityNode);
            updateLeafCount(countryNode, provinceNode);

        }
        // 计算count
        // 计算节点数量
        calculateCount(rootNodes);
        // 返回根节点列表
        return rootNodes;
    }

    private static RegionTree handleData(RegionTree parentNode, RegionTree childrenNode, String layer, String name, Map<String, RegionTree> nameNode, List<RegionTree> rootNodes, Integer id) {
        if (childrenNode == null && StrUtil.isNotEmpty(name)) {
            childrenNode = new RegionTree(name);
            nameNode.put(name, childrenNode);
            if ("country".equals(layer)) {
                if (ObjectUtil.isNotNull(childrenNode)) {
                    childrenNode.setLayer(layer);
                    childrenNode.setId(id);
                    rootNodes.add(childrenNode);
                }
            } else {
                if (ObjectUtil.isNotNull(parentNode)) {
                    // 设置省份节点层
                    childrenNode.setLayer(layer);
                    // 设置省份节点id
                    childrenNode.setId(id);
                    // 将省份节点放入国家节点中
                    parentNode.getChildren().add(childrenNode);
                }
            }
        }

        return childrenNode;
    }

    /**
     * 计算count
     *
     * @param nodes 节点
     * <AUTHOR>
     * @since 2023-08-18 09:47:56
     */

    private static void calculateCount(List<RegionTree> nodes) {
        for (RegionTree node : nodes) {
            updateCount(node);
        }
    }

    /**
     * 计算子节点的count之和并累加到当前节点的count
     *
     * @param node 节点
     * @return int
     * <AUTHOR>
     * @since 2023-08-18 09:47:33
     */
    private static int updateCount(RegionTree node) {
        int count = 0;
        // 遍历子节点
        for (RegionTree child : node.getChildren()) {
            // 递归计算子节点数量
            int childCount = updateCount(child);
            // 累加子节点数量
            count += childCount;
        }
        // 累加当前节点数量
        count += Optional.ofNullable(node.getCount()).orElse(0);
        // 设置当前节点数量
        node.setCount(count);
        return count;
    }

    /**
     * 计算叶子节点的count
     *
     * @param root     当前节点
     * @param children 子节点
     * <AUTHOR>
     * @since 2023-08-18 09:46:46
     */
    private static void updateLeafCount(RegionTree root, RegionTree children) {
        if (root != null) {
            // 判断是否为叶子节点
            if (root.getChildren().isEmpty() || children == null) {
                int count = Optional.ofNullable(root.getCount()).orElse(0);
                root.setCount(count + 1);
            }
        }
    }

    public static String getMonthName(String monthNumber) {
        try {
            // 1. 解析字符串为 YearMonth 对象（格式: yyyy-MM）
            YearMonth yearMonth = YearMonth.parse(monthNumber, DateTimeFormatter.ofPattern("yyyy-MM"));
            // 2. 获取月份名称
            return yearMonth.getMonth().getDisplayName(TextStyle.FULL, Locale.ENGLISH).toLowerCase();
        } catch (DateTimeParseException e) {
            throw new IllegalArgumentException("Invalid date format. Expected 'yyyy-MM', e.g., '2025-02'");
        }
    }
}
