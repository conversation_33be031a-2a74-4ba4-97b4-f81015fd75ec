package com.botong.workorder.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.botong.api.module.system.SysOrgApi;
import com.botong.framework.common.constant.BtoConstant;
import com.botong.framework.common.exception.ServerException;
import com.botong.framework.common.page.PageResult;
import com.botong.framework.common.utils.AssertUtils;
import com.botong.framework.common.utils.DateUtils;
import com.botong.framework.common.utils.ExcelUtils;
import com.botong.framework.security.user.SecurityUser;
import com.botong.framework.security.user.UserDetail;
import com.botong.workorder.dao.WorkOrderStatisticDao;
import com.botong.workorder.entity.WorkOrderProcessManageViewEntity;
import com.botong.workorder.query.PlantElectricChargeAnalyzeQuery;
import com.botong.workorder.query.UserAlarmTypeStatisticsQuery;
import com.botong.workorder.query.WorkOrderStatisticQuery;
import com.botong.workorder.service.*;
import com.botong.workorder.vo.*;
import lombok.AllArgsConstructor;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.time.format.TextStyle;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 工单统计服务实现类
 * 提供工单相关的统计功能实现，包括用户报警类型统计、月度运维工单汇总、电费分析、区域统计等
 * 主要负责处理各类工单数据的统计分析和报表导出功能
 *
 * <AUTHOR>
 * @since 2023/8/18 17:29
 */
@Service
@AllArgsConstructor
public class WorkOrderStatisticServiceImpl implements WorkOrderStatisticService {
    private final WorkOrderStatisticDao workOrderStatisticDao;
    private final WorkOrderProcessManageService workOrderProcessManageService;
    private final WorkOperationPersonInfoService workOperationPersonInfoService;
    private final WorkOperationInfoService workOperationInfoService;
    private final SysOrgApi sysOrgApi;
    private final PlantBaseInfoService plantBaseInfoService;


    /**
     * 导出用户报警类型统计报表
     * 根据查询条件导出用户报警类型统计数据到Excel文件
     * 
     * @param query 用户报警类型统计查询条件
     * @param response HTTP响应对象，用于输出Excel文件
     */
    @Override
    public void exportUserAlarmTypeStatistics(UserAlarmTypeStatisticsQuery query, HttpServletResponse response) {
        List<Map<String, Object>> maps = workOrderProcessManageService.getUserAlarmTypeStatistics(query);
        ExcelUtils.monthlyOpsWorkOrderSummary(response, "用户工单运维报表", maps);
    }

    /**
     * 获取电费分析数据
     * 根据年月获取电费分析数据，如果未指定年月则默认获取前两月的数据
     * 
     * @param yearMonth 年月字符串，格式为yyyy-MM
     * @return 电费分析数据列表
     */
    @Override
    public List<ElectricChargeAnalyzeVO> getElectricChargeAnalyze(String yearMonth) {
        if (StrUtil.isBlank(yearMonth)) {
            yearMonth = DateUtil.format(DateUtil.offsetMonth(new Date(), -2), "yyyy-MM");
        }
        String monthField = getMonthName(yearMonth);
        return workOrderStatisticDao.getElectricChargeAnalyze(yearMonth, monthField);
    }

    /**
     * 分页查询电站电费分析数据
     * 根据查询条件分页获取电站电费分析信息
     * 
     * @param query 电站电费分析查询条件
     * @return 分页查询结果，包含电站电费分析数据列表
     */
    @Override
    public PageResult<ElectricChargeAnalyzeVO> getPlantElectricChargeAnalyze(PlantElectricChargeAnalyzeQuery query) {
        Page<ElectricChargeAnalyzeVO> page = new Page<>(query.getPage(), query.getLimit());
        IPage<ElectricChargeAnalyzeVO> iPage = workOrderStatisticDao.getPlantElectricChargeAnalyze(query, page);
        return new PageResult<>(iPage.getRecords(), page.getTotal());
    }

    /**
     * 获取用户报警类型统计信息
     * 根据查询条件获取用户报警类型统计数据，调用工单流程管理服务实现
     * 
     * @param query 用户报警类型统计查询条件
     * @return 用户报警类型统计结果列表
     */
    @Override
    public List<Map<String, Object>> getUserAlarmTypeStatistics(UserAlarmTypeStatisticsQuery query) {
        return workOrderProcessManageService.getUserAlarmTypeStatistics(query);
    }

    /**
     * 获取月度运维工单汇总统计
     * 根据指定日期获取该月度的运维工单汇总数据
     * 
     * @param date 指定日期
     * @return 月度运维工单汇总统计列表
     */
    @Override
    public List<Map<String, Object>> monthlyOpsWorkOrderSummary(Date date) {
        DateTime start = DateUtil.beginOfMonth(date);
        DateTime end = DateUtil.endOfMonth(date);

        return workOrderProcessManageService.monthlyOpsWorkOrderSummary(start, end);
    }

    /**
     * 导出月度运维工单汇总报表
     * 根据指定日期导出该月度的运维工单汇总数据到Excel文件
     * 
     * @param date 指定日期
     * @param response HTTP响应对象，用于输出Excel文件
     */
    @Override
    public void exportMonthlyOpsWorkOrderSummary(Date date, HttpServletResponse response) {
        DateTime start = DateUtil.beginOfMonth(date);
        DateTime end = DateUtil.endOfMonth(date);
        List<Map<String, Object>> maps = workOrderProcessManageService.monthlyOpsWorkOrderSummary(start, end);
        ExcelUtils.monthlyOpsWorkOrderSummary(response, "月度工单运维报表", maps);
    }

    /**
     * 按告警时间统计工单信息
     * 根据年份和电站ID统计告警时间的工单数据
     * 
     * @param year 统计年份
     * @param plantId 电站ID，可为空
     * @return 按告警时间统计的工单信息列表
     */
    @Override
    public List<StatisticsByAlarmTimeVO> statisticsByAlarmTime(String year, String plantId) {
        return workOrderProcessManageService.statisticsByAlarmTime(year, plantId);
    }

    /**
     * 统计电站和工单信息
     * 异步统计电站数量和工单数量信息，包括工单统计、电站统计和完成率统计
     * 使用CompletableFuture实现并发处理，提高统计效率
     * 
     * @param plantType 电站类型列表
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 包含电站和工单统计信息的Map
     */
    @Override
    public Map<String, Object> statisticPlantAndWorkOrder(List<Long> plantType, String startTime, String endTime) {
        List<Long> scopeList = CollUtil.isNotEmpty(plantType) ? sysOrgApi.getSubOrgIdList(plantType) : Collections.emptyList();

        SecurityContext securityContext = SecurityContextHolder.getContext();
        CompletableFuture<Map<String, Object>> workTask = CompletableFuture.supplyAsync(() -> {
            SecurityContextHolder.setContext(securityContext);
            return this.count(scopeList);
        });
        CompletableFuture<Map<String, Object>> plantTask = CompletableFuture.supplyAsync(() -> {
            SecurityContextHolder.setContext(securityContext);
            return plantBaseInfoService.getPlantNumInfo(scopeList);
        });

        CompletableFuture<Map<String, Object>> rateTask = CompletableFuture.supplyAsync(() -> {
            SecurityContextHolder.setContext(securityContext);
            return workOrderProcessManageService.getWorkOrderStatistics(scopeList, startTime, endTime);
        });

        // 等待所有任务完成
        CompletableFuture<Void> allTasks = CompletableFuture.allOf(workTask, plantTask);

        // 组合结果
        return allTasks.thenApply(v -> {
            Map<String, Object> workOrderMap = null;
            Map<String, Object> plantMap = null;
            Map<String, Object> rateMap = null;
            try {
                workOrderMap = workTask.get();
                plantMap = plantTask.get();
                rateMap = rateTask.get();
            } catch (Exception e) {
                throw new ServerException("操作失败");
            }
            HashMap<String, Object> map = new HashMap<>();
            map.put("workOrder", workOrderMap);
            map.put("plant", plantMap);
            map.put("rate", rateMap);
            return map;
        }).join();
    }


    /**
     * 统计工单数量信息
     * 根据数据权限范围统计工单数量，包括总数量、本月数量、本年数量等
     * 
     * @param scopeList 数据权限范围列表
     * @return 工单数量统计结果Map
     */
    @Override
    public Map<String, Object> count(List<Long> scopeList) {
        DateTime dateTime = new DateTime();
        String date = DateUtils.format(dateTime, DateUtils.DATE_TIME_PATTERN);
        return workOrderProcessManageService.count(date, scopeList);
    }


    /**
     * 获取工单统计数据
     * 根据查询条件获取工单统计数据，支持个人查询和按电站类型过滤
     * 
     * @param query 工单统计查询条件
     * @return 工单统计数据列表
     */
    @Override
    public List<StatisticsVO> statistics(WorkOrderStatisticQuery query) {
        // 获取当前用户
        UserDetail user = SecurityUser.getUser();
        // 抛出异常
        if (CollUtil.isEmpty(user.getDataScopeList()) && !user.getSuperAdmin().equals(BtoConstant.SUPER_ADMIN)) {
            throw new ServerException("数据权限未配置");
        }
        // 获取当前用户的工单id
        List<Long> mergedWorkIdList = new ArrayList<>();
        if (query.getPersonal() == 1) {
            List<Long> workIdList = workOperationPersonInfoService.getWorkIdsByUser(user.getId());
            mergedWorkIdList.addAll(workIdList);
            if (CollUtil.isEmpty(mergedWorkIdList)) {
                return Collections.emptyList();
            }
        }

        // 去重
        List<Long> newList = mergedWorkIdList.stream().distinct().collect(Collectors.toList());

        List<Long> scopeList = CollUtil.isNotEmpty(query.getPlantType()) ? sysOrgApi.getSubOrgIdList(query.getPlantType()) : Collections.emptyList();

        // 返回统计结果
        return workOrderStatisticDao.statistics(query, query.getDictTypeId(), user, newList, scopeList);
    }

    /**
     * 获取区域工单统计信息
     * 根据区域类型和查询条件获取区域工单统计数据，支持个人查询和按电站类型过滤
     * 
     * @param query 工单统计查询条件
     * @return 区域工单统计信息列表
     */
    @Override
    public List<RegionCount> regionCount(WorkOrderStatisticQuery query) {
        // 校验参数
        AssertUtils.isNull(query.getType(), "type");
        UserDetail user = SecurityUser.getUser();
        // 抛出异常
        if (CollUtil.isEmpty(user.getDataScopeList()) && !user.getSuperAdmin().equals(BtoConstant.SUPER_ADMIN)) {
            throw new ServerException("数据权限未配置");
        }

        List<RegionCount> regionCount;
        List<Long> workIdList = new ArrayList<>();
        // 判断是否为个人查询
        if (query.getPersonal() == 1) {
            // 获取用户ID
            workIdList = workOperationPersonInfoService.getWorkIdsByUser(user.getId());
            // 判断是否为空
            if (CollUtil.isEmpty(workIdList)) {
                return Collections.emptyList();
            }
        }
        // 去重
        List<Long> newList = workIdList.stream().distinct().collect(Collectors.toList());
        // 查询数据
        List<Long> scopeList = CollUtil.isNotEmpty(query.getPlantType()) ? sysOrgApi.getSubOrgIdList(query.getPlantType()) : Collections.emptyList();

        regionCount = workOrderStatisticDao.regionCount(query, query.getType(), user, newList, scopeList);
        // 设置类型
        setType(query.getType(), regionCount);
        return regionCount;
    }


    /**
     * 设置区域类型
     * 根据当前区域类型设置下一级区域类型，用于区域树形结构的层级关系
     * 
     * @param type 当前区域类型
     * @param regionCount 区域统计信息列表
     */
    private static void setType(String type, List<RegionCount> regionCount) {
        // 根据type参数设置类型
        switch (type) {
            case "country":
                type = "province";
                break;
            case "province":
                type = "city";
                break;
            case "city":
                type = "area";
                break;
            case "area":
                type = "town";
                break;
            default:
                break;
        }

        String finalType = type;
        // 遍历设置类型
        regionCount.forEach(item -> item.setType(finalType));
    }

    /**
     * 获取区域树形结构
     * 根据查询条件获取工单数据的区域树形结构，支持个人查询和按电站类型过滤
     * 构建包含国家、省份、城市等层级的树形结构数据
     * 
     * @param query 工单统计查询条件
     * @return 区域树形结构列表
     */
    @Override
    public List<RegionTree> regionTree(WorkOrderStatisticQuery query) {
        UserDetail user = SecurityUser.getUser();
        // 抛出异常
        if (CollUtil.isEmpty(user.getDataScopeList()) && !user.getSuperAdmin().equals(BtoConstant.SUPER_ADMIN)) {
            throw new ServerException("数据权限未配置");
        }
        LambdaQueryWrapper<WorkOrderProcessManageViewEntity> wrapper = new LambdaQueryWrapper<>();
        List<Long> workIdList = new ArrayList<>();
        if (query.getPersonal() == 1) {
            workIdList = workOperationPersonInfoService.getWorkIdsByUser(user.getId());
            if (CollUtil.isEmpty(workIdList)) {
                return Collections.emptyList();
            }
            List<Long> newList = workIdList.stream().distinct().collect(Collectors.toList());
            wrapper.in(CollUtil.isNotEmpty(newList), WorkOrderProcessManageViewEntity::getWorkId, newList);
        }

        // 数据范围
        if (user.getSuperAdmin().equals(0)) {
            wrapper.and(w -> {
                w
                        .in(CollUtil.isNotEmpty(user.getDataScopeList()), WorkOrderProcessManageViewEntity::getOrgId, user.getDataScopeList())
                        .or()
                        .eq(ObjectUtil.isNotNull(user.getId()), WorkOrderProcessManageViewEntity::getUpUserId, user.getId());

            });
        }

        wrapper
                .in(CollUtil.isNotEmpty(query.getOvertimed()), WorkOrderProcessManageViewEntity::getOvertimed, query.getOvertimed())
                .eq(query.getPlatform() != null, WorkOrderProcessManageViewEntity::getPlatform, query.getPlatform());

        List<Long> scopeList = CollUtil.isNotEmpty(query.getPlantType()) ? sysOrgApi.getSubOrgIdList(query.getPlantType()) : Collections.emptyList();
        wrapper.in(CollUtil.isNotEmpty(scopeList), WorkOrderProcessManageViewEntity::getOrgId, scopeList);

        List<Long> list = workOrderProcessManageService
                .list(wrapper)
                .stream()
                .distinct()
                .map(WorkOrderProcessManageViewEntity::getWorkId)
                .collect(Collectors.toList());

        if (list.isEmpty()) {
            return Collections.emptyList();
        }
        List<RegionVO> region = workOrderStatisticDao.region(list, user);
        return buildRegionTree(region);
    }


    /**
     * 构建区域树形结构
     * 根据区域数据构建包含国家、省份、城市等层级的树形结构
     * 使用HashMap存储节点避免重复创建，使用AtomicInteger保证ID唯一性
     * 
     * @param allData 所有区域数据列表
     * @return 区域树形结构列表
     */
    private static List<RegionTree> buildRegionTree(List<RegionVO> allData) {
        // 初始化id
        AtomicInteger id = new AtomicInteger(0); // 使用 AtomicInteger 保证线程安全

        // 计算初始容量
        int initialCapacity = Math.max((int) (allData.size() / 0.75) + 1, 16);
        // 创建一个map，用于存储名称节点
        Map<String, RegionTree> nameNode = new HashMap<>(initialCapacity);
        // 创建一个list，用于存储根节点
        List<RegionTree> rootNodes = new ArrayList<>();
        // 遍历所有数据
        for (RegionVO data : allData) {
            // 如果数据为空，则跳过
            if (data == null) {
                continue;
            }
            // 获取国家节点
            RegionTree countryNode = handleData(null, nameNode.get(data.getCountry()), "country", data.getCountry(), nameNode, rootNodes, id.incrementAndGet());

            // 获取省份节点
            RegionTree provinceNode = handleData(countryNode, nameNode.get(data.getProvince()), "province", data.getProvince(), nameNode, rootNodes, id.incrementAndGet());

            // 获取城市节点
            RegionTree cityNode = handleData(provinceNode, nameNode.get(data.getCity()), "city", data.getCity(), nameNode, rootNodes, id.incrementAndGet());

            // 更新叶子节点数量
            updateLeafCount(cityNode, null);
            updateLeafCount(provinceNode, cityNode);
            updateLeafCount(countryNode, provinceNode);
        }
        // 计算count
        // 计算节点数量
        calculateCount(rootNodes);
        // 返回根节点列表
        return rootNodes;
    }

    /**
     * 处理区域数据节点
     * 根据区域层级和数据创建或获取对应的区域节点，支持树形结构的构建
     * 
     * @param parentNode 父节点
     * @param childrenNode 子节点
     * @param layer 当前层级
     * @param name 区域名称
     * @param nameNode 区域节点映射表
     * @param rootNodes 根节点列表
     * @param id 节点ID
     * @return 处理后的区域节点
     */
    private static RegionTree handleData(RegionTree parentNode, RegionTree childrenNode, String layer, String name, Map<String, RegionTree> nameNode, List<RegionTree> rootNodes, Integer id) {
        if (childrenNode == null && StrUtil.isNotEmpty(name)) {
            childrenNode = new RegionTree(name);
            nameNode.put(name, childrenNode);
            if ("country".equals(layer)) {
                if (ObjectUtil.isNotNull(childrenNode)) {
                    childrenNode.setLayer(layer);
                    childrenNode.setId(id);
                    rootNodes.add(childrenNode);
                }
            } else {
                if (ObjectUtil.isNotNull(parentNode)) {
                    // 设置省份节点层
                    childrenNode.setLayer(layer);
                    // 设置省份节点id
                    childrenNode.setId(id);
                    // 将省份节点放入国家节点中
                    parentNode.getChildren().add(childrenNode);
                }
            }
        }

        return childrenNode;
    }

    /**
     * 计算节点总数
     * 递归计算所有节点的总数，包括子节点数量
     * 
     * @param nodes 节点列表
     */
    private static void calculateCount(List<RegionTree> nodes) {
        for (RegionTree node : nodes) {
            updateCount(node);
        }
    }

    /**
     * 递归计算节点数量
     * 计算当前节点及其所有子节点的数量总和
     * 
     * @param node 当前节点
     * @return 节点总数
     */
    private static int updateCount(RegionTree node) {
        int count = 0;
        // 遍历子节点
        for (RegionTree child : node.getChildren()) {
            // 递归计算子节点数量
            int childCount = updateCount(child);
            // 累加子节点数量
            count += childCount;
        }
        // 累加当前节点数量
        count += Optional.ofNullable(node.getCount()).orElse(0);
        // 设置当前节点数量
        node.setCount(count);
        return count;
    }

    /**
     * 更新叶子节点数量
     * 为叶子节点增加计数，用于统计该区域下的工单数量
     * 
     * @param root 当前节点
     * @param children 子节点
     */
    private static void updateLeafCount(RegionTree root, RegionTree children) {
        if (root != null) {
            // 判断是否为叶子节点
            if (root.getChildren().isEmpty() || children == null) {
                int count = Optional.ofNullable(root.getCount()).orElse(0);
                root.setCount(count + 1);
            }
        }
    }

    /**
     * 获取月份名称
     * 将年月字符串转换为对应的英文月份名称
     * 
     * @param monthNumber 年月字符串，格式为yyyy-MM
     * @return 英文月份名称（小写）
     * @throws IllegalArgumentException 如果日期格式无效
     */
    public static String getMonthName(String monthNumber) {
        try {
            // 1. 解析字符串为 YearMonth 对象（格式: yyyy-MM）
            YearMonth yearMonth = YearMonth.parse(monthNumber, DateTimeFormatter.ofPattern("yyyy-MM"));
            // 2. 获取月份名称
            return yearMonth.getMonth().getDisplayName(TextStyle.FULL, Locale.ENGLISH).toLowerCase();
        } catch (DateTimeParseException e) {
            throw new IllegalArgumentException("Invalid date format. Expected 'yyyy-MM', e.g., '2025-02'");
        }
    }
}
