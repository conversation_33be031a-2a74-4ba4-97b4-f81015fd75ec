package com.botong.workorder.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.botong.framework.common.excel.ExcelFinishCallBack;
import com.botong.framework.common.utils.DateUtils;
import com.botong.framework.common.utils.ExcelUtils;
import com.botong.framework.mybatis.service.impl.BaseServiceImpl;
import com.botong.workorder.convert.WorkChargeConvert;
import com.botong.workorder.dao.WorkChargeDao;
import com.botong.workorder.entity.WorkChargeEntity;
import com.botong.workorder.query.WorkChargeQuery;
import com.botong.workorder.service.PlantBaseInfoService;
import com.botong.workorder.service.WorkChargeService;
import com.botong.workorder.vo.ChargeStatisticsVO;
import com.botong.workorder.vo.PlantBaseInfoVO;
import com.botong.workorder.vo.WorkChargeVO;
import com.fhs.trans.service.impl.TransService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;

/**
 * 二次增值台账服务实现类
 * 提供二次增值台账相关的业务操作实现，包括分页查询、保存、更新、删除、导出、导入等功能
 *
 * <AUTHOR>
 * @since 1.0.0 2025-02-24
 */
@Service
@AllArgsConstructor
public class WorkChargeServiceImpl extends BaseServiceImpl<WorkChargeDao, WorkChargeEntity> implements WorkChargeService {

    private final TransService transService;

    private final PlantBaseInfoService plantBaseInfoService;

    /**
     * 分页查询二次增值台账列表
     * 根据查询条件获取二次增值台账信息，包含统计信息
     * 
     * @param query 查询条件参数
     * @return 分页查询结果，包含台账列表和统计信息
     */
    @Override
    public HashMap<String, Object> page(WorkChargeQuery query) {
        IPage<WorkChargeEntity> page = baseMapper.page(getPage(query), query);
        ChargeStatisticsVO chargeStatisticsVO = baseMapper.getChargeStatistics(query);
        HashMap<String, Object> map = new HashMap<>();
        map.put("chargeStatisticsVO", chargeStatisticsVO);
        map.put("total", page.getTotal());
        map.put("list", WorkChargeConvert.INSTANCE.convertList(page.getRecords()));
        return map;
    }

    /**
     * 构建查询条件包装器
     * 根据查询条件构建MyBatis-Plus的查询包装器
     * 
     * @param query 查询条件参数
     * @return 查询条件包装器
     */
    private LambdaQueryWrapper<WorkChargeEntity> getWrapper(WorkChargeQuery query) {
        LambdaQueryWrapper<WorkChargeEntity> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(Objects.nonNull(query.getWorkOrderId()), WorkChargeEntity::getWorkOrderId, query.getWorkOrderId());
        wrapper.eq(Objects.nonNull(query.getIsAssociated()), WorkChargeEntity::getIsAssociated, query.getIsAssociated());
        return wrapper;
    }

    /**
     * 保存二次增值台账信息
     * 将VO对象转换为实体对象并保存到数据库
     * 
     * @param vo 二次增值台账数据对象
     */
    @Override
    public void save(WorkChargeVO vo) {
        WorkChargeEntity entity = WorkChargeConvert.INSTANCE.convert(vo);
        baseMapper.insert(entity);
    }

    /**
     * 更新二次增值台账信息
     * 将VO对象转换为实体对象并更新到数据库
     * 
     * @param vo 二次增值台账数据对象，包含需要更新的信息
     */
    @Override
    public void update(WorkChargeVO vo) {
        WorkChargeEntity entity = WorkChargeConvert.INSTANCE.convert(vo);
        updateById(entity);
    }

    /**
     * 批量删除二次增值台账
     * 根据台账ID列表批量删除二次增值台账信息
     * 
     * @param idList 需要删除的台账ID列表
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(List<Long> idList) {
        removeByIds(idList);
    }

    /**
     * 根据工单ID查询二次增值台账列表
     * 根据工单ID获取对应的二次增值台账信息列表
     * 
     * @param workId 工单ID
     * @return 二次增值台账列表数据
     */
    @Override
    public List<WorkChargeVO> listByWorkId(Long workId) {
        return baseMapper.getList(workId);
    }

    /**
     * 导出二次增值台账数据
     * 将所有二次增值台账数据导出到Excel文件
     */
    @Override
    public void export() {
        List<WorkChargeVO> list = listByWorkId(null);
        transService.transBatch(list);
        // 写到浏览器打开
        ExcelUtils.excelExport(WorkChargeVO.class, "work_charge_excel" + DateUtils.format(new Date()), null, list);
    }

    /**
     * 通过Excel文件导入二次增值台账数据
     * 解析Excel文件内容并批量保存二次增值台账信息
     * 
     * @param file Excel文件
     */
    @Override
    public void importByExcel(MultipartFile file) {
        ExcelUtils.readAnalysis(file, 1, WorkChargeVO.class, new ExcelFinishCallBack<WorkChargeVO>() {
            @Override
            public void doAfterAllAnalysed(List<WorkChargeVO> result) {
                saveWorkCharge(result);
            }

            @Override
            public void doSaveBatch(List<WorkChargeVO> result) {
                saveWorkCharge(result);
            }

            /**
             * 保存二次增值台账数据
             * 将Excel解析结果转换为实体对象并批量保存
             * 
             * @param result 解析后的台账数据列表
             */
            private void saveWorkCharge(List<WorkChargeVO> result) {
                ExcelUtils.parseDict(result);
                List<WorkChargeEntity> workChargeEntities = WorkChargeConvert.INSTANCE.convertEntitys(result);
                for (WorkChargeEntity workChargeEntity : workChargeEntities) {
                    if ("0".equals(workChargeEntity.getPlatformType())) {
                        PlantBaseInfoVO infoByIdOrName = plantBaseInfoService.getInfoByIdOrName(workChargeEntity.getPlantName(), 0);
                        if (Objects.nonNull(infoByIdOrName)) {
                            workChargeEntity.setPlantUid(infoByIdOrName.getPlantId());
                        }
                    }
                }
                saveBatch(workChargeEntities);
            }
        });
    }

}