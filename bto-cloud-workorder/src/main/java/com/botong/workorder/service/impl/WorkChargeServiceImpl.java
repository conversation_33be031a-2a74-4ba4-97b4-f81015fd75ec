package com.botong.workorder.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.botong.framework.common.excel.ExcelFinishCallBack;
import com.botong.framework.common.utils.DateUtils;
import com.botong.framework.common.utils.ExcelUtils;
import com.botong.framework.mybatis.service.impl.BaseServiceImpl;
import com.botong.workorder.convert.WorkChargeConvert;
import com.botong.workorder.dao.WorkChargeDao;
import com.botong.workorder.entity.WorkChargeEntity;
import com.botong.workorder.query.WorkChargeQuery;
import com.botong.workorder.service.PlantBaseInfoService;
import com.botong.workorder.service.WorkChargeService;
import com.botong.workorder.vo.ChargeStatisticsVO;
import com.botong.workorder.vo.PlantBaseInfoVO;
import com.botong.workorder.vo.WorkChargeVO;
import com.fhs.trans.service.impl.TransService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;

/**
 * 二次增值台账表
 *
 * <AUTHOR>
 * @since 1.0.0 2025-02-24
 */
@Service
@AllArgsConstructor
public class WorkChargeServiceImpl extends BaseServiceImpl<WorkChargeDao, WorkChargeEntity> implements WorkChargeService {

    private final TransService transService;

    private final PlantBaseInfoService plantBaseInfoService;

    @Override
    public HashMap<String, Object> page(WorkChargeQuery query) {

        IPage<WorkChargeEntity> page = baseMapper.page(getPage(query), query);
        ChargeStatisticsVO chargeStatisticsVO = baseMapper.getChargeStatistics(query);
        HashMap<String, Object> map = new HashMap<>();
        map.put("chargeStatisticsVO", chargeStatisticsVO);
        map.put("total", page.getTotal());
        map.put("list", WorkChargeConvert.INSTANCE.convertList(page.getRecords()));
        return map;
    }

    private LambdaQueryWrapper<WorkChargeEntity> getWrapper(WorkChargeQuery query) {
        LambdaQueryWrapper<WorkChargeEntity> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(Objects.nonNull(query.getWorkOrderId()), WorkChargeEntity::getWorkOrderId, query.getWorkOrderId());
        wrapper.eq(Objects.nonNull(query.getIsAssociated()), WorkChargeEntity::getIsAssociated, query.getIsAssociated());
        return wrapper;
    }

    @Override
    public void save(WorkChargeVO vo) {
        WorkChargeEntity entity = WorkChargeConvert.INSTANCE.convert(vo);

        baseMapper.insert(entity);
    }

    @Override
    public void update(WorkChargeVO vo) {
        WorkChargeEntity entity = WorkChargeConvert.INSTANCE.convert(vo);

        updateById(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(List<Long> idList) {
        removeByIds(idList);
    }

    @Override
    public List<WorkChargeVO> listByWorkId(Long workId) {

        return baseMapper.getList(workId);
    }

    @Override
    public void export() {
        List<WorkChargeVO> list = listByWorkId(null);
        transService.transBatch(list);
        // 写到浏览器打开
        ExcelUtils.excelExport(WorkChargeVO.class, "work_charge_excel" + DateUtils.format(new Date()), null, list);
    }

    @Override
    public void importByExcel(MultipartFile file) {
        ExcelUtils.readAnalysis(file, 1, WorkChargeVO.class, new ExcelFinishCallBack<WorkChargeVO>() {
            @Override
            public void doAfterAllAnalysed(List<WorkChargeVO> result) {
                saveWorkCharge(result);
            }

            @Override
            public void doSaveBatch(List<WorkChargeVO> result) {
                saveWorkCharge(result);
            }

            private void saveWorkCharge(List<WorkChargeVO> result) {
                ExcelUtils.parseDict(result);
                List<WorkChargeEntity> workChargeEntities = WorkChargeConvert.INSTANCE.convertEntitys(result);
                for (WorkChargeEntity workChargeEntity : workChargeEntities) {
                    if ("0".equals(workChargeEntity.getPlatformType())) {
                        PlantBaseInfoVO infoByIdOrName = plantBaseInfoService.getInfoByIdOrName(workChargeEntity.getPlantName(), 0);
                        if (Objects.nonNull(infoByIdOrName)) {
                            workChargeEntity.setPlantUid(infoByIdOrName.getPlantId());
                        }
                    }
                }
                saveBatch(workChargeEntities);
            }
        });
    }

}