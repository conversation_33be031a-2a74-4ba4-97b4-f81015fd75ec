package com.botong.workorder.service;

import com.botong.framework.common.page.PageResult;
import com.botong.framework.mybatis.service.BaseService;
import com.botong.workorder.entity.PlantMaterialInfoEntity;
import com.botong.workorder.query.PlantMaterialInfoQuery;
import com.botong.workorder.vo.PlantMaterialInfoTree;
import com.botong.workorder.vo.PlantMaterialInfoVO;

import java.util.List;

/**
 * 电站材料表
 *
 * <AUTHOR> 
 * @since  2023-08-23
 */
public interface PlantMaterialInfoService extends BaseService<PlantMaterialInfoEntity> {

    /**
     * 分页查询
     *
     * @param query 查询
     * @return {@link PageResult }<{@link PlantMaterialInfoTree }>
     * <AUTHOR>
     * @since 2023-08-24 17:26:40
     */
    PageResult<PlantMaterialInfoTree> page(PlantMaterialInfoQuery query);


    /**
     * 新增
     *
     * @param vo vo
     * <AUTHOR>
     * @since 2023-08-24 17:26:40
     */
    void save(PlantMaterialInfoVO vo);

    /**
     * 修改
     *
     * @param vo vo
     * <AUTHOR>
     * @since 2023-08-24 17:26:40
     */
    void update(PlantMaterialInfoVO vo);

    /**
     * 删除
     *
     * @param idList id集合
     * <AUTHOR>
     * @since 2023-08-24 17:26:40
     */
    void delete(List<Long> idList);

    /**
     * 材料树
     *
     * @return {@link List }<{@link PlantMaterialInfoVO }>
     * <AUTHOR>
     * @since 2023-08-31 11:47:55
     */
    List<PlantMaterialInfoTree> materialList();
}