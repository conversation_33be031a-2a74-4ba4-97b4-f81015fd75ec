package com.botong.workorder.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.botong.framework.common.page.PageResult;
import com.botong.framework.mybatis.service.impl.BaseServiceImpl;
import com.botong.workorder.convert.WorkApplyInfoConvert;
import com.botong.workorder.dao.WorkApplyInfoDao;
import com.botong.workorder.entity.WorkApplyInfoEntity;
import com.botong.workorder.query.WorkApplyInfoQuery;
import com.botong.workorder.service.WorkApplyInfoService;
import com.botong.workorder.vo.WorkApplyInfoVO;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 工单申请信息服务实现类
 * 提供工单申请信息相关的业务操作实现，包括分页查询、保存、更新、删除等功能
 *
 * <AUTHOR>
 * @since 2023/8/16
 */
@Service
@AllArgsConstructor
public class WorkApplyInfoServiceImpl extends BaseServiceImpl<WorkApplyInfoDao, WorkApplyInfoEntity> implements WorkApplyInfoService {

    /**
     * 分页查询工单申请信息
     * 根据查询条件获取工单申请信息列表，支持分页
     * 
     * @param query 查询条件参数
     * @return 分页查询结果，包含工单申请信息列表数据
     */
    @Override
    public PageResult<WorkApplyInfoVO> page(WorkApplyInfoQuery query) {
        IPage<WorkApplyInfoEntity> page = baseMapper.selectPage(getPage(query), getWrapper(query));

        return new PageResult<>(WorkApplyInfoConvert.INSTANCE.convertList(page.getRecords()), page.getTotal());
    }

    /**
     * 构建查询条件包装器
     * 根据查询条件构建MyBatis-Plus的查询包装器
     * 
     * @param query 查询条件参数
     * @return 查询条件包装器
     */
    private LambdaQueryWrapper<WorkApplyInfoEntity> getWrapper(WorkApplyInfoQuery query) {
        LambdaQueryWrapper<WorkApplyInfoEntity> wrapper = Wrappers.lambdaQuery();
        return wrapper;
    }

    /**
     * 保存工单申请信息
     * 将VO对象转换为实体对象并保存到数据库
     * 
     * @param vo 工单申请信息视图对象
     */
    @Override
    public void save(WorkApplyInfoVO vo) {
        WorkApplyInfoEntity entity = WorkApplyInfoConvert.INSTANCE.convert(vo);

        baseMapper.insert(entity);
    }

    /**
     * 根据工单ID更新申请信息
     * 根据工单ID作为条件更新对应的申请信息
     * 
     * @param entity 工单申请信息实体，包含需要更新的信息
     */
    @Override
    public void updateByWorkId(WorkApplyInfoEntity entity) {
        lambdaUpdate().eq(WorkApplyInfoEntity::getWorkId, entity.getWorkId()).update(entity);
    }

    /**
     * 批量删除工单申请信息
     * 根据申请信息ID列表批量删除申请信息
     * 
     * @param idList 需要删除的申请信息ID列表
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(List<Long> idList) {
        removeByIds(idList);
    }

    /**
     * 根据工单ID批量删除申请信息
     * 根据工单ID列表批量删除对应的申请信息，使用逻辑删除方式
     * 
     * @param idList 工单ID列表
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByWorkIds(List<Long> idList) {
        LambdaUpdateWrapper<WorkApplyInfoEntity> updateWrapper = new LambdaUpdateWrapper<>();
        for (Long id : idList) {
            updateWrapper.eq(WorkApplyInfoEntity::getWorkId, id);
            updateWrapper.set(WorkApplyInfoEntity::getDeleted, 1);
            update(updateWrapper);
            updateWrapper.clear();
        }
    }

    /**
     * 根据工单ID获取申请信息
     * 根据工单ID查询对应的申请信息
     * 
     * @param id 工单ID
     * @return 工单申请信息视图对象
     */
    @Override
    public WorkApplyInfoVO getByWorkId(Long id) {
        QueryWrapper<WorkApplyInfoEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(WorkApplyInfoEntity::getWorkId, id);
        WorkApplyInfoEntity workApplyInfoEntity = this.baseMapper.selectOne(queryWrapper);
        return WorkApplyInfoConvert.INSTANCE.convert(workApplyInfoEntity);
    }

}