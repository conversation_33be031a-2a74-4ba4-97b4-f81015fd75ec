package com.botong.workorder.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.botong.framework.common.page.PageResult;
import com.botong.framework.mybatis.service.impl.BaseServiceImpl;
import com.botong.workorder.convert.WorkApplyInfoConvert;
import com.botong.workorder.dao.WorkApplyInfoDao;
import com.botong.workorder.entity.WorkApplyInfoEntity;
import com.botong.workorder.query.WorkApplyInfoQuery;
import com.botong.workorder.service.WorkApplyInfoService;
import com.botong.workorder.vo.WorkApplyInfoVO;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR> by zhb on 2023/8/16.
 */
@Service
@AllArgsConstructor
public class WorkApplyInfoServiceImpl extends BaseServiceImpl<WorkApplyInfoDao, WorkApplyInfoEntity> implements WorkApplyInfoService {

    @Override
    public PageResult<WorkApplyInfoVO> page(WorkApplyInfoQuery query) {
        IPage<WorkApplyInfoEntity> page = baseMapper.selectPage(getPage(query), getWrapper(query));

        return new PageResult<>(WorkApplyInfoConvert.INSTANCE.convertList(page.getRecords()), page.getTotal());
    }

    private LambdaQueryWrapper<WorkApplyInfoEntity> getWrapper(WorkApplyInfoQuery query) {
        LambdaQueryWrapper<WorkApplyInfoEntity> wrapper = Wrappers.lambdaQuery();
        return wrapper;
    }

    @Override
    public void save(WorkApplyInfoVO vo) {
        WorkApplyInfoEntity entity = WorkApplyInfoConvert.INSTANCE.convert(vo);

        baseMapper.insert(entity);
    }

    @Override
    public void updateByWorkId(WorkApplyInfoEntity entity) {

        lambdaUpdate().eq(WorkApplyInfoEntity::getWorkId, entity.getWorkId()).update(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(List<Long> idList) {
        removeByIds(idList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByWorkIds(List<Long> idList) {
        LambdaUpdateWrapper<WorkApplyInfoEntity> updateWrapper = new LambdaUpdateWrapper<>();
        for (Long id : idList) {
            updateWrapper.eq(WorkApplyInfoEntity::getWorkId, id);
            updateWrapper.set(WorkApplyInfoEntity::getDeleted, 1);
            update(updateWrapper);
            updateWrapper.clear();
        }
    }

    @Override
    public WorkApplyInfoVO getByWorkId(Long id) {
        QueryWrapper<WorkApplyInfoEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(WorkApplyInfoEntity::getWorkId, id);
        WorkApplyInfoEntity workApplyInfoEntity = this.baseMapper.selectOne(queryWrapper);
        return WorkApplyInfoConvert.INSTANCE.convert(workApplyInfoEntity);

    }

}