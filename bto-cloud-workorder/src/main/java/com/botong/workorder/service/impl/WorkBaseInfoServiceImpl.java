package com.botong.workorder.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.botong.framework.common.page.PageResult;
import com.botong.framework.mybatis.service.impl.BaseServiceImpl;
import com.botong.workorder.convert.WorkBaseInfoConvert;
import com.botong.workorder.dao.WorkBaseInfoDao;
import com.botong.workorder.entity.WorkBaseInfoEntity;
import com.botong.workorder.query.WorkBaseInfoQuery;
import com.botong.workorder.service.WorkBaseInfoService;
import com.botong.workorder.vo.WorkBaseInfoVO;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 工单基础信息服务实现类
 * 提供工单基础信息相关的业务操作实现，包括分页查询、保存、更新、删除等功能
 *
 * <AUTHOR>
 * @since 2023-08-10
 */
@Service
@AllArgsConstructor
public class WorkBaseInfoServiceImpl extends BaseServiceImpl<WorkBaseInfoDao, WorkBaseInfoEntity> implements WorkBaseInfoService {

    private final WorkBaseInfoDao workBaseInfoDao;

    /**
     * 分页查询工单基础信息
     * 根据查询条件获取工单基础信息列表，支持分页
     * 
     * @param query 查询条件参数
     * @return 分页查询结果，包含工单基础信息列表数据
     */
    @Override
    public PageResult<WorkBaseInfoVO> page(WorkBaseInfoQuery query) {
        IPage<WorkBaseInfoEntity> page = baseMapper.selectPage(getPage(query), getWrapper(query));

        return new PageResult<>(WorkBaseInfoConvert.INSTANCE.convertList(page.getRecords()), page.getTotal());
    }

    /**
     * 构建查询条件包装器
     * 根据查询条件构建MyBatis-Plus的查询包装器
     * 
     * @param query 查询条件参数
     * @return 查询条件包装器
     */
    private LambdaQueryWrapper<WorkBaseInfoEntity> getWrapper(WorkBaseInfoQuery query) {
        LambdaQueryWrapper<WorkBaseInfoEntity> wrapper = Wrappers.lambdaQuery();
        if (ObjectUtil.isNotEmpty(query)) {
            wrapper
                    .in(CollUtil.isNotEmpty(query.getAlarmTypeList()), WorkBaseInfoEntity::getAlarmType, query.getAlarmTypeList())
                    .in(CollUtil.isNotEmpty(query.getStatusList()), WorkBaseInfoEntity::getStatus, query.getStatusList())
            ;
        }
        return wrapper;
    }

    /**
     * 保存工单基础信息
     * 将VO对象转换为实体对象，设置默认状态为0，并保存到数据库
     * 
     * @param vo 工单基础信息视图对象
     */
    @Override
    public void save(WorkBaseInfoVO vo) {
        WorkBaseInfoEntity entity = WorkBaseInfoConvert.INSTANCE.convert(vo);

        entity.setStatus(0);
        baseMapper.insert(entity);
    }

    /**
     * 更新工单基础信息
     * 将VO对象转换为实体对象并更新到数据库
     * 
     * @param vo 工单基础信息视图对象，包含需要更新的信息
     */
    @Override
    public void update(WorkBaseInfoVO vo) {
        WorkBaseInfoEntity entity = WorkBaseInfoConvert.INSTANCE.convert(vo);

        updateById(entity);
    }

    /**
     * 批量删除工单基础信息
     * 根据工单ID列表批量删除工单基础信息
     * 
     * @param idList 需要删除的工单ID列表
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(List<Long> idList) {
        removeByIds(idList);
    }

    /**
     * 检查工单是否存在
     * 验证工单ID列表中哪些工单不存在于系统中
     * 
     * @param workOrderIdList 工单ID列表
     * @return 不存在的工单ID列表
     */
   @Override
    public List<Long> exist(List<Long> workOrderIdList) {
        // 查询已存在的工单ID
        List<Long> existentList = this.lambdaQuery()
                .in(WorkBaseInfoEntity::getId, workOrderIdList)
                .list().stream()
                .map(WorkBaseInfoEntity::getId)
                .collect(Collectors.toList());
        // 返回不存在于existentList中的id
        return workOrderIdList.stream()
                .filter(id -> !existentList.contains(id))
                .collect(Collectors.toList());
    }

    /**
     * 根据工单ID获取电站用户电话
     * 根据工单ID列表批量获取对应的电站用户电话号码
     * 
     * @param workIdList 工单ID列表
     * @return 电站用户电话列表
     */
    @Override
    public List<String> getPlantUserPhoneByWorkId(List<Long> workIdList) {
        return workBaseInfoDao.getPlantUserPhoneByWorkId(workIdList);
    }

}