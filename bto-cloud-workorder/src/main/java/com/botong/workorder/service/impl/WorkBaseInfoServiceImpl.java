package com.botong.workorder.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.botong.framework.common.page.PageResult;
import com.botong.framework.mybatis.service.impl.BaseServiceImpl;
import com.botong.workorder.convert.WorkBaseInfoConvert;
import com.botong.workorder.dao.WorkBaseInfoDao;
import com.botong.workorder.entity.WorkBaseInfoEntity;
import com.botong.workorder.query.WorkBaseInfoQuery;
import com.botong.workorder.service.WorkBaseInfoService;
import com.botong.workorder.vo.WorkBaseInfoVO;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 工单基础明细表
 *
 * <AUTHOR>
 * @since 2023-08-10
 */
@Service
@AllArgsConstructor
public class WorkBaseInfoServiceImpl extends BaseServiceImpl<WorkBaseInfoDao, WorkBaseInfoEntity> implements WorkBaseInfoService {

    private final WorkBaseInfoDao workBaseInfoDao;

    @Override
    public PageResult<WorkBaseInfoVO> page(WorkBaseInfoQuery query) {
        IPage<WorkBaseInfoEntity> page = baseMapper.selectPage(getPage(query), getWrapper(query));

        return new PageResult<>(WorkBaseInfoConvert.INSTANCE.convertList(page.getRecords()), page.getTotal());
    }

    private LambdaQueryWrapper<WorkBaseInfoEntity> getWrapper(WorkBaseInfoQuery query) {
        LambdaQueryWrapper<WorkBaseInfoEntity> wrapper = Wrappers.lambdaQuery();
        if (ObjectUtil.isNotEmpty(query)) {
            wrapper
                    .in(CollUtil.isNotEmpty(query.getAlarmTypeList()), WorkBaseInfoEntity::getAlarmType, query.getAlarmTypeList())
                    .in(CollUtil.isNotEmpty(query.getStatusList()), WorkBaseInfoEntity::getStatus, query.getStatusList())
            ;
        }
        return wrapper;
    }

    @Override
    public void save(WorkBaseInfoVO vo) {
        WorkBaseInfoEntity entity = WorkBaseInfoConvert.INSTANCE.convert(vo);

        entity.setStatus(0);
        baseMapper.insert(entity);
    }

    @Override
    public void update(WorkBaseInfoVO vo) {
        WorkBaseInfoEntity entity = WorkBaseInfoConvert.INSTANCE.convert(vo);

        updateById(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(List<Long> idList) {
        removeByIds(idList);
    }

   @Override
    public List<Long> exist(List<Long> workOrderIdList) {
        // 是否存在工单id
        List<Long> existentList = this.lambdaQuery()
                .in(WorkBaseInfoEntity::getId, workOrderIdList)
                .list().stream()
                .map(WorkBaseInfoEntity::getId)
                .collect(Collectors.toList());
        // 返回不存在于existentList中的id
        return workOrderIdList.stream()
                .filter(id -> !existentList.contains(id))
                .collect(Collectors.toList());
    }

    @Override
    public List<String> getPlantUserPhoneByWorkId(List<Long> workIdList) {
        return workBaseInfoDao.getPlantUserPhoneByWorkId(workIdList);
    }

}