package com.botong.workorder.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.botong.framework.common.exception.ServerException;
import com.botong.framework.common.page.PageResult;
import com.botong.framework.common.utils.AssertUtils;
import com.botong.framework.mybatis.service.impl.BaseServiceImpl;
import com.botong.workorder.convert.BillWorkExpenseConvert;
import com.botong.workorder.convert.WorkOperationInfoConvert;
import com.botong.workorder.dao.WorkOperationInfoDao;
import com.botong.workorder.dto.PlantMaterialInfoDTO;
import com.botong.workorder.dto.WorkOperationInfoDTO;
import com.botong.workorder.entity.*;
import com.botong.workorder.enums.WorkOrderOvertimeEnum;
import com.botong.workorder.query.WorkOperationInfoQuery;
import com.botong.workorder.service.*;
import com.botong.workorder.vo.OfferPlantMaterialInfoVO;
import com.botong.workorder.vo.WorkChargeVO;
import com.botong.workorder.vo.WorkOperationInfoVO;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import lombok.AllArgsConstructor;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

import static com.botong.workorder.enums.WorkOrderOvertimeEnum.ABOUT_TO_TIMEOUT;
import static com.botong.workorder.enums.WorkOrderStatusEnum.COMPLETED;

/**
 * 工单运维信息服务实现类
 * 提供工单运维信息相关的业务操作实现，包括分页查询、保存、更新、删除等功能
 * 主要负责处理工单运维流程中的各项信息，如运维状态、维修时间、故障原因、材料信息等
 * 同时提供超时计算、批量更新、工单详情获取等高级功能
 *
 * <AUTHOR>
 * @since 1.0.0 2023-08-22
 */

@Service
@Transactional(rollbackFor = Exception.class)
@AllArgsConstructor
public class WorkOperationInfoServiceImpl extends BaseServiceImpl<WorkOperationInfoDao, WorkOperationInfoEntity> implements WorkOperationInfoService {

    @Resource
    private WorkServiceOfferService workServiceOfferService;
    @Resource
    private PlantMaterialInfoService plantMaterialInfoService;
    @Resource
    private WorkBaseInfoService workBaseInfoService;
    @Resource
    private WorkExamineInfoService workExamineInfoService;
    @Resource
    private PlantBaseInfoService plantBaseInfoService;

    private final BillWorkExpenseService billWorkExpenseService;

    private final WorkChargeService workChargeService;


    /**
     * 分页查询工单运维信息
     * 根据查询条件获取工单运维信息列表，支持分页
     * 
     * @param query 查询条件参数
     * @return 分页查询结果，包含工单运维信息列表数据
     */
    @Override
    public PageResult<WorkOperationInfoVO> page(WorkOperationInfoQuery query) {
        IPage<WorkOperationInfoEntity> page = baseMapper.selectPage(getPage(query), getWrapper(query));

        return new PageResult<>(WorkOperationInfoConvert.INSTANCE.convertList(page.getRecords()), page.getTotal());
    }

    /**
     * 构建查询条件包装器
     * 根据查询条件构建MyBatis-Plus的查询包装器
     * 
     * @param query 查询条件参数
     * @return 查询条件包装器
     */
    private LambdaQueryWrapper<WorkOperationInfoEntity> getWrapper(WorkOperationInfoQuery query) {
        LambdaQueryWrapper<WorkOperationInfoEntity> wrapper = Wrappers.lambdaQuery();

        return wrapper;
    }

    /**
     * 保存工单运维信息
     * 将VO对象转换为实体对象并保存到数据库
     * 
     * @param vo 工单运维信息数据对象
     */
    @Override
    public void save(WorkOperationInfoVO vo) {
        WorkOperationInfoEntity entity = WorkOperationInfoConvert.INSTANCE.convert(vo);

        baseMapper.insert(entity);
    }

    /**
     * 更新工单运维信息
     * 根据工单ID作为条件更新对应的运维信息
     * 
     * @param vo 工单运维信息数据对象，包含需要更新的信息
     */
    @Override
    public void update(WorkOperationInfoVO vo) {
        // 校验参数
        AssertUtils.isNull(vo.getWorkId(), "工单id");
        // 创建LambdaQueryWrapper
        LambdaQueryWrapper<WorkOperationInfoEntity> wrapper = new LambdaQueryWrapper<>();
        // 设置查询条件
        wrapper.eq(WorkOperationInfoEntity::getWorkId, vo.getWorkId());
        // 转换WorkOperationInfoVO
        WorkOperationInfoEntity entity = WorkOperationInfoConvert.INSTANCE.convert(vo);
        // 更新实体
        this.update(entity, wrapper);
    }

    /**
     * 批量删除工单运维信息
     * 根据运维信息ID列表批量删除工单运维信息
     * 
     * @param idList 需要删除的运维信息ID列表
     */
    @Override
    public void delete(List<Long> idList) {
        removeByIds(idList);
    }

    /**
     * 获取工单运维详情信息
     * 根据工单ID获取完整的运维详情，包括工单基本信息、电站信息、材料信息、审核信息、费用信息等
     * 
     * @param workId 工单ID
     * @return 完整的工单运维信息视图对象
     */
    @Override
    public WorkOperationInfoVO info(Long workId) {
        // 根据工单ID查询工单信息
        WorkOperationInfoEntity entity = this.lambdaQuery().eq(WorkOperationInfoEntity::getWorkId, workId).one();
        // 根据工单ID查询工单基本信息
        WorkBaseInfoEntity workBaseInfo = workBaseInfoService.getById(workId);
        if (ObjectUtil.isNull(workBaseInfo)) {
            throw new ServerException("尚未创建工单");
        }

        // 将实体类转换为VO
        WorkOperationInfoVO vo = BeanUtil.copyProperties(entity, WorkOperationInfoVO.class);
        vo.setAlarmType(workBaseInfo.getAlarmType());

        String plantId = workBaseInfo.getPlantId();
        PlantBaseInfoEntity plant = plantBaseInfoService.getById(plantId);
        vo.setWarrantyStatus(plant.getWarrantyStatus());

        // 获取工单的原料信息
        List<OfferPlantMaterialInfoVO> plantMaterialInfoList = getOfferPlantMaterialInfo(workId);

        if (CollUtil.isNotEmpty(plantMaterialInfoList)) {
            vo.setPlantMaterialInfoList(plantMaterialInfoList);
        }

        // 获取工单审核信息
        WorkExamineInfoEntity examineInfo = workExamineInfoService.lambdaQuery().eq(WorkExamineInfoEntity::getWorkId, workId).one();
        if (ObjectUtil.isNotNull(examineInfo)) {
            vo.setAuditResult(examineInfo.getAuditResult());
        }
        LambdaQueryWrapper<BillWorkExpenseEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BillWorkExpenseEntity::getWorkId, workId);
        List<BillWorkExpenseEntity> list = billWorkExpenseService.list(queryWrapper);
        if (CollUtil.isNotEmpty(list)) {
            vo.setBillWorkExpenseList(BillWorkExpenseConvert.INSTANCE.convertList(list));
        }

        List<WorkChargeVO> workCharges = workChargeService.listByWorkId(workId);
        if (CollUtil.isNotEmpty(workCharges)) {
            vo.setWorkChargeList(workCharges);
        }
        return vo;
    }

    /**
     * 获取工单材料信息
     * 根据工单ID获取工单相关的材料信息，包括厂家、供应商、材料类型、规格等完整信息
     * 
     * @param workId 工单ID
     * @return 材料信息列表
     */
    @NotNull
    public List<OfferPlantMaterialInfoVO> getOfferPlantMaterialInfo(Long workId) {
        // 报价List
        List<WorkServiceOfferEntity> serviceOfferList = workServiceOfferService.lambdaQuery().
                eq(WorkServiceOfferEntity::getWorkId, workId)
                .list(); // 从数据库或其他地方选出的与工单对应的服务报价数据

        if (serviceOfferList.isEmpty()) {
            return Collections.emptyList();
        }

        // 厂家List
        List<PlantMaterialInfoEntity> manufacturerList = plantMaterialInfoService.lambdaQuery()
                .eq(PlantMaterialInfoEntity::getType, 0L)
                .list();
        // 供应商List
        List<PlantMaterialInfoEntity> supplyList = plantMaterialInfoService.lambdaQuery()
                .eq(PlantMaterialInfoEntity::getType, 1L)
                .list();
        // 材料类型List
        List<PlantMaterialInfoEntity> materialTypeList = plantMaterialInfoService.lambdaQuery()
                .eq(PlantMaterialInfoEntity::getType, 2L)
                .list();
        // 规格List
        List<PlantMaterialInfoEntity> specificationList = plantMaterialInfoService.lambdaQuery()
                .eq(PlantMaterialInfoEntity::getType, 3L)
                .list();

        // 材料表
        List<OfferPlantMaterialInfoVO> plantMaterialInfoList = new ArrayList<>();

        // 遍历服务报价数据
        for (WorkServiceOfferEntity serviceOffer : serviceOfferList) {
            Long supplyId = null;
            Long manufacturerId = null;
            Long materialTypeId = null;

            // 创建一个材料信息对象
            OfferPlantMaterialInfoVO materialInfo = new OfferPlantMaterialInfoVO();

            // 设置材料ID和名称
            materialInfo.setId(serviceOffer.getMaterialId());
            materialInfo.setCount(serviceOffer.getCount());
            materialInfo.setTotalPrice(serviceOffer.getTotalPrice());

            // 遍历电站材料数据，根据ID匹配材料信息
            for (PlantMaterialInfoEntity specification : specificationList) {
                if (specification.getId().equals(serviceOffer.getMaterialId())) {
                    // 设置名称、类型、采购方式、价格和类型
                    materialInfo.setName(specification.getName());
                    materialInfo.setPurchaseWay(specification.getPurchaseWay());
                    materialInfo.setPrice(specification.getPrice());
                    materialInfo.setType(specification.getType());
                    materialTypeId = specification.getPid();

                }
            }

            for (PlantMaterialInfoEntity materialType : materialTypeList) {
                if (materialType.getId().equals(materialTypeId)) {
                    materialInfo.setMaterialTypeId(materialType.getId());
                    materialInfo.setMaterialTypeName(materialType.getName());
                    supplyId = materialType.getPid();
                }
            }

            for (PlantMaterialInfoEntity supply : supplyList) {
                if (supply.getId().equals(supplyId)) {
                    materialInfo.setSupplyId(supply.getId());
                    materialInfo.setSupplyName(supply.getName());
                    manufacturerId = supply.getPid();
                }
            }
            for (PlantMaterialInfoEntity manufacturer : manufacturerList) {
                if (manufacturer.getId().equals(manufacturerId)) {
                    materialInfo.setManufacturerId(manufacturer.getId());
                    materialInfo.setManufacturerName(manufacturer.getName());
                }
            }
            // 将材料信息添加到列表中
            plantMaterialInfoList.add(materialInfo);
        }
        return plantMaterialInfoList;
    }

    /**
     * 批量更新工单运维信息
     * 根据工单ID批量更新运维信息，包括保修状态、材料信息、维修状态等
     * 同时更新相关的电站信息和工单基本信息
     * 
     * @param dto 工单运维信息传输对象，包含需要更新的所有信息
     */
    @Override
    public void updateBatch(WorkOperationInfoDTO dto) {
        Long workId = dto.getWorkId();
        if (ObjectUtil.isNotNull(dto.getWarrantyStatus())) {
            WorkBaseInfoEntity workBaseInfo = workBaseInfoService.getById(workId);
            AssertUtils.isNull(workBaseInfo, "工单");
            String plantId = workBaseInfo.getPlantId();
            plantBaseInfoService.lambdaUpdate()
                    .eq(PlantBaseInfoEntity::getId, plantId)
                    .set(PlantBaseInfoEntity::getWarrantyStatus, dto.getWarrantyStatus())
                    .update();
        }

        // 获取物料信息列表
        List<PlantMaterialInfoDTO> plantMaterialInfoList = dto.getPlantMaterialInfoList();
        // 在保清空报价材料
        if (dto.getWarrantyStatus() == null || dto.getWarrantyStatus() == 0 || dto.getRepairStatus() == 1) {
            plantMaterialInfoList = null;
        }

        if (dto.getRepairStatus() == 1) {
            dto.setOpCause("");
            dto.setRepairPhotoPath("");
        } else {
            dto.setUnrepairedCause("");
        }
        // 如果物料信息列表不为空
        if (CollUtil.isNotEmpty(plantMaterialInfoList)) {
            // 创建一个id和数量映射的Map
            Map<Long, Integer> idCountMap = plantMaterialInfoList.stream()
                    .filter(Objects::nonNull)
                    .filter(item -> item.getId() != null && item.getCount() != null)
                    .collect(Collectors.toMap(
                            PlantMaterialInfoDTO::getId,
                            PlantMaterialInfoDTO::getCount,
                            (existing, replacement) -> replacement // 处理重复key的情况
                    ));

            // 获取物料id和价格映射的Map
            Map<Long, BigDecimal> idPriceMap = getIdPriceMap(dto.getPlantMaterialInfoList());

            // 如果id和数量映射的Map和价格映射的Map都不为空
            if (CollUtil.isNotEmpty(idCountMap) && CollUtil.isNotEmpty(idPriceMap)) {
                // 创建一个工作服务报价实体列表
                List<WorkServiceOfferEntity> list = new ArrayList<>();
                // 获取工作id
                // 遍历id和数量映射的Map
                for (Map.Entry<Long, Integer> entry : idCountMap.entrySet()) {
                    Long id = entry.getKey();
                    Integer count = entry.getValue();
                    // 获取价格
                    BigDecimal price = idPriceMap.get(id);
                    // 如果价格不为空
                    if (price != null) {
                        // 创建一个工作服务报价实体
                        WorkServiceOfferEntity workServiceOffer = new WorkServiceOfferEntity(workId, id, count);
                        // 计算总价
                        BigDecimal totalPrice = price.multiply(BigDecimal.valueOf(count)).setScale(2, RoundingMode.HALF_UP);
                        // 设置总价
                        workServiceOffer.setTotalPrice(totalPrice);
                        // 添加到列表
                        list.add(workServiceOffer);
                    }
                }

                // 如果工作服务报价实体列表不为空
                if (CollUtil.isNotEmpty(list)) {
                    // 更新工作服务报价实体列表
                    workServiceOfferService.lambdaUpdate()
                            .eq(WorkServiceOfferEntity::getWorkId, workId)
                            .remove();
                    workServiceOfferService.saveBatch(list);
                }
            }
        } else {
            // 更新工作服务报价实体列表
            workServiceOfferService.lambdaUpdate()
                    .eq(WorkServiceOfferEntity::getWorkId, dto.getWorkId())
                    .remove();
        }

        // 创建一个工作操作信息实体
        WorkOperationInfoEntity entity = BeanUtil.copyProperties(dto, WorkOperationInfoEntity.class);
        entity.setRepairTime(new Date());
        // 判断是否存在
        boolean exists = this.lambdaQuery()
                .eq(WorkOperationInfoEntity::getWorkId, entity.getWorkId())
                .exists();

        // 写入同行人
        if (entity.getHasCompanion() != null && entity.getHasCompanion() == 1) {
            List<Long> companion = dto.getCompanion();
            entity.setCompanion(JSONUtil.toJsonStr(companion));
        }

        // 如果存在
        if (exists) {
            // 更新
            this.lambdaUpdate()
                    .eq(WorkOperationInfoEntity::getWorkId, entity.getWorkId())
                    .update(entity);
        } else {
            // 保存
            this.save(entity);
        }

        if (workId != null) {
            workBaseInfoService.lambdaUpdate()
                    .eq(WorkBaseInfoEntity::getId, workId)
                    .set(WorkBaseInfoEntity::getAlarmType, dto.getAlarmType()).update();
        }


    }

    /**
     * 根据工单ID批量删除运维信息
     * 根据工单ID列表批量删除对应的运维信息
     * 
     * @param idList 工单ID列表
     */
    @Override
    public void deleteByWorkIds(List<Long> idList) {
        LambdaQueryWrapper<WorkOperationInfoEntity> wrapper = Wrappers.lambdaQuery();
        wrapper.in(WorkOperationInfoEntity::getWorkId, idList);
        baseMapper.delete(wrapper);
    }

    /**
     * 获取材料价格映射
     * 根据材料信息列表获取材料ID与价格的映射关系
     * 
     * @param plantMaterialInfoList 材料信息列表
     * @return 材料ID与价格的映射Map
     */
    private Map<Long, BigDecimal> getIdPriceMap(List<PlantMaterialInfoDTO> plantMaterialInfoList) {
        // 如果plantMaterialInfoList为空，返回空Map
        if (CollUtil.isEmpty(plantMaterialInfoList)) {
            return Collections.emptyMap();
        }

        // 获取plantMaterialInfoList中id的列表
        List<Long> idList = plantMaterialInfoList.stream()
                .filter(Objects::nonNull)
                .map(PlantMaterialInfoDTO::getId)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        // 根据id列表查询plantMaterialInfoEntity
        List<PlantMaterialInfoEntity> plantMaterialInfoEntities = plantMaterialInfoService.lambdaQuery()
                .in(PlantMaterialInfoEntity::getId, idList)
                .list();

        // 如果plantMaterialInfoEntities为空，返回空Map
        if (CollUtil.isEmpty(plantMaterialInfoEntities)) {
            return Collections.emptyMap();
        }

        // 将plantMaterialInfoEntities中的id和price封装成Map
        return plantMaterialInfoEntities.stream()
                .collect(Collectors.toMap(
                        PlantMaterialInfoEntity::getId,
                        PlantMaterialInfoEntity::getPrice
                ));
    }

    /**
     * 更新工单超时状态
     * 根据工单ID列表批量更新工单的超时状态，计算是否即将超时或已超时
     * 
     * @param workIdList 工单ID列表，如果为空则处理所有工单
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateOvertime(List<Long> workIdList) {
        List<WorkOperationInfoEntity> list;
        // 创建一个Map，用来存储工单ID和状态
        Map<Long, Integer> workIdAndStatusMap = new HashMap<>();
        // 判断workIdList是否为空
        if (CollUtil.isNotEmpty(workIdList)) {
            // 查询工单ID在workIdList中的数据
            list = this.lambdaQuery()
                    .in(WorkOperationInfoEntity::getWorkId, workIdList)
                    .list();
            // 如果查询结果为空，抛出异常
            if (CollUtil.isEmpty(list)) {
                throw new ServerException("工单不存在");
            }
        } else {
            // 查询所有工单
            List<WorkBaseInfoEntity> workList = workBaseInfoService.lambdaQuery().list();
            // 将查询结果转换为Map
            workIdAndStatusMap = workList.stream().collect(Collectors.toMap(WorkBaseInfoEntity::getId, WorkBaseInfoEntity::getStatus));
            // 获取Map中的key集合
            Set<Long> workIdSet = workIdAndStatusMap.keySet();
            // 如果key集合为空，返回
            if (CollUtil.isEmpty(workIdSet)) {
                return;
            }
            // 查询工单ID在key集合中的数据
            list = this.lambdaQuery()
                    .in(WorkOperationInfoEntity::getWorkId, workIdSet)
                    .list();

            // 如果查询结果为空，返回
            if (CollUtil.isEmpty(list)) {
                return;
            }
        }
        // 将Map赋值给finalMap
        Map<Long, Integer> finalMap = workIdAndStatusMap;
        // 遍历查询结果
        list.stream()
                .filter(Objects::nonNull)
                .forEach(entity -> {
                    // 计算超时时间
                    Date repairTime = Optional.ofNullable(entity.getRepairTime()).orElse(Date.from(LocalDate.now().atStartOfDay().atZone(ZoneId.systemDefault()).toInstant()));
                    // 计算超时时间
                    calculateOvertime(entity.getPreRepairTime(), entity, repairTime, finalMap);
                });

        // 更新查询结果
        this.updateBatchById(list);

    }

    /**
     * 计算工单超时时间
     * 根据预计维修时间和实际维修时间计算工单是否超时及超时天数
     * 
     * @param preRepairTime 预计维修时间
     * @param entity 工单运维实体
     * @param repaireTime 实际维修时间
     * @param workIdAndStatusMap 工单状态映射
     */
    private void calculateOvertime(Date preRepairTime, WorkOperationInfoEntity entity, Date repaireTime, Map<Long, Integer> workIdAndStatusMap) {
        // 计算超时时间
        Optional.ofNullable(preRepairTime)
                .map(time -> time.toInstant().atZone(ZoneId.systemDefault()).toLocalDate())
                .ifPresent(preRepairDate -> {
                    LocalDate date = repaireTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
                    LocalDate plusDays = preRepairDate.plusDays(1);
                    LocalDate minusDays = preRepairDate.minusDays(1);
                    Integer workStatus = workIdAndStatusMap.getOrDefault(entity.getWorkId(), 0);
                    // 判断当前时间是否在超时之前，且超时之前是否完成
                    if (date.isBefore(plusDays) && isAfterOrEqual(date, minusDays) && !COMPLETED.getValue().equals(workStatus)) {
                        entity.setOvertime(ABOUT_TO_TIMEOUT.getName());
                        entity.setOvertimed(ABOUT_TO_TIMEOUT.getValue());
                    } else if (isAfterOrEqual(date, plusDays) && !COMPLETED.getValue().equals(workStatus)) {
                        // 计算超时时间
                        long dayDiff = ChronoUnit.DAYS.between(plusDays, date);
                        entity.setOvertime(++dayDiff + "天");
                        entity.setOvertimed(WorkOrderOvertimeEnum.TIMEOUT.getValue());
                    } else if (!WorkOrderOvertimeEnum.TIMEOUT.getValue().equals(entity.getOvertimed())) {
                        entity.setOvertime("-");
                        entity.setOvertimed(WorkOrderOvertimeEnum.NORMAL.getValue());
                    }
                });
    }

    /**
     * 比较两个日期的大小
     * 判断第一个日期是否在第二个日期之后或相等
     * 
     * @param date1 第一个日期
     * @param date2 第二个日期
     * @return 如果date1在date2之后或相等则返回true，否则返回false
     */
    private boolean isAfterOrEqual(LocalDate date1, LocalDate date2) {
        // 比较date1和date2的大小，返回true表示date1大于等于date2
        return date1.isAfter(date2) || date1.isEqual(date2);
    }

    /**
     * 批量更新运维信息列表
     * 批量更新多个工单的运维信息
     * 
     * @param list 工单运维信息视图对象列表
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateList(Collection<WorkOperationInfoVO> list) {
        // 遍历list，对每一个元素调用update方法
        list.forEach(this::update);
    }

    /**
     * 获取工单与维修人员的映射关系
     * 根据工单ID列表获取每个工单对应的维修人员ID列表
     * 
     * @param workIds 工单ID列表
     * @return 工单ID与维修人员ID列表的映射Map
     */
    @Override
    public Map<Long, List<Long>> getRepairMapByWorkIds(List<Long> workIds) {
        if (CollUtil.isEmpty(workIds)) {
            return Collections.emptyMap();
        }

        List<WorkOperationInfoEntity> list = this.list(
                new LambdaQueryWrapper<WorkOperationInfoEntity>()
                        .in(WorkOperationInfoEntity::getWorkId, workIds)
                        .eq(WorkOperationInfoEntity::getDeleted, 0)
        );

        return list.stream()
                .filter(e -> e.getWorkId() != null)            // 过滤 workId 为空的记录
                .map(e -> new AbstractMap.SimpleEntry<>(e.getWorkId(), extractPersonIds(e)))
                .filter(entry -> CollUtil.isNotEmpty(entry.getValue()))   // 只保留有人员 ID 的
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        Map.Entry::getValue,
                        (l1, l2) -> {                           // 同一 workId 合并并去重
                            l1.addAll(l2);
                            return l1.stream().distinct().collect(Collectors.toList());
                        },
                        LinkedHashMap::new                      // 保持插入顺序，可选
                ));
    }

    /**
     * 根据用户名模糊查询工单ID
     * 根据维修人员用户名模糊匹配，获取包含该人员的工单ID列表
     * 
     * @param repairUser 维修人员用户名（模糊匹配）
     * @return 匹配的工单ID列表
     */
    @Override
    public List<Long> selectWorkIdsByUsernameLike(String repairUser) {
        MPJLambdaWrapper<WorkOperationInfoEntity> wrapper =
                new MPJLambdaWrapper<WorkOperationInfoEntity>()
                        .select(WorkOperationInfoEntity::getWorkId)
                        .innerJoin(SysUserEntity.class, "u",
                                on -> on.apply(
                                        "FIND_IN_SET(u.id," +
                                                " REPLACE(REPLACE(t.companion,'[',''),']','')) > 0"
                                )
                        )
                        .like(SysUserEntity::getUsername, repairUser)
                        .eq(SysUserEntity::getDeleted, 0)
                        .eq(WorkOperationInfoEntity::getDeleted, 0);
        return baseMapper.selectJoinList(Long.class, wrapper);
    }

    /**
     * 提取同行人员ID列表
     * 从运维信息中提取同行人员的ID列表，处理JSON格式的同行人数据
     * 
     * @param entity 工单运维实体
     * @return 同行人员ID列表
     */
    private List<Long> extractPersonIds(WorkOperationInfoEntity entity) {
        String companion = entity.getCompanion();
        if (StrUtil.isBlank(companion) || "[]".equals(companion.trim())) {
            return Collections.emptyList();
        }
        return Arrays.stream(StrUtil.removeAll(companion, '[', ']')
                        .split(","))
                .map(String::trim)
                .filter(StrUtil::isNotBlank)
                .map(Long::valueOf)
                .collect(Collectors.toList());
    }

}