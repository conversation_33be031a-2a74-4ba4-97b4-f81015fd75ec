package com.botong.workorder.service;

import com.botong.framework.common.page.PageResult;
import com.botong.framework.mybatis.service.BaseService;
import com.botong.workorder.entity.WorkToolInventoryEntity;
import com.botong.workorder.query.WorkToolInventoryQuery;
import com.botong.workorder.vo.WorkToolInventoryVO;

import java.util.List;

/**
 * 运维工具库
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-03-11
 */
public interface WorkToolInventoryService extends BaseService<WorkToolInventoryEntity> {

    PageResult<WorkToolInventoryVO> page(WorkToolInventoryQuery query);

    void save(WorkToolInventoryVO vo);

    void update(WorkToolInventoryVO vo);

    void delete(List<Long> idList);

    void export(WorkToolInventoryQuery query);
}