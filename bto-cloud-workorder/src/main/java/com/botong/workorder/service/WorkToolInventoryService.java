package com.botong.workorder.service;

import com.botong.framework.common.page.PageResult;
import com.botong.framework.mybatis.service.BaseService;
import com.botong.workorder.entity.WorkToolInventoryEntity;
import com.botong.workorder.query.WorkToolInventoryQuery;
import com.botong.workorder.vo.WorkToolInventoryVO;

import java.util.List;

/**
 * 运维工具库服务接口
 * 提供运维工具库相关的业务操作，包括分页查询、保存、更新、删除、导出等功能
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-03-11
 */
public interface WorkToolInventoryService extends BaseService<WorkToolInventoryEntity> {

    /**
     * 分页查询运维工具信息
     * 根据查询条件获取运维工具信息列表，支持按工具名称、保管人姓名等条件进行模糊查询
     * 通过关联查询获取保管人和审核人的用户名信息
     *
     * @param query 查询条件参数，包含分页信息和查询条件
     * @return 分页查询结果，包含运维工具信息列表和总记录数
     */
    PageResult<WorkToolInventoryVO> page(WorkToolInventoryQuery query);

    /**
     * 保存运维工具信息
     * 将VO对象转换为实体对象并保存到数据库
     *
     * @param vo 运维工具信息数据对象
     */
    void save(WorkToolInventoryVO vo);

    /**
     * 更新运维工具信息
     * 将VO对象转换为实体对象并更新到数据库
     *
     * @param vo 运维工具信息数据对象，包含需要更新的信息
     */
    void update(WorkToolInventoryVO vo);

    /**
     * 批量删除运维工具信息
     * 根据工具信息ID列表批量删除运维工具信息
     *
     * @param idList 需要删除的工具信息ID列表
     */
    void delete(List<Long> idList);

    /**
     * 导出运维工具信息
     * 根据查询条件导出运维工具信息到Excel文件
     * 支持自定义查询条件，导出所有符合条件的工具信息
     *
     * @param query 查询条件参数，用于筛选需要导出的数据
     */
    void export(WorkToolInventoryQuery query);
}