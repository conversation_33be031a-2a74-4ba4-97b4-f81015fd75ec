package com.botong.workorder.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.botong.framework.common.page.PageResult;
import com.botong.framework.mybatis.service.impl.BaseServiceImpl;
import com.botong.workorder.convert.BillWorkExpenseConvert;
import com.botong.workorder.dao.BillWorkExpenseDao;
import com.botong.workorder.entity.BillWorkExpenseEntity;
import com.botong.workorder.query.BillWorkExpenseQuery;
import com.botong.workorder.service.BillWorkExpenseService;
import com.botong.workorder.vo.BillWorkExpenseVO;
import com.botong.workorder.vo.PageWorkExpenseVO;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;

/**
 * 工单费用账单服务实现类
 * 实现工单费用账单相关的业务操作，包括分页查询、保存、更新、删除等功能
 *
 * <AUTHOR>
 * @since 1.0.0 2025-01-14
 */
@Service
@AllArgsConstructor
public class BillWorkExpenseServiceImpl extends BaseServiceImpl<BillWorkExpenseDao, BillWorkExpenseEntity> implements BillWorkExpenseService {

    private final BillWorkExpenseDao billWorkExpenseDao;

    /**
     * 分页查询工单费用账单列表
     * 
     * @param query 查询条件参数
     * @return 分页查询结果，包含工单费用账单列表数据
     */
    @Override
    public PageResult<PageWorkExpenseVO> page(BillWorkExpenseQuery query) {
        Page<PageWorkExpenseVO> page = new Page<>(query.getPage(), query.getLimit());
        List<PageWorkExpenseVO> list = billWorkExpenseDao.page(query, page);
        return new PageResult<>(list, page.getTotal());
    }

    /**
     * 构建查询条件包装器
     * 
     * @param query 查询条件参数
     * @return 查询条件包装器
     */
    private LambdaQueryWrapper<BillWorkExpenseEntity> getWrapper(BillWorkExpenseQuery query) {
        LambdaQueryWrapper<BillWorkExpenseEntity> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(Objects.nonNull(query.getWorkId()), BillWorkExpenseEntity::getWorkId, query.getWorkId());
        return wrapper;
    }

    /**
     * 保存工单费用账单信息
     * 
     * @param vo 工单费用账单数据对象
     */
    @Override
    public void save(BillWorkExpenseVO vo) {
        BillWorkExpenseEntity entity = BillWorkExpenseConvert.INSTANCE.convert(vo);

        baseMapper.insert(entity);
    }

    /**
     * 更新工单费用账单信息
     * 
     * @param vo 工单费用账单数据对象，包含需要更新的信息
     */
    @Override
    public void update(BillWorkExpenseVO vo) {
        BillWorkExpenseEntity entity = BillWorkExpenseConvert.INSTANCE.convert(vo);

        updateById(entity);
    }

    /**
     * 批量删除工单费用账单
     * 
     * @param idList 需要删除的账单ID列表
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(List<Long> idList) {
        removeByIds(idList);
    }

    /**
     * 获取工单费用账单列表
     * 
     * @param query 查询条件参数
     * @return 分页查询结果，包含账单详细信息列表
     */
    @Override
    public PageResult<BillWorkExpenseVO> getList(BillWorkExpenseQuery query) {
        IPage<BillWorkExpenseEntity> page = billWorkExpenseDao.selectPage(getPage(query), getWrapper(query));
        if (Objects.nonNull(page)) {
            return new PageResult<>(BillWorkExpenseConvert.INSTANCE.convertList(page.getRecords()), page.getTotal());
        }
        return null;
    }

}