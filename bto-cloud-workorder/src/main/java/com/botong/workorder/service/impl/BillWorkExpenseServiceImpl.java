package com.botong.workorder.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.botong.framework.common.page.PageResult;
import com.botong.framework.mybatis.service.impl.BaseServiceImpl;
import com.botong.workorder.convert.BillWorkExpenseConvert;
import com.botong.workorder.dao.BillWorkExpenseDao;
import com.botong.workorder.entity.BillWorkExpenseEntity;
import com.botong.workorder.query.BillWorkExpenseQuery;
import com.botong.workorder.service.BillWorkExpenseService;
import com.botong.workorder.vo.BillWorkExpenseVO;
import com.botong.workorder.vo.PageWorkExpenseVO;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;

/**
 * 工单费用账单
 *
 * <AUTHOR>
 * @since 1.0.0 2025-01-14
 */
@Service
@AllArgsConstructor
public class BillWorkExpenseServiceImpl extends BaseServiceImpl<BillWorkExpenseDao, BillWorkExpenseEntity> implements BillWorkExpenseService {

    private final BillWorkExpenseDao billWorkExpenseDao;

    @Override
    public PageResult<PageWorkExpenseVO> page(BillWorkExpenseQuery query) {
        Page<PageWorkExpenseVO> page = new Page<>(query.getPage(), query.getLimit());
        List<PageWorkExpenseVO> list = billWorkExpenseDao.page(query, page);
        return new PageResult<>(list, page.getTotal());
    }

    private LambdaQueryWrapper<BillWorkExpenseEntity> getWrapper(BillWorkExpenseQuery query) {
        LambdaQueryWrapper<BillWorkExpenseEntity> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(Objects.nonNull(query.getWorkId()), BillWorkExpenseEntity::getWorkId, query.getWorkId());
        return wrapper;
    }

    @Override
    public void save(BillWorkExpenseVO vo) {
        BillWorkExpenseEntity entity = BillWorkExpenseConvert.INSTANCE.convert(vo);

        baseMapper.insert(entity);
    }

    @Override
    public void update(BillWorkExpenseVO vo) {
        BillWorkExpenseEntity entity = BillWorkExpenseConvert.INSTANCE.convert(vo);

        updateById(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(List<Long> idList) {
        removeByIds(idList);
    }

    @Override
    public PageResult<BillWorkExpenseVO> getList(BillWorkExpenseQuery query) {
        IPage<BillWorkExpenseEntity> page = billWorkExpenseDao.selectPage(getPage(query), getWrapper(query));
        if (Objects.nonNull(page)) {
            return new PageResult<>(BillWorkExpenseConvert.INSTANCE.convertList(page.getRecords()), page.getTotal());
        }
        return null;
    }

}