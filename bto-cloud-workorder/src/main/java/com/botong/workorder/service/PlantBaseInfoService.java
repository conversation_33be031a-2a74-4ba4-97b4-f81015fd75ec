package com.botong.workorder.service;


import com.botong.api.module.photovoltaic.vo.PlantAlarmVO;
import com.botong.api.module.photovoltaic.vo.ProjectTypeTreeVO;
import com.botong.api.module.workorder.entity.OrgBaseEntity;
import com.botong.framework.common.page.PageResult;
import com.botong.framework.mybatis.service.BaseService;
import com.botong.workorder.entity.PlantBaseInfoEntity;
import com.botong.workorder.query.LowPowerQuery;
import com.botong.workorder.query.PlantAlarmQuery;
import com.botong.workorder.query.PlantBaseInfoQuery;
import com.botong.workorder.vo.LowPowerPlantVO;
import com.botong.workorder.vo.PlantBaseInfoVO;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> by zhb on 2023/8/16.
 */
public interface PlantBaseInfoService extends BaseService<PlantBaseInfoEntity> {

    /**
     * 分页查询电站信息
     *
     * @param query 查询条件
     * @return 分页数据
     */
    PageResult<PlantBaseInfoVO> page(PlantBaseInfoQuery query);

    /**
     * 保存电站
     *
     * @param vo 电站基础信息实体
     */
    PlantBaseInfoEntity save(PlantBaseInfoVO vo);

    /**
     * 更新电站
     *
     * @param vo 需要更新的电站基础信息实体
     */
    void update(PlantBaseInfoVO vo);

    /**
     * 删除电站
     *
     * @param idList 需要删除的电站ids
     */
    void delete(List<Long> idList);

    /**
     * 获取所有的电站id集合
     *
     * @return ids
     */
    Map<String, List<Map<String, Object>>> getPlantIdsAndNames(@Valid Integer platform);

    /**
     * 根据电站id或者名称获取该信息
     *
     * @param param 电站名称或者id
     * @return 电站实体
     */
    PlantBaseInfoVO getInfoByIdOrName(String param, Integer platform);

    /**
     * 根据名称或者id模糊查询电站信息
     *
     * @param query 查询条件
     * @return 电站实体集合
     */
    PageResult<PlantBaseInfoVO> getInfoLikeIdOrName(@Valid PlantBaseInfoQuery query);

    /**
     * 实时获取光伏系统未处理电站告警数据
     *
     * @return 告警数据列表
     */
    PageResult<PlantAlarmVO> requestAlarmList(PlantAlarmQuery query);

    /**
     * 获取光伏项目类型数据
     *
     * @return 项目类型树形结构数据
     */
    List<ProjectTypeTreeVO> getProjectTypeTree();

    /**
     * 保存或更新电站信息
     *
     * @param entity 电站信息
     * @return 是否保存成功
     */
    boolean saveOrUpdate(PlantBaseInfoEntity entity);

    PlantBaseInfoEntity getByInfo(Integer value, String plantId, String plantName);

    Map<String, Object> getPlantNumInfo(List<Long> scopeList);

    List<OrgBaseEntity> getAllProject();

    void updateProjectByName(String redisName, String name);


    List<String> transOrgId(List<Long> orgIds);

    HashMap<String, Object> getPlantDiagram(String plantId, Integer plantType);

    PageResult<LowPowerPlantVO> getLowPowerPlant(LowPowerQuery query);

    void exportLowPowerPlant(LowPowerQuery query);
}