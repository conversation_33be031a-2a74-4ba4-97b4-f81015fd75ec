package com.botong.workorder.service;

import com.botong.api.module.photovoltaic.vo.PlantAlarmVO;
import com.botong.api.module.photovoltaic.vo.ProjectTypeTreeVO;
import com.botong.api.module.workorder.entity.OrgBaseEntity;
import com.botong.framework.common.page.PageResult;
import com.botong.framework.mybatis.service.BaseService;
import com.botong.workorder.entity.PlantBaseInfoEntity;
import com.botong.workorder.query.LowPowerQuery;
import com.botong.workorder.query.PlantAlarmQuery;
import com.botong.workorder.query.PlantBaseInfoQuery;
import com.botong.workorder.vo.LowPowerPlantVO;
import com.botong.workorder.vo.PlantBaseInfoVO;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 电站基础信息服务接口
 * 提供电站基础信息相关的业务操作，包括分页查询、保存、更新、删除等功能
 * 
 * <AUTHOR>
 * @since 2023-08-16
 */
public interface PlantBaseInfoService extends BaseService<PlantBaseInfoEntity> {

    /**
     * 分页查询电站信息
     *
     * @param query 查询条件
     * @return 分页数据
     */
    PageResult<PlantBaseInfoVO> page(PlantBaseInfoQuery query);

    /**
     * 保存电站
     *
     * @param vo 电站基础信息实体
     */
    PlantBaseInfoEntity save(PlantBaseInfoVO vo);

    /**
     * 更新电站
     *
     * @param vo 需要更新的电站基础信息实体
     */
    void update(PlantBaseInfoVO vo);

    /**
     * 删除电站
     *
     * @param idList 需要删除的电站ids
     */
    void delete(List<Long> idList);

    /**
     * 获取所有的电站id集合
     *
     * @return ids
     */
    Map<String, List<Map<String, Object>>> getPlantIdsAndNames(@Valid Integer platform);

    /**
     * 根据电站id或者名称获取该信息
     *
     * @param param 电站名称或者id
     * @return 电站实体
     */
    PlantBaseInfoVO getInfoByIdOrName(String param, Integer platform);

    /**
     * 根据名称或者id模糊查询电站信息
     *
     * @param query 查询条件
     * @return 电站实体集合
     */
    PageResult<PlantBaseInfoVO> getInfoLikeIdOrName(@Valid PlantBaseInfoQuery query);

    /**
     * 实时获取光伏系统未处理电站告警数据
     *
     * @return 告警数据列表
     */
    PageResult<PlantAlarmVO> requestAlarmList(PlantAlarmQuery query);

    /**
     * 获取光伏项目类型数据
     *
     * @return 项目类型树形结构数据
     */
    List<ProjectTypeTreeVO> getProjectTypeTree();

    /**
     * 保存或更新电站信息
     *
     * @param entity 电站信息
     * @return 是否保存成功
     */
    boolean saveOrUpdate(PlantBaseInfoEntity entity);

    /**
     * 根据条件获取电站信息
     * 
     * @param value 电站类型值
     * @param plantId 电站ID
     * @param plantName 电站名称
     * @return 电站基础信息实体
     */
    PlantBaseInfoEntity getByInfo(Integer value, String plantId, String plantName);

    /**
     * 获取电站数量统计信息
     * 
     * @param scopeList 范围ID列表
     * @return 电站数量统计结果
     */
    Map<String, Object> getPlantNumInfo(List<Long> scopeList);

    /**
     * 获取所有项目信息
     * 
     * @return 项目基础信息列表
     */
    List<OrgBaseEntity> getAllProject();

    /**
     * 根据名称更新项目信息
     * 
     * @param redisName Redis中的名称
     * @param name 实际名称
     */
    void updateProjectByName(String redisName, String name);

    /**
     * 转换组织ID列表
     * 
     * @param orgIds 组织ID列表
     * @return 转换后的字符串列表
     */
    List<String> transOrgId(List<Long> orgIds);

    /**
     * 获取电站图表数据
     * 
     * @param plantId 电站ID
     * @param plantType 电站类型
     * @return 电站图表数据
     */
    HashMap<String, Object> getPlantDiagram(String plantId, Integer plantType);

    /**
     * 分页查询低功率电站
     * 
     * @param query 查询条件参数
     * @return 分页查询结果，包含低功率电站列表数据
     */
    PageResult<LowPowerPlantVO> getLowPowerPlant(LowPowerQuery query);

    /**
     * 导出低功率电站数据
     * 
     * @param query 查询条件参数
     */
    void exportLowPowerPlant(LowPowerQuery query);
}