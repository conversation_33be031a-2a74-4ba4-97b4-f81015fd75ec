package com.botong.workorder.service;

import com.botong.framework.common.page.PageResult;
import com.botong.workorder.query.PlantElectricChargeAnalyzeQuery;
import com.botong.workorder.query.UserAlarmTypeStatisticsQuery;
import com.botong.workorder.query.WorkOrderStatisticQuery;
import com.botong.workorder.vo.*;

import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 工单统计服务接口
 * 提供工单统计相关的业务操作，包括工单统计、区域树、导出等功能
 *
 * <AUTHOR>
 * @since 2023-08-18
 */
public interface WorkOrderStatisticService {
    /**
     * 工单统计
     *
     * @param query 查询
     * @return {@link List }<{@link StatisticsVO }>
     * <AUTHOR>
     * @since 2023-08-14 11:03:01
     */
    List<StatisticsVO> statistics(WorkOrderStatisticQuery query);


    /**
     * 区域树
     *
     * @param query 查询
     * @return {@link List }<{@link RegionTree }>
     * <AUTHOR>
     * @since 2023-09-12 10:38:17
     */
    List<RegionTree> regionTree(WorkOrderStatisticQuery query);

    /**
     * 工单统计
     *
     * @param query 查询
     * @return {@link List }<{@link RegionCount }>
     * <AUTHOR>
     * @since 2023-08-14 11:03:01
     */
    List<RegionCount> regionCount(WorkOrderStatisticQuery query);

    /**
     * 统计工单数量信息
     * 根据数据权限范围统计工单数量，包括总数量、本月数量、本年数量等
     *
     * @param scopeList 数据权限范围列表
     * @return 工单数量统计结果Map
     */
    Map<String, Object> count(List<Long> scopeList);

    /**
     * 统计电站和工单信息
     * 异步统计电站数量和工单数量信息，包括工单统计、电站统计和完成率统计
     * 使用CompletableFuture实现并发处理，提高统计效率
     *
     * @param plantType 电站类型列表
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 包含电站和工单统计信息的Map
     */
    Map<String, Object> statisticPlantAndWorkOrder(List<Long> plantType, String startTime, String endTime);

    /**
     * 按告警时间统计工单信息
     * 根据年份和电站ID统计告警时间的工单数据
     *
     * @param year 统计年份
     * @param plantId 电站ID，可为空
     * @return 按告警时间统计的工单信息列表
     */
    List<StatisticsByAlarmTimeVO> statisticsByAlarmTime(String year, String plantId);

    /**
     * 获取月度运维工单汇总统计
     * 根据指定日期获取该月度的运维工单汇总数据
     *
     * @param date 指定日期
     * @return 月度运维工单汇总统计列表
     */
    List<Map<String, Object>> monthlyOpsWorkOrderSummary(Date date);

    /**
     * 导出月度运维工单汇总报表
     * 根据指定日期导出该月度的运维工单汇总数据到Excel文件
     *
     * @param date 指定日期
     * @param response HTTP响应对象，用于输出Excel文件
     */
    void exportMonthlyOpsWorkOrderSummary(Date date, HttpServletResponse response);

    /**
     * 获取用户报警类型统计信息
     * 根据查询条件获取用户报警类型统计数据，调用工单流程管理服务实现
     *
     * @param query 用户报警类型统计查询条件
     * @return 用户报警类型统计结果列表
     */
    List<Map<String, Object>> getUserAlarmTypeStatistics(UserAlarmTypeStatisticsQuery query);

    /**
     * 导出用户报警类型统计报表
     * 根据查询条件导出用户报警类型统计数据到Excel文件
     *
     * @param query 用户报警类型统计查询条件
     * @param response HTTP响应对象，用于输出Excel文件
     */
    void exportUserAlarmTypeStatistics(UserAlarmTypeStatisticsQuery query, HttpServletResponse response);

    /**
     * 获取电费分析数据
     * 根据年月获取电费分析数据，如果未指定年月则默认获取前两月的数据
     *
     * @param yearMonth 年月字符串，格式为yyyy-MM
     * @return 电费分析数据列表
     */
    List<ElectricChargeAnalyzeVO> getElectricChargeAnalyze(String yearMonth);

    /**
     * 分页查询电站电费分析数据
     * 根据查询条件分页获取电站电费分析信息
     *
     * @param query 电站电费分析查询条件
     * @return 分页查询结果，包含电站电费分析数据列表
     */
    PageResult<ElectricChargeAnalyzeVO> getPlantElectricChargeAnalyze(PlantElectricChargeAnalyzeQuery query);
}
