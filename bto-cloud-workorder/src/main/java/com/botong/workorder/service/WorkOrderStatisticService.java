package com.botong.workorder.service;

import com.botong.framework.common.page.PageResult;
import com.botong.workorder.query.PlantElectricChargeAnalyzeQuery;
import com.botong.workorder.query.UserAlarmTypeStatisticsQuery;
import com.botong.workorder.query.WorkOrderStatisticQuery;
import com.botong.workorder.vo.*;

import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2023/8/18 17:27
 */
public interface WorkOrderStatisticService {
    /**
     * 工单统计
     *
     * @param query 查询
     * @return {@link List }<{@link StatisticsVO }>
     * <AUTHOR>
     * @since 2023-08-14 11:03:01
     */
    List<StatisticsVO> statistics(WorkOrderStatisticQuery query);


    /**
     * 区域树
     *
     * @param query 查询
     * @return {@link List }<{@link RegionTree }>
     * <AUTHOR>
     * @since 2023-09-12 10:38:17
     */
    List<RegionTree> regionTree(WorkOrderStatisticQuery query);

    /**
     * 工单统计
     *
     * @param query 查询
     * @return {@link List }<{@link RegionCount }>
     * <AUTHOR>
     * @since 2023-08-14 11:03:01
     */
    List<RegionCount> regionCount(WorkOrderStatisticQuery query);

    Map<String, Object> count(List<Long> scopeList);

    Map<String, Object> statisticPlantAndWorkOrder(List<Long> plantType, String startTime, String endTime);

    List<StatisticsByAlarmTimeVO> statisticsByAlarmTime(String year, String plantId);

    List<Map<String, Object>> monthlyOpsWorkOrderSummary(Date date);

    void exportMonthlyOpsWorkOrderSummary(Date date, HttpServletResponse response);


    List<Map<String, Object>> getUserAlarmTypeStatistics(UserAlarmTypeStatisticsQuery query);

    void exportUserAlarmTypeStatistics(UserAlarmTypeStatisticsQuery query, HttpServletResponse response);

    List<ElectricChargeAnalyzeVO> getElectricChargeAnalyze(String yearMonth);

    PageResult<ElectricChargeAnalyzeVO> getPlantElectricChargeAnalyze(PlantElectricChargeAnalyzeQuery query);
}
