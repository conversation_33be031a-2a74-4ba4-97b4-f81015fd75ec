package com.botong.workorder.service;

import com.botong.framework.common.page.PageResult;
import com.botong.framework.mybatis.service.BaseService;
import com.botong.workorder.entity.WorkApplyInfoEntity;
import com.botong.workorder.query.WorkApplyInfoQuery;
import com.botong.workorder.vo.WorkApplyInfoVO;

import java.util.List;

/**
 * 工单申请信息服务接口
 * 提供工单申请信息相关的业务操作，包括分页查询、保存、更新、删除等功能
 * 
 * <AUTHOR>
 * @since 2023-08-16
 */
public interface WorkApplyInfoService extends BaseService<WorkApplyInfoEntity> {

    /**
     * 分页查询工单申请信息
     *
     * @param query 查询条件
     * @return 分页数据
     */
    PageResult<WorkApplyInfoVO> page(WorkApplyInfoQuery query);

    /**
     * 保存申请单
     *
     * @param vo 工单申请表实体
     */
    void save(WorkApplyInfoVO vo);

    /**
     * 更新申请单
     *
     * @param vo 需要更新的工单申请实体
     */
    void updateByWorkId(WorkApplyInfoEntity vo);

    /**
     * 删除工单申请单
     *
     * @param idList 需要删除的申请单ids
     */
    void delete(List<Long> idList);

    /**
     * 根据工单ID删除申请单
     *
     * @param idList 工单ids
     */
    void deleteByWorkIds(List<Long> idList);

    /**
     * 根据工单ID获取工单申请单实体
     *
     * @param id 工单ID
     * @return 申请单实体
     */
    WorkApplyInfoVO getByWorkId(Long id);
}