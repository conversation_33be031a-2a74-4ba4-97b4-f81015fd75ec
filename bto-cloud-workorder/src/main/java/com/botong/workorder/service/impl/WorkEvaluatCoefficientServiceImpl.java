package com.botong.workorder.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.botong.framework.common.page.PageResult;
import com.botong.framework.mybatis.service.impl.BaseServiceImpl;
import com.botong.workorder.convert.WorkEvaluatCoefficientConvert;
import com.botong.workorder.dao.WorkEvaluatCoefficientDao;
import com.botong.workorder.entity.WorkEvaluatCoefficientEntity;
import com.botong.workorder.query.WorkEvaluatCoefficientQuery;
import com.botong.workorder.service.WorkEvaluatCoefficientService;
import com.botong.workorder.vo.WorkEvaluatCoefficientVO;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 工单考核系数服务实现类
 * 提供工单考核系数相关的业务操作实现，包括分页查询、保存、更新、删除等功能
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-07-03
 */
@Service
@AllArgsConstructor
public class WorkEvaluatCoefficientServiceImpl extends BaseServiceImpl<WorkEvaluatCoefficientDao, WorkEvaluatCoefficientEntity> implements WorkEvaluatCoefficientService {

    /**
     * 分页查询工单考核系数列表
     * 根据查询条件获取工单考核系数信息，支持分页
     * 
     * @param query 查询条件参数
     * @return 分页查询结果，包含工单考核系数列表数据
     */
    @Override
    public PageResult<WorkEvaluatCoefficientVO> page(WorkEvaluatCoefficientQuery query) {
        IPage<WorkEvaluatCoefficientEntity> page = baseMapper.selectPage(getPage(query), getWrapper(query));

        return new PageResult<>(WorkEvaluatCoefficientConvert.INSTANCE.convertList(page.getRecords()), page.getTotal());
    }

    /**
     * 构建查询条件包装器
     * 根据查询条件构建MyBatis-Plus的查询包装器
     * 
     * @param query 查询条件参数
     * @return 查询条件包装器
     */
    private LambdaQueryWrapper<WorkEvaluatCoefficientEntity> getWrapper(WorkEvaluatCoefficientQuery query){
        LambdaQueryWrapper<WorkEvaluatCoefficientEntity> wrapper = Wrappers.lambdaQuery();

        return wrapper;
    }

    /**
     * 保存工单考核系数信息
     * 将VO对象转换为实体对象并保存到数据库
     * 
     * @param vo 工单考核系数数据对象
     */
    @Override
    public void save(WorkEvaluatCoefficientVO vo) {
        WorkEvaluatCoefficientEntity entity = WorkEvaluatCoefficientConvert.INSTANCE.convert(vo);

        baseMapper.insert(entity);
    }

    /**
     * 更新工单考核系数信息
     * 将VO对象转换为实体对象并更新到数据库
     * 
     * @param vo 工单考核系数数据对象，包含需要更新的信息
     */
    @Override
    public void update(WorkEvaluatCoefficientVO vo) {
        WorkEvaluatCoefficientEntity entity = WorkEvaluatCoefficientConvert.INSTANCE.convert(vo);

        updateById(entity);
    }

    /**
     * 批量删除工单考核系数
     * 根据考核系数ID列表批量删除工单考核系数信息
     * 
     * @param idList 需要删除的考核系数ID列表
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(List<Long> idList) {
        removeByIds(idList);
    }

}