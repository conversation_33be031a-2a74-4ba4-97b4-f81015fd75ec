package com.botong.workorder.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.botong.framework.common.page.PageResult;
import com.botong.framework.mybatis.service.impl.BaseServiceImpl;
import com.botong.workorder.convert.WorkEvaluatCoefficientConvert;
import com.botong.workorder.dao.WorkEvaluatCoefficientDao;
import com.botong.workorder.entity.WorkEvaluatCoefficientEntity;
import com.botong.workorder.query.WorkEvaluatCoefficientQuery;
import com.botong.workorder.service.WorkEvaluatCoefficientService;
import com.botong.workorder.vo.WorkEvaluatCoefficientVO;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 工单考核系数表
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-07-03
 */
@Service
@AllArgsConstructor
public class WorkEvaluatCoefficientServiceImpl extends BaseServiceImpl<WorkEvaluatCoefficientDao, WorkEvaluatCoefficientEntity> implements WorkEvaluatCoefficientService {

    @Override
    public PageResult<WorkEvaluatCoefficientVO> page(WorkEvaluatCoefficientQuery query) {
        IPage<WorkEvaluatCoefficientEntity> page = baseMapper.selectPage(getPage(query), getWrapper(query));

        return new PageResult<>(WorkEvaluatCoefficientConvert.INSTANCE.convertList(page.getRecords()), page.getTotal());
    }

    private LambdaQueryWrapper<WorkEvaluatCoefficientEntity> getWrapper(WorkEvaluatCoefficientQuery query){
        LambdaQueryWrapper<WorkEvaluatCoefficientEntity> wrapper = Wrappers.lambdaQuery();

        return wrapper;
    }

    @Override
    public void save(WorkEvaluatCoefficientVO vo) {
        WorkEvaluatCoefficientEntity entity = WorkEvaluatCoefficientConvert.INSTANCE.convert(vo);

        baseMapper.insert(entity);
    }

    @Override
    public void update(WorkEvaluatCoefficientVO vo) {
        WorkEvaluatCoefficientEntity entity = WorkEvaluatCoefficientConvert.INSTANCE.convert(vo);

        updateById(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(List<Long> idList) {
        removeByIds(idList);
    }

}