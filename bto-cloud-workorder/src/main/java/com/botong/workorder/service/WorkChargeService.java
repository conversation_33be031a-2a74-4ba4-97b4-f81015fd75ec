package com.botong.workorder.service;

import com.botong.framework.mybatis.service.BaseService;
import com.botong.workorder.entity.WorkChargeEntity;
import com.botong.workorder.query.WorkChargeQuery;
import com.botong.workorder.vo.WorkChargeVO;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashMap;
import java.util.List;

/**
 * 二次增值台账表
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-02-24
 */
public interface WorkChargeService extends BaseService<WorkChargeEntity> {

    HashMap<String, Object> page(WorkChargeQuery query);

    void save(WorkChargeVO vo);

    void update(WorkChargeVO vo);

    void delete(List<Long> idList);

    List<WorkChargeVO> listByWorkId(Long workId);

    void export();

    void importByExcel(MultipartFile file);
}