package com.botong.workorder.service;

import com.botong.framework.mybatis.service.BaseService;
import com.botong.workorder.entity.WorkChargeEntity;
import com.botong.workorder.query.WorkChargeQuery;
import com.botong.workorder.vo.WorkChargeVO;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashMap;
import java.util.List;

/**
 * 二次增值台账服务接口
 * 提供二次增值台账相关的业务操作，包括分页查询、保存、更新、删除、导出、导入等功能
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-02-24
 */
public interface WorkChargeService extends BaseService<WorkChargeEntity> {

    /**
     * 分页查询二次增值台账列表
     * 
     * @param query 查询条件参数
     * @return 分页查询结果，包含二次增值台账列表数据
     */
    HashMap<String, Object> page(WorkChargeQuery query);

    /**
     * 保存二次增值台账信息
     * 
     * @param vo 二次增值台账数据对象
     */
    void save(WorkChargeVO vo);

    /**
     * 更新二次增值台账信息
     * 
     * @param vo 二次增值台账数据对象，包含需要更新的信息
     */
    void update(WorkChargeVO vo);

    /**
     * 批量删除二次增值台账
     * 
     * @param idList 需要删除的台账ID列表
     */
    void delete(List<Long> idList);

    /**
     * 根据工单ID查询二次增值台账列表
     * 
     * @param workId 工单ID
     * @return 二次增值台账列表数据
     */
    List<WorkChargeVO> listByWorkId(Long workId);

    /**
     * 导出二次增值台账数据
     */
    void export();

    /**
     * 通过Excel文件导入二次增值台账数据
     * 
     * @param file Excel文件
     */
    void importByExcel(MultipartFile file);
}