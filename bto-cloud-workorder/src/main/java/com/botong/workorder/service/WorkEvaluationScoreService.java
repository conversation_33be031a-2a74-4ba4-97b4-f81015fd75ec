package com.botong.workorder.service;

import com.botong.workorder.query.WorkEvaluationScoreQuery;
import com.botong.workorder.vo.WorkEvaluationFlatScoreVO;

/**
 * 工作订单评估评分服务接口
 * 提供工作订单评估评分相关的业务操作，包括获取工程师评估评分、导出评估结果等功能
 *
 * <AUTHOR>
 * @since 1.0.0 2025-07-04
 */
public interface WorkEvaluationScoreService {


    /**
     * 获取工程师评估评分（扁平化结构）
     *
     * @param query 查询条件
     * @return 扁平化评估评分结果
     */
    WorkEvaluationFlatScoreVO getFlatEvaluationScores(WorkEvaluationScoreQuery query);

    /**
     * 导出工程师评估评分（扁平化结构）到 Excel
     *
     * @param query 查询条件
     */
    void exportFlatEvaluationScores(WorkEvaluationScoreQuery query);
}
