package com.botong.workorder.service;

import com.botong.workorder.query.WorkEvaluationScoreQuery;
import com.botong.workorder.vo.WorkEvaluationFlatScoreVO;

/**
 * 工作订单评估评分服务
 *
 * <AUTHOR>
 * @since 1.0.0 2025-07-04
 */
public interface WorkEvaluationScoreService {


    /**
     * 获取工程师评估评分（扁平化结构）
     *
     * @param query 查询条件
     * @return 扁平化评估评分结果
     */
    WorkEvaluationFlatScoreVO getFlatEvaluationScores(WorkEvaluationScoreQuery query);

    /**
     * 导出工程师评估评分（扁平化结构）到 Excel
     *
     * @param query 查询条件
     */
    void exportFlatEvaluationScores(WorkEvaluationScoreQuery query);
}
