package com.botong.workorder.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.botong.framework.common.page.PageResult;
import com.botong.framework.mybatis.service.impl.BaseServiceImpl;
import com.botong.workorder.convert.DeviceConvert;
import com.botong.workorder.dao.DeviceDao;
import com.botong.workorder.entity.DeviceEntity;
import com.botong.workorder.query.DeviceQuery;
import com.botong.workorder.service.DeviceService;
import com.botong.workorder.vo.DeviceVO;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 设备
 *
 * <AUTHOR>
 * @since 2025-01-03
 */
@Service
@AllArgsConstructor
public class DeviceServiceImpl extends BaseServiceImpl<DeviceDao, DeviceEntity> implements DeviceService {

    @Override
    public PageResult<DeviceVO> page(DeviceQuery query) {
        IPage<DeviceEntity> page = baseMapper.selectPage(getPage(query), getWrapper(query));

        return new PageResult<>(DeviceConvert.INSTANCE.convertList(page.getRecords()), page.getTotal());
    }

    private LambdaQueryWrapper<DeviceEntity> getWrapper(DeviceQuery query) {
        LambdaQueryWrapper<DeviceEntity> wrapper = Wrappers.lambdaQuery();
        getWorkOrderDataScopeWrapper(wrapper, null, "project_special", "project_special");
        wrapper
                .eq(StrUtil.isNotBlank(query.getPlantId()), DeviceEntity::getPlantId, query.getPlantId())
                .eq(StrUtil.isNotBlank(query.getDeviceId()), DeviceEntity::getDeviceId, query.getDeviceId())
                .eq(query.getDeviceType() != null, DeviceEntity::getDeviceType, query.getDeviceType())
        ;
        return wrapper;
    }

}