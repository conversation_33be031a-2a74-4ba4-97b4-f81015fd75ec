package com.botong.workorder.service;

import com.botong.framework.common.page.PageResult;
import com.botong.framework.mybatis.service.BaseService;
import com.botong.workorder.entity.WorkServiceOfferEntity;
import com.botong.workorder.query.WorkServiceOfferQuery;
import com.botong.workorder.vo.WorkServiceOfferVO;

import java.util.List;

/**
 * 工单服务报价表
 *
 * <AUTHOR>
 * @since 2023-08-24 17:35:22
 */
public interface WorkServiceOfferService extends BaseService<WorkServiceOfferEntity> {

    /**
     * 分页查询
     *
     * @param query 查询条件
     * @return PageResult
     */
    PageResult<WorkServiceOfferVO> page(WorkServiceOfferQuery query);

    /**
     * 新增
     * @param vo vo
     */
    void save(WorkServiceOfferVO vo);

    /**
     * 修改
     * @param vo vo
     */
    void update(WorkServiceOfferVO vo);

    /**
     * 删除
     * @param idList id集合
     */
    void delete(List<Long> idList);

    /**
     * 根据工单id查询报价信息
     * @param workId 工单id
     * @return 工单材料报价信息
     */
    List<WorkServiceOfferVO> getInfoByWorkId(Long workId);

    /**
     * 根据工单id删除服务报价
     * @param idList 工单ids
     */
    void deleteByWorkIds(List<Long> idList);
}