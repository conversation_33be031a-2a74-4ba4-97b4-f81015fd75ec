package com.botong.workorder.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.botong.framework.common.utils.AssertUtils;
import com.botong.framework.mybatis.service.impl.BaseServiceImpl;
import com.botong.workorder.convert.WorkOperationPersonInfoConvert;
import com.botong.workorder.dao.WorkOperationPersonInfoDao;
import com.botong.workorder.entity.WorkOperationPersonInfoEntity;
import com.botong.workorder.query.WorkOperationPersonInfoQuery;
import com.botong.workorder.service.WorkOperationPersonInfoService;
import com.botong.workorder.vo.WorkOperationPersonInfoVO;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 工单运维人员关联服务实现类
 * 提供工单运维人员关联相关的业务操作实现，包括人员分配、查询、更新、删除等功能
 * 主要负责处理工单与维修人员之间的关联关系，支持批量操作和动态更新
 *
 * <AUTHOR>
 * @since 1.0.0 2023-08-21
 */
@Service
@AllArgsConstructor
public class WorkOperationPersonInfoServiceImpl extends BaseServiceImpl<WorkOperationPersonInfoDao, WorkOperationPersonInfoEntity> implements WorkOperationPersonInfoService {

    /**
     * 构建查询条件包装器
     * 根据查询条件构建MyBatis-Plus的查询包装器
     * 
     * @param query 查询条件参数
     * @return 查询条件包装器
     */
    private LambdaQueryWrapper<WorkOperationPersonInfoEntity> getWrapper(WorkOperationPersonInfoQuery query) {
        LambdaQueryWrapper<WorkOperationPersonInfoEntity> wrapper = Wrappers.lambdaQuery();

        return wrapper;
    }

    /**
     * 保存工单运维人员关联信息
     * 将VO对象转换为实体对象并保存到数据库
     * 
     * @param vo 运维人员关联信息数据对象
     */
    @Override
    public void save(WorkOperationPersonInfoVO vo) {
        WorkOperationPersonInfoEntity entity = WorkOperationPersonInfoConvert.INSTANCE.convert(vo);

        baseMapper.insert(entity);
    }

    /**
     * 更新工单运维人员关联信息
     * 将VO对象转换为实体对象并更新到数据库
     * 
     * @param vo 运维人员关联信息数据对象，包含需要更新的信息
     */
    @Override
    public void update(WorkOperationPersonInfoVO vo) {
        WorkOperationPersonInfoEntity entity = WorkOperationPersonInfoConvert.INSTANCE.convert(vo);

        updateById(entity);
    }

    /**
     * 批量删除工单运维人员关联信息
     * 根据关联信息ID列表批量删除运维人员关联信息
     * 
     * @param idList 需要删除的关联信息ID列表
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(List<Long> idList) {
        removeByIds(idList);
    }

    /**
     * 根据用户ID获取工单ID列表
     * 获取指定用户作为维修人员或创建者的所有工单ID列表
     * 
     * @param userId 用户ID
     * @return 工单ID列表
     */
    @Override
    public List<Long> getWorkIdsByUser(Long userId) {
        LambdaQueryWrapper<WorkOperationPersonInfoEntity> wrapper = Wrappers.lambdaQuery();
        wrapper.select(WorkOperationPersonInfoEntity::getWorkId);
        AssertUtils.isNull(userId, "userId 不能为空");
        wrapper.eq(WorkOperationPersonInfoEntity::getRepairId, userId);

        List<Long> repairWorkIdList = baseMapper.selectList(wrapper).stream().distinct().map(WorkOperationPersonInfoEntity::getWorkId).collect(Collectors.toList());
        List<Long> creatorList = baseMapper.getWorkIdListByCreatorId(userId);
        repairWorkIdList.addAll(creatorList);
        return repairWorkIdList;
    }

    /**
     * 根据工单ID批量删除运维人员关联信息
     * 根据工单ID列表批量删除对应的运维人员关联信息
     * 
     * @param idList 工单ID列表
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByWorkIds(List<Long> idList) {
        LambdaUpdateWrapper<WorkOperationPersonInfoEntity> wrapper = Wrappers.lambdaUpdate();
        wrapper.in(WorkOperationPersonInfoEntity::getWorkId, idList);
        baseMapper.delete(wrapper);
    }

    /**
     * 保存或更新工单运维人员关联信息
     * 根据工单ID和维修人员ID列表，批量保存或更新运维人员关联信息
     * 支持新增、删除、更新操作，确保数据的一致性
     * 
     * @param workId 工单ID
     * @param repairIds 维修人员ID列表
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveOrUpdate(Long workId, List<Long> repairIds) {
        // 如果维修工ID列表为空，则删除所有维修工ID
        if (CollUtil.isEmpty(repairIds)) {
            this.lambdaUpdate().eq(WorkOperationPersonInfoEntity::getRepairId, repairIds).remove();
            return;
        }
        // 数据库维修工ID列表
        List<Long> dbRepairList = getRepairIdsByWorkId(workId);

        // 需要新增的维修工ID
        Collection<Long> insertRepairIdList = CollUtil.subtract(repairIds, dbRepairList);
        if (CollUtil.isNotEmpty(insertRepairIdList)) {
            // 创建实体
            List<WorkOperationPersonInfoEntity> entities = insertRepairIdList.stream().map(repairId -> {
                WorkOperationPersonInfoEntity entity = new WorkOperationPersonInfoEntity();
                entity.setWorkId(workId);
                entity.setRepairId(repairId);
                return entity;
            }).collect(Collectors.toList());

            // 批量新增
            saveBatch(entities);
        }

        // 需要删除的维修工ID
        Collection<Long> deleteRepairIdList = CollUtil.subtract(dbRepairList, repairIds);
        if (CollUtil.isNotEmpty(deleteRepairIdList)) {
            // 创建查询条件
            LambdaQueryWrapper<WorkOperationPersonInfoEntity> queryWrapper = new LambdaQueryWrapper<>();
            remove(queryWrapper.eq(WorkOperationPersonInfoEntity::getWorkId, workId).in(WorkOperationPersonInfoEntity::getRepairId, deleteRepairIdList));
        }
    }

    /**
     * 根据工单ID获取维修人员ID列表
     * 获取指定工单分配的所有维修人员ID列表
     * 
     * @param workId 工单ID
     * @return 维修人员ID列表
     */
    @Override
    public List<Long> getRepairIdsByWorkId(Long workId) {
        // 判断工单id是否为空
        AssertUtils.isNull(workId, "工单id");
        // 根据工单id查询
        List<WorkOperationPersonInfoEntity> list = this.lambdaQuery().eq(WorkOperationPersonInfoEntity::getWorkId, workId).list();
        // 返回维修id列表
        return list.stream().map(WorkOperationPersonInfoEntity::getRepairId).collect(Collectors.toList());
    }

}