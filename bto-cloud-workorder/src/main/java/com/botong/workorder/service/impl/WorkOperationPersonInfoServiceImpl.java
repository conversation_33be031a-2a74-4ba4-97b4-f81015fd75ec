package com.botong.workorder.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.botong.framework.common.utils.AssertUtils;
import com.botong.framework.mybatis.service.impl.BaseServiceImpl;
import com.botong.workorder.convert.WorkOperationPersonInfoConvert;
import com.botong.workorder.dao.WorkOperationPersonInfoDao;
import com.botong.workorder.entity.WorkOperationPersonInfoEntity;
import com.botong.workorder.query.WorkOperationPersonInfoQuery;
import com.botong.workorder.service.WorkOperationPersonInfoService;
import com.botong.workorder.vo.WorkOperationPersonInfoVO;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 工单运维人员关联表
 *
 * <AUTHOR>
 * @since 1.0.0 2023-08-21
 */
@Service
@AllArgsConstructor
public class WorkOperationPersonInfoServiceImpl extends BaseServiceImpl<WorkOperationPersonInfoDao, WorkOperationPersonInfoEntity> implements WorkOperationPersonInfoService {

    private LambdaQueryWrapper<WorkOperationPersonInfoEntity> getWrapper(WorkOperationPersonInfoQuery query) {
        LambdaQueryWrapper<WorkOperationPersonInfoEntity> wrapper = Wrappers.lambdaQuery();

        return wrapper;
    }

    @Override
    public void save(WorkOperationPersonInfoVO vo) {
        WorkOperationPersonInfoEntity entity = WorkOperationPersonInfoConvert.INSTANCE.convert(vo);

        baseMapper.insert(entity);
    }

    @Override
    public void update(WorkOperationPersonInfoVO vo) {
        WorkOperationPersonInfoEntity entity = WorkOperationPersonInfoConvert.INSTANCE.convert(vo);

        updateById(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(List<Long> idList) {
        removeByIds(idList);
    }

    @Override
    public List<Long> getWorkIdsByUser(Long userId) {
        LambdaQueryWrapper<WorkOperationPersonInfoEntity> wrapper = Wrappers.lambdaQuery();
        wrapper.select(WorkOperationPersonInfoEntity::getWorkId);
        AssertUtils.isNull(userId, "userId 不能为空");
        wrapper.eq(WorkOperationPersonInfoEntity::getRepairId, userId);

        List<Long> repairWorkIdList = baseMapper.selectList(wrapper).stream().distinct().map(WorkOperationPersonInfoEntity::getWorkId).collect(Collectors.toList());
        List<Long> creatorList = baseMapper.getWorkIdListByCreatorId(userId);
        repairWorkIdList.addAll(creatorList);
        return repairWorkIdList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByWorkIds(List<Long> idList) {
        LambdaUpdateWrapper<WorkOperationPersonInfoEntity> wrapper = Wrappers.lambdaUpdate();
        wrapper.in(WorkOperationPersonInfoEntity::getWorkId, idList);
        baseMapper.delete(wrapper);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveOrUpdate(Long workId, List<Long> repairIds) {
        // 如果维修工ID列表为空，则删除所有维修工ID
        if (CollUtil.isEmpty(repairIds)) {
            this.lambdaUpdate().eq(WorkOperationPersonInfoEntity::getRepairId, repairIds).remove();
            return;
        }
        // 数据库维修工ID列表
        List<Long> dbRepairList = getRepairIdsByWorkId(workId);

        // 需要新增的维修工ID
        Collection<Long> insertRepairIdList = CollUtil.subtract(repairIds, dbRepairList);
        if (CollUtil.isNotEmpty(insertRepairIdList)) {
            // 创建实体
            List<WorkOperationPersonInfoEntity> entities = insertRepairIdList.stream().map(repairId -> {
                WorkOperationPersonInfoEntity entity = new WorkOperationPersonInfoEntity();
                entity.setWorkId(workId);
                entity.setRepairId(repairId);
                return entity;
            }).collect(Collectors.toList());

            // 批量新增
            saveBatch(entities);
        }

        // 需要删除的维修工ID
        Collection<Long> deleteRepairIdList = CollUtil.subtract(dbRepairList, repairIds);
        if (CollUtil.isNotEmpty(deleteRepairIdList)) {
            // 创建查询条件
            LambdaQueryWrapper<WorkOperationPersonInfoEntity> queryWrapper = new LambdaQueryWrapper<>();
            remove(queryWrapper.eq(WorkOperationPersonInfoEntity::getWorkId, workId).in(WorkOperationPersonInfoEntity::getRepairId, deleteRepairIdList));
        }
    }

    @Override
    public List<Long> getRepairIdsByWorkId(Long workId) {
        // 判断工单id是否为空
        AssertUtils.isNull(workId, "工单id");
        // 根据工单id查询
        List<WorkOperationPersonInfoEntity> list = this.lambdaQuery().eq(WorkOperationPersonInfoEntity::getWorkId, workId).list();
        // 返回维修id列表
        return list.stream().map(WorkOperationPersonInfoEntity::getRepairId).collect(Collectors.toList());
    }

}