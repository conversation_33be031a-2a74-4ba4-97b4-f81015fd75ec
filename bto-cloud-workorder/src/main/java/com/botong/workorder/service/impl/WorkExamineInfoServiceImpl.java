package com.botong.workorder.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.botong.framework.common.page.PageResult;
import com.botong.framework.mybatis.service.impl.BaseServiceImpl;
import com.botong.workorder.convert.WorkExamineInfoConvert;
import com.botong.workorder.dao.WorkExamineInfoDao;
import com.botong.workorder.entity.WorkExamineInfoEntity;
import com.botong.workorder.query.WorkExamineInfoQuery;
import com.botong.workorder.service.WorkExamineInfoService;
import com.botong.workorder.vo.WorkExamineInfoVO;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 工单审核信息服务实现类
 * 提供工单审核信息相关的业务操作实现，包括分页查询、保存、更新、删除等功能
 * 主要处理工单审核流程中的各项信息，如审核状态、审核意见、审核人等
 *
 * <AUTHOR> 
 * @since 1.0.0 2023-09-04
 */
@Service
@AllArgsConstructor
public class WorkExamineInfoServiceImpl extends BaseServiceImpl<WorkExamineInfoDao, WorkExamineInfoEntity> implements WorkExamineInfoService {

    /**
     * 分页查询工单审核信息
     * 根据查询条件获取工单审核信息列表，支持分页
     * 
     * @param query 查询条件参数
     * @return 分页查询结果，包含工单审核信息列表数据
     */
    @Override
    public PageResult<WorkExamineInfoVO> page(WorkExamineInfoQuery query) {
        IPage<WorkExamineInfoEntity> page = baseMapper.selectPage(getPage(query), getWrapper(query));

        return new PageResult<>(WorkExamineInfoConvert.INSTANCE.convertList(page.getRecords()), page.getTotal());
    }

    /**
     * 构建查询条件包装器
     * 根据查询条件构建MyBatis-Plus的查询包装器
     * 
     * @param query 查询条件参数
     * @return 查询条件包装器
     */
    private LambdaQueryWrapper<WorkExamineInfoEntity> getWrapper(WorkExamineInfoQuery query){
        LambdaQueryWrapper<WorkExamineInfoEntity> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(WorkExamineInfoEntity::getWorkId, query.getWorkId());

        return wrapper;
    }

    /**
     * 保存工单审核信息
     * 将VO对象转换为实体对象并保存到数据库
     * 
     * @param vo 工单审核信息数据对象
     */
    @Override
    public void save(WorkExamineInfoVO vo) {
        WorkExamineInfoEntity entity = WorkExamineInfoConvert.INSTANCE.convert(vo);

        baseMapper.insert(entity);
    }

    /**
     * 更新工单审核信息
     * 将VO对象转换为实体对象并更新到数据库
     * 
     * @param vo 工单审核信息数据对象，包含需要更新的信息
     */
    @Override
    public void update(WorkExamineInfoVO vo) {
        WorkExamineInfoEntity entity = WorkExamineInfoConvert.INSTANCE.convert(vo);

        updateById(entity);
    }

    /**
     * 批量删除工单审核信息
     * 根据审核信息ID列表批量删除工单审核信息
     * 
     * @param idList 需要删除的审核信息ID列表
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(List<Long> idList) {
        removeByIds(idList);
    }

    /**
     * 根据工单ID获取审核信息
     * 根据工单ID查询对应的审核信息
     * 
     * @param workId 工单ID
     * @return 工单审核信息实体对象
     */
    @Override
    public WorkExamineInfoEntity info(Long workId) {
        return this.lambdaQuery().eq(WorkExamineInfoEntity::getWorkId, workId).one();
    }

    /**
     * 根据工单ID批量删除审核信息
     * 根据工单ID列表批量删除对应的审核信息
     * 
     * @param idList 工单ID列表
     */
    @Override
    public void deleteByWorkIds(List<Long> idList) {
        if(CollUtil.isNotEmpty(idList)){
            this.lambdaUpdate().in(WorkExamineInfoEntity::getWorkId, idList).remove();
        }
    }

}