package com.botong.workorder.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.botong.framework.common.page.PageResult;
import com.botong.framework.mybatis.service.impl.BaseServiceImpl;
import com.botong.workorder.convert.WorkExamineInfoConvert;
import com.botong.workorder.dao.WorkExamineInfoDao;
import com.botong.workorder.entity.WorkExamineInfoEntity;
import com.botong.workorder.query.WorkExamineInfoQuery;
import com.botong.workorder.service.WorkExamineInfoService;
import com.botong.workorder.vo.WorkExamineInfoVO;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 工单审核管理
 *
 * <AUTHOR> 
 * @since 1.0.0 2023-09-04
 */
@Service
@AllArgsConstructor
public class WorkExamineInfoServiceImpl extends BaseServiceImpl<WorkExamineInfoDao, WorkExamineInfoEntity> implements WorkExamineInfoService {

    @Override
    public PageResult<WorkExamineInfoVO> page(WorkExamineInfoQuery query) {
        IPage<WorkExamineInfoEntity> page = baseMapper.selectPage(getPage(query), getWrapper(query));

        return new PageResult<>(WorkExamineInfoConvert.INSTANCE.convertList(page.getRecords()), page.getTotal());
    }

    private LambdaQueryWrapper<WorkExamineInfoEntity> getWrapper(WorkExamineInfoQuery query){
        LambdaQueryWrapper<WorkExamineInfoEntity> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(WorkExamineInfoEntity::getWorkId, query.getWorkId());

        return wrapper;
    }

    @Override
    public void save(WorkExamineInfoVO vo) {
        WorkExamineInfoEntity entity = WorkExamineInfoConvert.INSTANCE.convert(vo);

        baseMapper.insert(entity);
    }

    @Override
    public void update(WorkExamineInfoVO vo) {
        WorkExamineInfoEntity entity = WorkExamineInfoConvert.INSTANCE.convert(vo);

        updateById(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(List<Long> idList) {
        removeByIds(idList);
    }

    @Override
    public WorkExamineInfoEntity info(Long workId) {
        return this.lambdaQuery().eq(WorkExamineInfoEntity::getWorkId, workId).one();
    }

    @Override
    public void deleteByWorkIds(List<Long> idList) {
        if(CollUtil.isNotEmpty(idList)){
            this.lambdaUpdate().in(WorkExamineInfoEntity::getWorkId, idList).remove();
        }
    }

}