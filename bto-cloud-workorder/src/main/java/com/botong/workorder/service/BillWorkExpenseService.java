package com.botong.workorder.service;

import com.botong.framework.common.page.PageResult;
import com.botong.framework.mybatis.service.BaseService;
import com.botong.workorder.entity.BillWorkExpenseEntity;
import com.botong.workorder.query.BillWorkExpenseQuery;
import com.botong.workorder.vo.BillWorkExpenseVO;
import com.botong.workorder.vo.PageWorkExpenseVO;

import java.util.List;

/**
 * 工单费用账单服务接口
 * 提供工单费用账单相关的业务操作，包括分页查询、保存、更新、删除等功能
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-01-14
 */
public interface BillWorkExpenseService extends BaseService<BillWorkExpenseEntity> {

    /**
     * 分页查询工单费用账单列表
     * 
     * @param query 查询条件参数
     * @return 分页查询结果，包含工单费用账单列表数据
     */
    PageResult<PageWorkExpenseVO> page(BillWorkExpenseQuery query);

    /**
     * 保存工单费用账单信息
     * 
     * @param vo 工单费用账单数据对象
     */
    void save(BillWorkExpenseVO vo);

    /**
     * 更新工单费用账单信息
     * 
     * @param vo 工单费用账单数据对象，包含需要更新的信息
     */
    void update(BillWorkExpenseVO vo);

    /**
     * 批量删除工单费用账单
     * 
     * @param idList 需要删除的账单ID列表
     */
    void delete(List<Long> idList);

    /**
     * 获取工单费用账单列表
     * 
     * @param query 查询条件参数
     * @return 分页查询结果，包含账单详细信息列表
     */
    PageResult<BillWorkExpenseVO> getList(BillWorkExpenseQuery query);
}