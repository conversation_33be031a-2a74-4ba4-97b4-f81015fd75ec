package com.botong.workorder.service;

import com.botong.framework.common.page.PageResult;
import com.botong.framework.mybatis.service.BaseService;
import com.botong.workorder.entity.BillWorkExpenseEntity;
import com.botong.workorder.query.BillWorkExpenseQuery;
import com.botong.workorder.vo.BillWorkExpenseVO;
import com.botong.workorder.vo.PageWorkExpenseVO;

import java.util.List;

/**
 * 工单费用账单
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-01-14
 */
public interface BillWorkExpenseService extends BaseService<BillWorkExpenseEntity> {

    PageResult<PageWorkExpenseVO> page(BillWorkExpenseQuery query);

    void save(BillWorkExpenseVO vo);

    void update(BillWorkExpenseVO vo);

    void delete(List<Long> idList);

    PageResult<BillWorkExpenseVO> getList(BillWorkExpenseQuery query);
}