package com.botong.workorder.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.botong.framework.common.page.PageResult;
import com.botong.framework.common.utils.DateUtils;
import com.botong.framework.common.utils.ExcelUtils;
import com.botong.framework.mybatis.service.impl.BaseServiceImpl;
import com.botong.workorder.convert.WorkToolInventoryConvert;
import com.botong.workorder.dao.WorkToolInventoryDao;
import com.botong.workorder.entity.SysUserEntity;
import com.botong.workorder.entity.WorkToolInventoryEntity;
import com.botong.workorder.query.WorkToolInventoryQuery;
import com.botong.workorder.service.WorkToolInventoryService;
import com.botong.workorder.vo.WorkToolInventoryVO;
import com.fhs.trans.service.impl.TransService;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * 运维工具库服务实现类
 * 提供运维工具库存管理的业务操作实现，包括工具信息的分页查询、保存、更新、删除和导出等功能
 * 主要负责处理运维工具的基本信息管理，支持按工具名称、保管人等条件进行查询和筛选
 *
 * <AUTHOR>
 * @since 1.0.0 2025-03-11
 */
@Service
@AllArgsConstructor
public class WorkToolInventoryServiceImpl extends BaseServiceImpl<WorkToolInventoryDao, WorkToolInventoryEntity> implements WorkToolInventoryService {

    private final TransService transService;

    /**
     * 分页查询运维工具信息
     * 根据查询条件获取运维工具信息列表，支持按工具名称、保管人姓名等条件进行模糊查询
     * 通过关联查询获取保管人和审核人的用户名信息
     * 
     * @param query 查询条件参数，包含分页信息和查询条件
     * @return 分页查询结果，包含运维工具信息列表和总记录数
     */
    @Override
    public PageResult<WorkToolInventoryVO> page(WorkToolInventoryQuery query) {
        MPJLambdaWrapper<WorkToolInventoryEntity> wrapper = new MPJLambdaWrapper<>();
        wrapper.selectAll(WorkToolInventoryEntity.class)
                .select("t1.username AS custodian_name")
                .select("t2.username AS verifier_name")
                .leftJoin(SysUserEntity.class, "t1", SysUserEntity::getId, WorkToolInventoryEntity::getCustodian)
                .leftJoin(SysUserEntity.class, "t2", SysUserEntity::getId, WorkToolInventoryEntity::getVerifier)
                .like(StrUtil.isNotBlank(query.getName()), WorkToolInventoryEntity::getName, query.getName())
                .like(StrUtil.isNotBlank(query.getCustodianName()), "t1.username", query.getCustodianName())
                .eq(ObjectUtil.isNotNull(query.getCustodian()), WorkToolInventoryEntity::getCustodian, query.getCustodian())
                .orderByDesc(WorkToolInventoryEntity::getUpdateTime)
        ;
        IPage<WorkToolInventoryVO> page = baseMapper.selectJoinPage(
                new Page<>(query.getPage(), query.getLimit()),
                WorkToolInventoryVO.class,
                wrapper
        );

        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    /**
     * 构建查询条件包装器
     * 根据查询条件构建MyBatis-Plus的查询包装器
     * 目前该方法未使用，保留用于后续扩展
     * 
     * @param query 查询条件参数
     * @return 查询条件包装器
     */
    private LambdaQueryWrapper<WorkToolInventoryEntity> getWrapper(WorkToolInventoryQuery query) {
        LambdaQueryWrapper<WorkToolInventoryEntity> wrapper = Wrappers.lambdaQuery();

        return wrapper;
    }

    /**
     * 保存运维工具信息
     * 将VO对象转换为实体对象并保存到数据库
     * 
     * @param vo 运维工具信息数据对象
     */
    @Override
    public void save(WorkToolInventoryVO vo) {
        WorkToolInventoryEntity entity = WorkToolInventoryConvert.INSTANCE.convert(vo);

        baseMapper.insert(entity);
    }

    /**
     * 更新运维工具信息
     * 将VO对象转换为实体对象并更新到数据库
     * 
     * @param vo 运维工具信息数据对象，包含需要更新的信息
     */
    @Override
    public void update(WorkToolInventoryVO vo) {
        WorkToolInventoryEntity entity = WorkToolInventoryConvert.INSTANCE.convert(vo);

        updateById(entity);
    }

    /**
     * 批量删除运维工具信息
     * 根据工具信息ID列表批量删除运维工具信息
     * 
     * @param idList 需要删除的工具信息ID列表
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(List<Long> idList) {
        removeByIds(idList);
    }

    /**
     * 导出运维工具信息
     * 根据查询条件导出运维工具信息到Excel文件
     * 支持自定义查询条件，导出所有符合条件的工具信息
     * 
     * @param query 查询条件参数，用于筛选需要导出的数据
     */
    @Override
    public void export(WorkToolInventoryQuery query) {
        query.setLimit(-1);
        query.setPage(-1);
        // 写到浏览器打开
        List<WorkToolInventoryVO> list = page(query).getList();
        transService.transBatch(list);
        ExcelUtils.excelExport(WorkToolInventoryVO.class, "运维工具库导出" + DateUtils.format(new Date()), null, list);
    }

}