package com.botong.workorder.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.botong.framework.common.page.PageResult;
import com.botong.framework.common.utils.DateUtils;
import com.botong.framework.common.utils.ExcelUtils;
import com.botong.framework.mybatis.service.impl.BaseServiceImpl;
import com.botong.workorder.convert.WorkToolInventoryConvert;
import com.botong.workorder.dao.WorkToolInventoryDao;
import com.botong.workorder.entity.SysUserEntity;
import com.botong.workorder.entity.WorkToolInventoryEntity;
import com.botong.workorder.query.WorkToolInventoryQuery;
import com.botong.workorder.service.WorkToolInventoryService;
import com.botong.workorder.vo.WorkToolInventoryVO;
import com.fhs.trans.service.impl.TransService;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * 运维工具库
 *
 * <AUTHOR>
 * @since 1.0.0 2025-03-11
 */
@Service
@AllArgsConstructor
public class WorkToolInventoryServiceImpl extends BaseServiceImpl<WorkToolInventoryDao, WorkToolInventoryEntity> implements WorkToolInventoryService {

    private final TransService transService;

    @Override
    public PageResult<WorkToolInventoryVO> page(WorkToolInventoryQuery query) {
        MPJLambdaWrapper<WorkToolInventoryEntity> wrapper = new MPJLambdaWrapper<>();
        wrapper.selectAll(WorkToolInventoryEntity.class)
                .select("t1.username AS custodian_name")
                .select("t2.username AS verifier_name")
                .leftJoin(SysUserEntity.class, "t1", SysUserEntity::getId, WorkToolInventoryEntity::getCustodian)
                .leftJoin(SysUserEntity.class, "t2", SysUserEntity::getId, WorkToolInventoryEntity::getVerifier)
                .like(StrUtil.isNotBlank(query.getName()), WorkToolInventoryEntity::getName, query.getName())
                .like(StrUtil.isNotBlank(query.getCustodianName()), "t1.username", query.getCustodianName())
                .eq(ObjectUtil.isNotNull(query.getCustodian()), WorkToolInventoryEntity::getCustodian, query.getCustodian())
                .orderByDesc(WorkToolInventoryEntity::getUpdateTime)
        ;
        IPage<WorkToolInventoryVO> page = baseMapper.selectJoinPage(
                new Page<>(query.getPage(), query.getLimit()),
                WorkToolInventoryVO.class,
                wrapper
        );

        return new PageResult<>(page.getRecords(), page.getTotal());


    }

    private LambdaQueryWrapper<WorkToolInventoryEntity> getWrapper(WorkToolInventoryQuery query) {
        LambdaQueryWrapper<WorkToolInventoryEntity> wrapper = Wrappers.lambdaQuery();

        return wrapper;
    }

    @Override
    public void save(WorkToolInventoryVO vo) {
        WorkToolInventoryEntity entity = WorkToolInventoryConvert.INSTANCE.convert(vo);

        baseMapper.insert(entity);
    }

    @Override
    public void update(WorkToolInventoryVO vo) {
        WorkToolInventoryEntity entity = WorkToolInventoryConvert.INSTANCE.convert(vo);

        updateById(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(List<Long> idList) {
        removeByIds(idList);
    }

    @Override
    public void export(WorkToolInventoryQuery query) {
        query.setLimit(-1);
        query.setPage(-1);
        // 写到浏览器打开
        List<WorkToolInventoryVO> list = page(query).getList();
        transService.transBatch(list);
        ExcelUtils.excelExport(WorkToolInventoryVO.class, "运维工具库导出" + DateUtils.format(new Date()), null, list);
    }

}