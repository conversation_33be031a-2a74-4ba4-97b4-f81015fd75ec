package com.botong.workorder.service;

import com.botong.framework.common.page.PageResult;
import com.botong.framework.mybatis.service.BaseService;
import com.botong.workorder.entity.WorkExamineInfoEntity;
import com.botong.workorder.query.WorkExamineInfoQuery;
import com.botong.workorder.vo.WorkExamineInfoVO;

import java.util.List;

/**
 * 工单审核管理服务接口
 * 提供工单审核信息相关的业务操作，包括分页查询、保存、更新、删除等功能
 *
 * <AUTHOR>
 * @since 1.0.0 2023-09-04
 */
public interface WorkExamineInfoService extends BaseService<WorkExamineInfoEntity> {

    /**
     * 分页查询
     * @param query 查询条件
     * @return 审核信息集合
     */
    PageResult<WorkExamineInfoVO> page(WorkExamineInfoQuery query);

    /**
     * 新增审核信息
     * @param vo 审核信息
     */
    void save(WorkExamineInfoVO vo);

    /**
     * 修改审核信息
     * @param vo 审核信息
     */
    void update(WorkExamineInfoVO vo);

    /**
     * 根据ID删除审核信息
     * @param idList 审核信息ID集合
     */
    void delete(List<Long> idList);


    /**
     * 根据工单ID获取审核信息
     * @param workId 工单ID
     * @return 审核信息实体
     */
    WorkExamineInfoEntity info(Long workId);

    /**
     * 根据工单ID集合批量删除审核信息
     * @param idList 工单ID集合
     */
    void deleteByWorkIds(List<Long> idList);
}