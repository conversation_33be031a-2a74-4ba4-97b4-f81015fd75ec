package com.botong.workorder.service;

import cn.hutool.core.date.DateTime;
import com.botong.framework.common.page.PageResult;
import com.botong.framework.mybatis.service.BaseService;
import com.botong.workorder.dto.BatchApplyDTO;
import com.botong.workorder.dto.WorkOperationInfoDTO;
import com.botong.workorder.dto.WorkOrderAssignDTO;
import com.botong.workorder.dto.WorkOrderExamineDTO;
import com.botong.workorder.entity.WorkOrderProcessManageViewEntity;
import com.botong.workorder.query.UserAlarmTypeStatisticsQuery;
import com.botong.workorder.query.WorkOrderProcessManageQuery;
import com.botong.workorder.vo.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 工单流程管理服务接口
 * 提供工单流程管理相关的业务操作，包括工单分配、状态修改、审核、导入导出等功能
 *
 * <AUTHOR>
 * @since 2023-08-15
 */
public interface WorkOrderProcessManageService extends BaseService<WorkOrderProcessManageViewEntity> {
    /**
     * 获取工单统计信息
     *
     * @return {@link Map }<{@link String }, {@link Object }>
     * <AUTHOR>
     * @since 2024-12-31 15:47:23
     */
    Map<String, Object> getWorkOrderStatistics(List<Long> scopeList, String startTime, String endTime);

    /**
     * 获取工单统计信息（闭环率、及时率、平均处理时长）
     *
     * @param date 日期
     * @return {@link Map }<{@link String }, {@link Object }>
     * <AUTHOR>
     * @since 2024-12-31 15:47:26
     */
    Map<String, Object> count(String date,List<Long> scopeList);

    /**
     * 工单分配
     *
     * @param assignDTO 分配dto
     * <AUTHOR>
     * @since 2023-08-24 17:20:49
     */
    void workOrderAssign(WorkOrderAssignDTO assignDTO);


    /**
     * 修改工单状态
     *
     * @param vo     vo
     * @param status 状态
     * <AUTHOR>
     * @since 2023-08-28 17:53:02
     */
    void changeStatus(List<Long> vo, Integer status, Date suspendExpiration);

    /**
     * 表单提交工单录入
     *
     * @param workOrderProcessManageVO 报修实体
     */
    Long workOrderApply(WorkOrderProcessManageVO workOrderProcessManageVO);

    /**
     * Excel导入文件录入工单
     *
     * @param file Excel文件
     */
    void importByExcel(MultipartFile file);

    /**
     * Excel文件导出工单列表数据
     */
    void export(WorkOrderProcessManageQuery query);

    /**
     * 分页查询工单列表数据
     *
     * @param query 查询条件
     * @return 分页结果数据
     */
    PageResult<WorkOrderProcessManageVO> page(WorkOrderProcessManageQuery query);

    /**
     * 根据工单 ids 删除工单
     *
     * @param idList 工单ids
     */
    void delete(List<Long> idList);

    /**
     * 修改工单
     *
     * @param vo 需要修改的实体
     */
    void update(WorkOrderProcessManageVO vo);

    /**
     * 工单审核
     *
     * @param dto dto
     * <AUTHOR>
     * @since 2023-09-11 16:52:55
     */
    void review(WorkOrderExamineDTO dto);

    /**
     * 工单详情
     *
     * @param workId 工作id
     * @return {@link WorkOrderInfoVO }
     * <AUTHOR>
     * @since 2023-09-11 16:52:38
     */
    WorkOrderInfoVO details(Long workId);

    /**
     * 超时工单分页
     *
     * @param query 查询
     * @return {@link PageResult }<{@link WorkOrderProcessManageVO }>
     * <AUTHOR>
     * @since 2023-09-11 16:52:27
     */
    PageResult<WorkOrderProcessManageVO> overtimePage(WorkOrderProcessManageQuery query);

    /**
     * 保存并提交
     *
     * @param dto 到
     * <AUTHOR>
     * @since 2023-09-14 15:45:31
     */
    void saveAndSubmit(WorkOperationInfoDTO dto);

    /**
     * 获取基础工单详情信息
     *
     * @param workId 工单id
     * @return WorkOrderProcessManageVO
     */
    WorkOrderBaseDetailsVO baseDetails(Long workId);

    /**
     * 批量生成工单功能
     */
    void batchApply(List<BatchApplyDTO> batchApplyDTO);

    /**
     * 按告警时间统计工单信息
     * 根据年份和电站ID统计告警时间的工单数据
     *
     * @param year 统计年份
     * @param plantId 电站ID，可为空
     * @return 按告警时间统计的工单信息列表
     */
    List<StatisticsByAlarmTimeVO> statisticsByAlarmTime(String year, String plantId);

    /**
     * 解除工单挂起状态
     * 自动解除已满足条件的挂起工单，包括电站状态恢复或挂起期限已过期的工单
     * 将符合条件的工单状态更新为待维修状态
     */
    void unsuspend();

    /**
     * 获取月度运维工单汇总统计
     * 根据时间范围获取各城市的月度运维工单统计数据，包括各类告警数量及未完成工单数
     *
     * @param start 统计开始时间
     * @param end 统计结束时间
     * @return 月度运维工单汇总列表，每个Map包含城市、各告警类型数量、未完成工单数及合计
     */
    List<Map<String, Object>> monthlyOpsWorkOrderSummary(DateTime start, DateTime end);

    /**
     * 获取城市统计信息
     * 获取各城市的电站、告警、工单等综合统计信息，包括低电压故障、停电故障、通信故障等
     *
     * @return 城市统计信息列表，包含各城市的详细统计数据
     */
    List<CityStatsStatistics> getCityStatsStatisticsByCity();

    /**
     * 导出城市统计数据
     * 将城市统计信息导出到Excel文件，文件名包含当前日期
     */
    void exportCityStats();

    /**
     * 获取用户报警类型统计信息
     * 根据查询条件获取当前用户的报警类型统计数据，按用户分组并计算各报警类型的数量
     *
     * @param query 报警类型统计查询条件，包含时间范围、用户范围等参数
     * @return 用户报警类型统计结果列表，每个Map包含用户名、岗位、各报警类型数量及合计
     */
    List<Map<String, Object>> getUserAlarmTypeStatistics(UserAlarmTypeStatisticsQuery query);
}