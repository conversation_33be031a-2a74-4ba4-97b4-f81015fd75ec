package com.botong.workorder.service;

import cn.hutool.core.date.DateTime;
import com.botong.framework.common.page.PageResult;
import com.botong.framework.mybatis.service.BaseService;
import com.botong.workorder.dto.BatchApplyDTO;
import com.botong.workorder.dto.WorkOperationInfoDTO;
import com.botong.workorder.dto.WorkOrderAssignDTO;
import com.botong.workorder.dto.WorkOrderExamineDTO;
import com.botong.workorder.entity.WorkOrderProcessManageViewEntity;
import com.botong.workorder.query.UserAlarmTypeStatisticsQuery;
import com.botong.workorder.query.WorkOrderProcessManageQuery;
import com.botong.workorder.vo.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> by zhb on 2023/8/15.
 */

public interface WorkOrderProcessManageService extends BaseService<WorkOrderProcessManageViewEntity> {
    /**
     * 获取工单统计信息
     *
     * @return {@link Map }<{@link String }, {@link Object }>
     * <AUTHOR>
     * @since 2024-12-31 15:47:23
     */
    Map<String, Object> getWorkOrderStatistics(List<Long> scopeList, String startTime, String endTime);

    /**
     * 获取工单统计信息（闭环率、及时率、平均处理时长）
     *
     * @param date 日期
     * @return {@link Map }<{@link String }, {@link Object }>
     * <AUTHOR>
     * @since 2024-12-31 15:47:26
     */
    Map<String, Object> count(String date,List<Long> scopeList);

    /**
     * 工单分配
     *
     * @param assignDTO 分配dto
     * <AUTHOR>
     * @since 2023-08-24 17:20:49
     */
    void workOrderAssign(WorkOrderAssignDTO assignDTO);


    /**
     * 修改工单状态
     *
     * @param vo     vo
     * @param status 状态
     * <AUTHOR>
     * @since 2023-08-28 17:53:02
     */
    void changeStatus(List<Long> vo, Integer status, Date suspendExpiration);

    /**
     * 表单提交工单录入
     *
     * @param workOrderProcessManageVO 报修实体
     */
    Long workOrderApply(WorkOrderProcessManageVO workOrderProcessManageVO);

    /**
     * Excel导入文件录入工单
     *
     * @param file Excel文件
     */
    void importByExcel(MultipartFile file);

    /**
     * Excel文件导出工单列表数据
     */
    void export(WorkOrderProcessManageQuery query);

    /**
     * 分页查询工单列表数据
     *
     * @param query 查询条件
     * @return 分页结果数据
     */
    PageResult<WorkOrderProcessManageVO> page(WorkOrderProcessManageQuery query);

    /**
     * 根据工单 ids 删除工单
     *
     * @param idList 工单ids
     */
    void delete(List<Long> idList);

    /**
     * 修改工单
     *
     * @param vo 需要修改的实体
     */
    void update(WorkOrderProcessManageVO vo);

    /**
     * 工单审核
     *
     * @param dto dto
     * <AUTHOR>
     * @since 2023-09-11 16:52:55
     */
    void review(WorkOrderExamineDTO dto);

    /**
     * 工单详情
     *
     * @param workId 工作id
     * @return {@link WorkOrderInfoVO }
     * <AUTHOR>
     * @since 2023-09-11 16:52:38
     */
    WorkOrderInfoVO details(Long workId);

    /**
     * 超时工单分页
     *
     * @param query 查询
     * @return {@link PageResult }<{@link WorkOrderProcessManageVO }>
     * <AUTHOR>
     * @since 2023-09-11 16:52:27
     */
    PageResult<WorkOrderProcessManageVO> overtimePage(WorkOrderProcessManageQuery query);

    /**
     * 保存并提交
     *
     * @param dto 到
     * <AUTHOR>
     * @since 2023-09-14 15:45:31
     */
    void saveAndSubmit(WorkOperationInfoDTO dto);

    /**
     * 获取基础工单详情信息
     *
     * @param workId 工单id
     * @return WorkOrderProcessManageVO
     */
    WorkOrderBaseDetailsVO baseDetails(Long workId);

    /**
     * 批量生成工单功能
     */
    void batchApply(List<BatchApplyDTO> batchApplyDTO);


    List<StatisticsByAlarmTimeVO> statisticsByAlarmTime(String year, String plantId);

    void unsuspend();

    List<Map<String, Object>> monthlyOpsWorkOrderSummary(DateTime start, DateTime end);

    List<CityStatsStatistics> getCityStatsStatisticsByCity();

    void exportCityStats();

    List<Map<String, Object>> getUserAlarmTypeStatistics(UserAlarmTypeStatisticsQuery query);
}