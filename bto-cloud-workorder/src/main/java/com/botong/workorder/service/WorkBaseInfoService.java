package com.botong.workorder.service;

import com.botong.framework.common.page.PageResult;
import com.botong.framework.mybatis.service.BaseService;
import com.botong.workorder.entity.WorkBaseInfoEntity;
import com.botong.workorder.query.WorkBaseInfoQuery;
import com.botong.workorder.vo.WorkBaseInfoVO;

import java.util.List;

/**
 * 工单基础明细表
 *
 * <AUTHOR>
 * @since 2023-08-24 17:29:53
 */
public interface WorkBaseInfoService extends BaseService<WorkBaseInfoEntity> {

    /**
     * 分页查询
     *
     * @param query 查询
     * @return {@link PageResult }<{@link WorkBaseInfoVO }>
     * <AUTHOR>
     * @since 2023-08-14 11:02:34
     */
    PageResult<WorkBaseInfoVO> page(WorkBaseInfoQuery query);

    /**
     * 新增
     *
     * @param vo vo
     * <AUTHOR>
     * @since 2023-08-14 11:02:46
     */
    void save(WorkBaseInfoVO vo);

    /**
     * 修改
     *
     * @param vo vo
     * <AUTHOR>
     * @since 2023-08-14 11:02:49
     */
    void update(WorkBaseInfoVO vo);

    /**
     * 删除
     *
     * @param idList id列表
     * <AUTHOR>
     * @since 2023-08-14 11:02:51
     */
    void delete(List<Long> idList);

    /**
     * 是否存在工单
     *
     * @param workOrderIdList 工单id列表
     * @return boolean
     * <AUTHOR>
     * @since 2023-08-22 09:36:35
     */
    List<Long> exist(List<Long> workOrderIdList);


    /**
     * 根据工单ID获取电站用户电话
     * @param workIdList 工单ID集合
     * @return 电站用户电话
     */
    List<String> getPlantUserPhoneByWorkId(List<Long> workIdList);
}