package com.botong.workorder.service;

import com.botong.framework.common.page.PageResult;
import com.botong.framework.mybatis.service.BaseService;
import com.botong.workorder.entity.WorkBaseInfoEntity;
import com.botong.workorder.query.WorkBaseInfoQuery;
import com.botong.workorder.vo.WorkBaseInfoVO;

import java.util.List;

/**
 * 工单基础信息服务接口
 * 提供工单基础信息相关的业务操作，包括分页查询、保存、更新、删除等功能
 *
 * <AUTHOR>
 * @since 2023-08-24 17:29:53
 */
public interface WorkBaseInfoService extends BaseService<WorkBaseInfoEntity> {

    /**
     * 分页查询
     *
     * @param query 查询
     * @return {@link PageResult }<{@link WorkBaseInfoVO }>
     * <AUTHOR>
     * @since 2023-08-14 11:02:34
     */
    PageResult<WorkBaseInfoVO> page(WorkBaseInfoQuery query);

    /**
     * 新增
     *
     * @param vo vo
     * <AUTHOR>
     * @since 2023-08-14 11:02:46
     */
    void save(WorkBaseInfoVO vo);

    /**
     * 修改
     *
     * @param vo vo
     * <AUTHOR>
     * @since 2023-08-14 11:02:49
     */
    void update(WorkBaseInfoVO vo);

    /**
     * 删除
     *
     * @param idList id列表
     * <AUTHOR>
     * @since 2023-08-14 11:02:51
     */
    void delete(List<Long> idList);

    /**
     * 检查工单是否存在
     * 
     * @param workOrderIdList 工单ID列表
     * @return 存在的工单ID列表
     */
    List<Long> exist(List<Long> workOrderIdList);

    /**
     * 根据工单ID获取电站用户电话
     * 
     * @param workIdList 工单ID集合
     * @return 电站用户电话列表
     */
    List<String> getPlantUserPhoneByWorkId(List<Long> workIdList);
}