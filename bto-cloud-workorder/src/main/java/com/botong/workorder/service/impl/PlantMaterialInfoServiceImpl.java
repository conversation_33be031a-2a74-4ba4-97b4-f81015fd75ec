package com.botong.workorder.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.botong.framework.common.constant.BtoConstant;
import com.botong.framework.common.exception.ServerException;
import com.botong.framework.common.page.PageResult;
import com.botong.framework.common.utils.TreeUtils;
import com.botong.framework.mybatis.service.impl.BaseServiceImpl;
import com.botong.workorder.convert.PlantMaterialInfoConvert;
import com.botong.workorder.dao.PlantMaterialInfoDao;
import com.botong.workorder.entity.PlantMaterialInfoEntity;
import com.botong.workorder.query.PlantMaterialInfoQuery;
import com.botong.workorder.service.PlantMaterialInfoService;
import com.botong.workorder.utils.PageUtils;
import com.botong.workorder.vo.PlantMaterialInfoTree;
import com.botong.workorder.vo.PlantMaterialInfoVO;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 电站材料信息服务实现类
 * 提供电站材料信息相关的业务操作实现，包括分页查询、保存、更新、删除以及材料树结构查询等功能
 *
 * <AUTHOR>
 * @since 2023-08-23
 */
@Service
@AllArgsConstructor
public class PlantMaterialInfoServiceImpl extends BaseServiceImpl<PlantMaterialInfoDao, PlantMaterialInfoEntity> implements PlantMaterialInfoService {

    /**
     * 分页查询电站材料信息
     * 获取所有材料信息并构建树形结构，然后进行分页处理
     * 
     * @param query 查询条件参数
     * @return 分页查询结果，包含电站材料树形结构数据
     */
    @Override
    public PageResult<PlantMaterialInfoTree> page(PlantMaterialInfoQuery query) {
        List<PlantMaterialInfoTree> tree = materialList();
        return PageUtils.page(tree, query);
    }

    /**
     * 构建查询条件包装器
     * 根据查询条件构建MyBatis-Plus的查询包装器
     * 
     * @param query 查询条件参数
     * @return 查询条件包装器
     */
    private LambdaQueryWrapper<PlantMaterialInfoEntity> getWrapper(PlantMaterialInfoQuery query) {
        LambdaQueryWrapper<PlantMaterialInfoEntity> wrapper = Wrappers.lambdaQuery();

        return wrapper;
    }

    /**
     * 保存电站材料信息
     * 将VO对象转换为实体对象并保存到数据库
     * 
     * @param vo 电站材料信息视图对象
     */
    @Override
    public void save(PlantMaterialInfoVO vo) {
        PlantMaterialInfoEntity entity = PlantMaterialInfoConvert.INSTANCE.convert(vo);

        baseMapper.insert(entity);
    }

    /**
     * 更新电站材料信息
     * 将VO对象转换为实体对象并更新到数据库，同时验证上级菜单不能为自己
     * 
     * @param vo 电站材料信息视图对象，包含需要更新的信息
     */
    @Override
    public void update(PlantMaterialInfoVO vo) {
        PlantMaterialInfoEntity entity = PlantMaterialInfoConvert.INSTANCE.convert(vo);
        // 上级菜单不能为自己
        if(entity.getId().equals(entity.getPid())){
            throw new ServerException("上级不能为自己");
        }
        updateById(entity);
    }

    /**
     * 批量删除电站材料信息
     * 根据材料ID列表批量删除材料信息，并删除子级关联关系
     * 
     * @param idList 需要删除的材料ID列表
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(List<Long> idList) {
        removeByIds(idList);
        //移除关系
        this.lambdaUpdate().in(PlantMaterialInfoEntity::getPid, idList).remove();
    }


    /**
     * 获取材料树形结构列表
     * 获取所有电站材料信息并构建树形结构
     * 
     * @return 电站材料树形结构列表
     */
    @Override
    public List<PlantMaterialInfoTree> materialList() {
        List<PlantMaterialInfoEntity> entities = baseMapper.selectList(null);
        return TreeUtils.build(BeanUtil.copyToList(entities, PlantMaterialInfoTree.class), BtoConstant.ROOT);
    }
}