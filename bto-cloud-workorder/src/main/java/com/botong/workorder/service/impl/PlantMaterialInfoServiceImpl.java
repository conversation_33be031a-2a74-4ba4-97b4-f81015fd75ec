package com.botong.workorder.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.botong.framework.common.constant.BtoConstant;
import com.botong.framework.common.exception.ServerException;
import com.botong.framework.common.page.PageResult;
import com.botong.framework.common.utils.TreeUtils;
import com.botong.framework.mybatis.service.impl.BaseServiceImpl;
import com.botong.workorder.convert.PlantMaterialInfoConvert;
import com.botong.workorder.dao.PlantMaterialInfoDao;
import com.botong.workorder.entity.PlantMaterialInfoEntity;
import com.botong.workorder.query.PlantMaterialInfoQuery;
import com.botong.workorder.service.PlantMaterialInfoService;
import com.botong.workorder.utils.PageUtils;
import com.botong.workorder.vo.PlantMaterialInfoTree;
import com.botong.workorder.vo.PlantMaterialInfoVO;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 电站材料表
 *
 * <AUTHOR>
 * @since 2023-08-23
 */
@Service
@AllArgsConstructor
public class PlantMaterialInfoServiceImpl extends BaseServiceImpl<PlantMaterialInfoDao, PlantMaterialInfoEntity> implements PlantMaterialInfoService {

    @Override
    public PageResult<PlantMaterialInfoTree> page(PlantMaterialInfoQuery query) {
        List<PlantMaterialInfoTree> tree = materialList();
        return PageUtils.page(tree, query);
    }

    private LambdaQueryWrapper<PlantMaterialInfoEntity> getWrapper(PlantMaterialInfoQuery query) {
        LambdaQueryWrapper<PlantMaterialInfoEntity> wrapper = Wrappers.lambdaQuery();

        return wrapper;
    }

    @Override
    public void save(PlantMaterialInfoVO vo) {
        PlantMaterialInfoEntity entity = PlantMaterialInfoConvert.INSTANCE.convert(vo);

        baseMapper.insert(entity);
    }

    @Override
    public void update(PlantMaterialInfoVO vo) {
        PlantMaterialInfoEntity entity = PlantMaterialInfoConvert.INSTANCE.convert(vo);
        // 上级菜单不能为自己
        if(entity.getId().equals(entity.getPid())){
            throw new ServerException("上级不能为自己");
        }
        updateById(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(List<Long> idList) {
        removeByIds(idList);
        //移除关系
        this.lambdaUpdate().in(PlantMaterialInfoEntity::getPid, idList).remove();
    }


    @Override
    public List<PlantMaterialInfoTree> materialList() {
        List<PlantMaterialInfoEntity> entities = baseMapper.selectList(null);
        return TreeUtils.build(BeanUtil.copyToList(entities, PlantMaterialInfoTree.class), BtoConstant.ROOT);
    }
}