package com.botong.workorder.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.botong.framework.common.page.PageResult;
import com.botong.framework.mybatis.service.impl.BaseServiceImpl;
import com.botong.workorder.convert.WorkServiceOfferConvert;
import com.botong.workorder.dao.WorkServiceOfferDao;
import com.botong.workorder.entity.WorkServiceOfferEntity;
import com.botong.workorder.query.WorkServiceOfferQuery;
import com.botong.workorder.service.WorkServiceOfferService;
import com.botong.workorder.vo.WorkServiceOfferVO;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 工单服务报价表
 *
 * <AUTHOR> 
 * @since 1.0.0 2023-08-23
 */
@Service
@AllArgsConstructor
public class WorkServiceOfferServiceImpl extends BaseServiceImpl<WorkServiceOfferDao, WorkServiceOfferEntity> implements WorkServiceOfferService {

    @Override
    public PageResult<WorkServiceOfferVO> page(WorkServiceOfferQuery query) {
        IPage<WorkServiceOfferEntity> page = baseMapper.selectPage(getPage(query), getWrapper(query));

        return new PageResult<>(WorkServiceOfferConvert.INSTANCE.convertList(page.getRecords()), page.getTotal());
    }

    private LambdaQueryWrapper<WorkServiceOfferEntity> getWrapper(WorkServiceOfferQuery query){
        LambdaQueryWrapper<WorkServiceOfferEntity> wrapper = Wrappers.lambdaQuery();

        return wrapper;
    }

    @Override
    public void save(WorkServiceOfferVO vo) {
        WorkServiceOfferEntity entity = WorkServiceOfferConvert.INSTANCE.convert(vo);

        baseMapper.insert(entity);
    }

    @Override
    public void update(WorkServiceOfferVO vo) {
        WorkServiceOfferEntity entity = WorkServiceOfferConvert.INSTANCE.convert(vo);

        updateById(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(List<Long> idList) {
        removeByIds(idList);
    }

    @Override
    public List<WorkServiceOfferVO> getInfoByWorkId(Long workId) {
        LambdaQueryWrapper<WorkServiceOfferEntity> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(WorkServiceOfferEntity::getWorkId, workId);
        List<WorkServiceOfferEntity> workServiceOfferEntities = baseMapper.selectList(wrapper);
        return WorkServiceOfferConvert.INSTANCE.convertList(workServiceOfferEntities);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByWorkIds(List<Long> idList) {
        LambdaQueryWrapper<WorkServiceOfferEntity> wrapper = Wrappers.lambdaQuery();
        wrapper.in(WorkServiceOfferEntity::getWorkId, idList);
        remove(wrapper);
    }

}