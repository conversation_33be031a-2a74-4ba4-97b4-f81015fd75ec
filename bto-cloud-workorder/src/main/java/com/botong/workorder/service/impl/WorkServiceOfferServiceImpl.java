package com.botong.workorder.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.botong.framework.common.page.PageResult;
import com.botong.framework.mybatis.service.impl.BaseServiceImpl;
import com.botong.workorder.convert.WorkServiceOfferConvert;
import com.botong.workorder.dao.WorkServiceOfferDao;
import com.botong.workorder.entity.WorkServiceOfferEntity;
import com.botong.workorder.query.WorkServiceOfferQuery;
import com.botong.workorder.service.WorkServiceOfferService;
import com.botong.workorder.vo.WorkServiceOfferVO;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 工单服务报价服务实现类
 * 提供工单服务报价相关的业务操作实现，包括分页查询、保存、更新、删除等功能
 * 主要负责处理工单服务报价的管理，包括材料报价、服务费用等相关信息的维护
 *
 * <AUTHOR> 
 * @since 1.0.0 2023-08-23
 */
@Service
@AllArgsConstructor
public class WorkServiceOfferServiceImpl extends BaseServiceImpl<WorkServiceOfferDao, WorkServiceOfferEntity> implements WorkServiceOfferService {

    /**
     * 分页查询
     *
     * @param query 查询条件
     * @return PageResult
     */
    @Override
    public PageResult<WorkServiceOfferVO> page(WorkServiceOfferQuery query) {
        IPage<WorkServiceOfferEntity> page = baseMapper.selectPage(getPage(query), getWrapper(query));

        return new PageResult<>(WorkServiceOfferConvert.INSTANCE.convertList(page.getRecords()), page.getTotal());
    }

    /**
     * 构建查询条件包装器
     * 根据查询条件构建MyBatis-Plus的查询包装器
     *
     * @param query 查询条件参数
     * @return 查询条件包装器
     */
    private LambdaQueryWrapper<WorkServiceOfferEntity> getWrapper(WorkServiceOfferQuery query){
        LambdaQueryWrapper<WorkServiceOfferEntity> wrapper = Wrappers.lambdaQuery();

        return wrapper;
    }

    /**
     * 保存工单服务报价信息
     * 将VO对象转换为实体对象并保存到数据库
     *
     * @param vo 工单服务报价数据对象
     */
    @Override
    public void save(WorkServiceOfferVO vo) {
        WorkServiceOfferEntity entity = WorkServiceOfferConvert.INSTANCE.convert(vo);

        baseMapper.insert(entity);
    }

    /**
     * 更新工单服务报价信息
     * 将VO对象转换为实体对象并更新到数据库
     *
     * @param vo 工单服务报价数据对象，包含需要更新的信息
     */
    @Override
    public void update(WorkServiceOfferVO vo) {
        WorkServiceOfferEntity entity = WorkServiceOfferConvert.INSTANCE.convert(vo);

        updateById(entity);
    }

    /**
     * 批量删除工单服务报价
     * 根据服务报价ID列表批量删除工单服务报价信息
     *
     * @param idList 需要删除的服务报价ID列表
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(List<Long> idList) {
        removeByIds(idList);
    }

    /**
     * 根据工单id查询报价信息
     *
     * @param workId 工单id
     * @return 工单服务报价信息
     */
    @Override
    public List<WorkServiceOfferVO> getInfoByWorkId(Long workId) {
        LambdaQueryWrapper<WorkServiceOfferEntity> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(WorkServiceOfferEntity::getWorkId, workId);
        List<WorkServiceOfferEntity> workServiceOfferEntities = baseMapper.selectList(wrapper);
        return WorkServiceOfferConvert.INSTANCE.convertList(workServiceOfferEntities);
    }

    /**
     * 根据工单id删除服务报价
     *
     * @param idList 工单ids
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByWorkIds(List<Long> idList) {
        LambdaQueryWrapper<WorkServiceOfferEntity> wrapper = Wrappers.lambdaQuery();
        wrapper.in(WorkServiceOfferEntity::getWorkId, idList);
        remove(wrapper);
    }

}