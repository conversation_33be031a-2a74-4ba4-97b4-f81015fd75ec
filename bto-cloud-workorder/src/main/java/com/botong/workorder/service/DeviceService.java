package com.botong.workorder.service;

import com.botong.framework.common.page.PageResult;
import com.botong.framework.mybatis.service.BaseService;
import com.botong.workorder.entity.DeviceEntity;
import com.botong.workorder.query.DeviceQuery;
import com.botong.workorder.vo.DeviceVO;

/**
 * 设备服务接口
 * 提供设备相关的业务操作，包括分页查询等功能
 *
 * <AUTHOR>
 * @since 2025-01-03
 */
public interface DeviceService extends BaseService<DeviceEntity> {

    /**
     * 分页查询设备列表
     * 
     * @param query 查询条件参数
     * @return 分页查询结果，包含设备列表数据
     */
    PageResult<DeviceVO> page(DeviceQuery query);
}