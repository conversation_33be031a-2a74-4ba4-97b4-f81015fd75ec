package com.botong.workorder.service;

import com.botong.framework.common.page.PageResult;
import com.botong.framework.mybatis.service.BaseService;
import com.botong.workorder.entity.DeviceEntity;
import com.botong.workorder.query.DeviceQuery;
import com.botong.workorder.vo.DeviceVO;

/**
 * 设备
 *
 * <AUTHOR>
 * @since 2025-01-03
 */
public interface DeviceService extends BaseService<DeviceEntity> {

    PageResult<DeviceVO> page(DeviceQuery query);
}