package com.botong.workorder.service;

import com.botong.framework.common.page.PageResult;
import com.botong.framework.mybatis.service.BaseService;
import com.botong.workorder.entity.WorkEvaluatCoefficientEntity;
import com.botong.workorder.query.WorkEvaluatCoefficientQuery;
import com.botong.workorder.vo.WorkEvaluatCoefficientVO;

import java.util.List;

/**
 * 工单考核系数表
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-07-03
 */
public interface WorkEvaluatCoefficientService extends BaseService<WorkEvaluatCoefficientEntity> {

    PageResult<WorkEvaluatCoefficientVO> page(WorkEvaluatCoefficientQuery query);

    void save(WorkEvaluatCoefficientVO vo);

    void update(WorkEvaluatCoefficientVO vo);

    void delete(List<Long> idList);
}