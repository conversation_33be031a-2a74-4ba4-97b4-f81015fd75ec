package com.botong.workorder.service;

import com.botong.framework.common.page.PageResult;
import com.botong.framework.mybatis.service.BaseService;
import com.botong.workorder.entity.WorkEvaluatCoefficientEntity;
import com.botong.workorder.query.WorkEvaluatCoefficientQuery;
import com.botong.workorder.vo.WorkEvaluatCoefficientVO;

import java.util.List;

/**
 * 工单考核系数服务接口
 * 提供工单考核系数相关的业务操作，包括分页查询、保存、更新、删除等功能
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-07-03
 */
public interface WorkEvaluatCoefficientService extends BaseService<WorkEvaluatCoefficientEntity> {

    /**
     * 分页查询工单考核系数列表
     * 
     * @param query 查询条件参数
     * @return 分页查询结果，包含工单考核系数列表数据
     */
    PageResult<WorkEvaluatCoefficientVO> page(WorkEvaluatCoefficientQuery query);

    /**
     * 保存工单考核系数信息
     * 
     * @param vo 工单考核系数数据对象
     */
    void save(WorkEvaluatCoefficientVO vo);

    /**
     * 更新工单考核系数信息
     * 
     * @param vo 工单考核系数数据对象，包含需要更新的信息
     */
    void update(WorkEvaluatCoefficientVO vo);

    /**
     * 批量删除工单考核系数
     * 
     * @param idList 需要删除的考核系数ID列表
     */
    void delete(List<Long> idList);
}