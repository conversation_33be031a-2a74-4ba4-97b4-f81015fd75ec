package com.botong.workorder.service;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.botong.api.module.photovoltaic.vo.PlantAlarmVO;
import com.botong.framework.common.page.PageResult;
import com.botong.framework.mybatis.service.BaseService;
import com.botong.workorder.entity.PlantAlarmEntity;
import com.botong.workorder.query.PlantAlarmQuery;

import java.util.List;

/**
 * 告警视图
 *
 * <AUTHOR> 
 * @since 1.0.0 2024-01-24
 */
public interface PlantAlarmService extends BaseService<PlantAlarmEntity> {

    PageResult<PlantAlarmVO> page(PlantAlarmQuery query);

    IPage<PlantAlarmVO> getPageList(PlantAlarmQuery query, Page<PlantAlarmVO> page);

    void save(PlantAlarmVO vo);

    void update(PlantAlarmVO vo);

    void delete(List<Long> idList);
}