package com.botong.workorder.service;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.botong.api.module.photovoltaic.vo.PlantAlarmVO;
import com.botong.framework.common.page.PageResult;
import com.botong.framework.mybatis.service.BaseService;
import com.botong.workorder.entity.PlantAlarmEntity;
import com.botong.workorder.query.PlantAlarmQuery;

import java.util.List;

/**
 * 告警视图服务接口
 * 提供告警信息相关的业务操作，包括分页查询、保存、更新、删除等功能
 * 
 * <AUTHOR> 
 * @since 1.0.0 2024-01-24
 */
public interface PlantAlarmService extends BaseService<PlantAlarmEntity> {

    /**
     * 分页查询告警信息列表
     * 
     * @param query 查询条件参数
     * @return 分页查询结果，包含告警信息列表数据
     */
    PageResult<PlantAlarmVO> page(PlantAlarmQuery query);

    /**
     * 根据分页参数获取告警信息列表
     * 
     * @param query 查询条件参数
     * @param page 分页参数
     * @return 分页查询结果，包含告警信息列表数据
     */
    IPage<PlantAlarmVO> getPageList(PlantAlarmQuery query, Page<PlantAlarmVO> page);

    /**
     * 保存告警信息
     * 
     * @param vo 告警信息数据对象
     */
    void save(PlantAlarmVO vo);

    /**
     * 更新告警信息
     * 
     * @param vo 告警信息数据对象，包含需要更新的信息
     */
    void update(PlantAlarmVO vo);

    /**
     * 批量删除告警信息
     * 
     * @param idList 需要删除的告警信息ID列表
     */
    void delete(List<Long> idList);
}