<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.botong.workorder.dao.WorkBaseInfoDao">

    <resultMap type="com.botong.workorder.entity.WorkBaseInfoEntity" id="workBaseInfoMap">
        <result property="id" column="id"/>
        <result property="status" column="status"/>
        <result property="plantId" column="plant_id"/>
        <result property="plantType" column="plant_type"/>
        <result property="source" column="source"/>
        <result property="alarmTime" column="alarm_time"/>
        <result property="alarmLevel" column="alarm_level"/>
        <result property="creator" column="creator"/>
        <result property="updater" column="updater"/>
        <result property="version" column="version"/>
        <result property="deleted" column="deleted"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="alarmType" column="alarm_type"/>
        <result property="issuePhoto" column="issue_photo"/>
    </resultMap>
    <select id="getPlantUserPhoneByWorkId" resultType="java.lang.String">
        SELECT
            plant_base_info.user_phone
        FROM
            work_base_info
                LEFT JOIN plant_base_info ON work_base_info.plant_id = plant_base_info.id
        WHERE
            work_base_info.id IN
        <foreach item="item" collection="workIdList" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    <select id="trans" resultType="java.util.HashMap">
        SELECT
        id plant_type,
        (SELECT GROUP_CONCAT( id ) FROM sys_user WHERE username IN
            <foreach item="item" collection="repairs" open="(" separator="," close=")">
                #{item}
            </foreach>) repairs
        FROM
            sys_org
        WHERE
        name = #{plantTypeLabel}
    </select>
    <select id="getPlantTypeAndRepairById" resultType="java.util.HashMap">
        SELECT
            `name` plantTypeLabel,(
            SELECT
                GROUP_CONCAT( username SEPARATOR '、' )
            FROM
                sys_user
            WHERE
                    id IN ( SELECT repair_id FROM work_operation_person_info WHERE work_id = #{workId} AND deleted = 0 )
              AND deleted = 0
        ) `repair`
        FROM
            sys_org
        WHERE
            id = ( SELECT plant_type FROM work_base_info WHERE id = #{workId} )
          AND deleted = 0
    </select>
    <select id="getStatus" resultType="com.botong.workorder.vo.WorkOrderProcessManageVO">
        SELECT plant_id, status
        FROM work_base_info
        <where>
            <if test="plantIds != null and plantIds.size() > 0">
                plant_id IN
                <foreach item="item" collection="plantIds" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            AND status != 3 AND deleted = 0
        </where>
    </select>

</mapper>