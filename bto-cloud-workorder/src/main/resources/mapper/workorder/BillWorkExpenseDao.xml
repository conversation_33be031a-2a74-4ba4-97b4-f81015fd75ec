<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.botong.workorder.dao.BillWorkExpenseDao">

    <resultMap type="com.botong.workorder.entity.BillWorkExpenseEntity" id="billWorkExpenseMap">
        <result property="id" column="id"/>
        <result property="workId" column="work_id"/>
        <result property="name" column="name"/>
        <result property="specification" column="specification"/>
        <result property="quantity" column="quantity"/>
        <result property="price" column="price"/>
        <result property="remark" column="remark"/>
        <result property="version" column="version"/>
        <result property="deleted" column="deleted"/>
        <result property="creator" column="creator"/>
        <result property="createTime" column="create_time"/>
        <result property="updater" column="updater"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>
    <select id="page" resultType="com.botong.workorder.vo.PageWorkExpenseVO">
        SELECT
        bill_work_expense.work_id,
        plant_base_info.plant_name,
        plant_base_info.project_id projectOrg,
        work_base_info.`status` workStatus,
        SUM( bill_work_expense.total_price ) AS totalPrice,
        bill_work_expense.create_time billDate
        FROM
        bill_work_expense
        LEFT JOIN work_base_info ON bill_work_expense.work_id = work_base_info.id
        LEFT JOIN plant_base_info ON work_base_info.plant_id = plant_base_info.id
        <where>
            work_base_info.deleted = 0
        </where>
        GROUP BY
        bill_work_expense.work_id
    </select>

</mapper>