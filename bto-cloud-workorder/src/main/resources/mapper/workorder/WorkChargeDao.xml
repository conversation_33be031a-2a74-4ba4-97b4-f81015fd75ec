<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.botong.workorder.dao.WorkChargeDao">

    <resultMap type="com.botong.workorder.entity.WorkChargeEntity" id="workChargeMap">
        <result property="id" column="id"/>
        <result property="plantUid" column="plant_uid"/>
        <result property="platformType" column="platform_type"/>
        <result property="plantName" column="plant_name"/>
        <result property="isAssociated" column="is_associated"/>
        <result property="workOrderId" column="work_order_id"/>
        <result property="chargeDate" column="charge_date"/>
        <result property="chargeType" column="charge_type"/>
        <result property="maintenanceContent" column="maintenance_content"/>
        <result property="quantity" column="quantity"/>
        <result property="unitPrice" column="unit_price"/>
        <result property="totalAmount" column="total_amount"/>
        <result property="payee" column="payee"/>
        <result property="remarks" column="remarks"/>
        <result property="attachment" column="attachment"/>
    </resultMap>
    <select id="page" resultType="com.botong.workorder.entity.WorkChargeEntity">
        SELECT
        wc.id,
        wc.platform_type,
        wc.work_order_id,
        wc.is_associated,
        wc.plant_uid,
        wc.charge_date,
        wc.charge_type,
        wc.maintenance_content,
        wc.quantity,
        wc.unit_price,
        wc.total_amount,
        wc.payee,
        wc.attachment,
        wc.remarks,
        CASE
        WHEN wc.platform_type = 0 THEN
        pbi.plant_name ELSE wc.plant_name
        END AS plantName
        FROM
        work_charge wc
        LEFT JOIN plant_base_info pbi ON wc.platform_type = 0
        AND wc.plant_uid = pbi.id
        <where>
            wc.deleted = 0
            <if test="query.plantName != null and query.plantName != ''">
                AND pbi.plant_name LIKE CONCAT('%', #{query.plantName}, '%')
            </if>
            <if test="query.chargeType != null">
                AND wc.charge_type = #{query.chargeType}
            </if>
            <if test="query.startTime != null and query.startTime != '' and query.endTime != null and query.endTime != ''">
                AND wc.charge_date BETWEEN #{query.startTime} AND #{query.endTime}
            </if>
            <if test="query.isAssociated != null">
                AND wc.is_associated = #{query.isAssociated}
            </if>
            <if test="query.workOrderId != null">
                AND wc.work_order_id = #{query.workOrderId}
            </if>
        </where>
        ORDER BY wc.update_time DESC
    </select>
    <select id="getList" resultType="com.botong.workorder.vo.WorkChargeVO">
        SELECT
        wc.id,
        wc.platform_type,
        wc.work_order_id,
        wc.is_associated,
        wc.plant_uid,
        wc.charge_date,
        wc.charge_type,
        wc.maintenance_content,
        wc.quantity,
        wc.unit_price,
        wc.total_amount,
        wc.payee,
        wc.attachment,
        wc.remarks,
        CASE
        WHEN wc.platform_type = 0 THEN
        pbi.plant_name ELSE wc.plant_name
        END AS plantName
        FROM
        work_charge wc
        LEFT JOIN plant_base_info pbi ON wc.platform_type = 0
        AND wc.plant_uid = pbi.id
        <where>
            wc.deleted = 0
            <if test="workId != null">
                AND wc.work_order_id = #{workId}
            </if>
        </where>
    </select>
    <select id="getChargeStatistics" resultType="com.botong.workorder.vo.ChargeStatisticsVO">
        SELECT
            SUM( wc.quantity ) AS totalQuantity,
            SUM( wc.total_amount ) AS totalAmount
        FROM
            work_charge wc
                LEFT JOIN plant_base_info pbi ON wc.platform_type = 0
                AND wc.plant_uid = pbi.id
        <where>
            wc.deleted = 0
            <if test="query.plantName != null and query.plantName != ''">
                AND pbi.plant_name LIKE CONCAT('%', #{query.plantName}, '%')
            </if>
            <if test="query.chargeType != null">
                AND wc.charge_type = #{query.chargeType}
            </if>
            <if test="query.startTime != null and query.startTime != '' and query.endTime != null and query.endTime != ''">
                AND wc.charge_date BETWEEN #{query.startTime} AND #{query.endTime}
            </if>
            <if test="query.isAssociated != null">
                AND wc.is_associated = #{query.isAssociated}
            </if>
            <if test="query.workOrderId != null">
                AND wc.work_order_id = #{query.workOrderId}
            </if>
        </where>
        ORDER BY wc.update_time DESC
    </select>

</mapper>