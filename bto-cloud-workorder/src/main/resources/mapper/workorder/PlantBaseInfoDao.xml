<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.botong.workorder.dao.PlantBaseInfoDao">

    <resultMap type="com.botong.workorder.entity.PlantBaseInfoEntity" id="plantBaseInfoMap">
        <result property="id" column="id"/>
        <result property="plantName" column="plant_name"/>
        <result property="inverterId" column="inverter_id"/>
        <result property="address" column="address"/>
        <result property="country" column="country"/>
        <result property="province" column="province"/>
        <result property="city" column="city"/>
        <result property="area" column="area"/>
        <result property="town" column="town"/>
        <result property="plantUserName" column="plant_user_name"/>
        <result property="userPhone" column="user_phone"/>
        <result property="type" column="type"/>
        <result property="warrantyContent" column="warranty_content"/>
        <result property="warrantyExpireDate" column="warranty_expire_date"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="flow" column="flow"/>
        <result property="warrantyStatus" column="warranty_status"/>
        <result property="projectId" column="project_id"/>
        <result property="projectName" column="project_name"/>
    </resultMap>
    <select id="getAllProject" resultType="com.botong.api.module.workorder.entity.OrgBaseEntity">
        SELECT id, name, pid
        FROM v_project_category
    </select>

    <select id="getPlantTypeByOrgId" resultType="java.lang.String">
        SELECT id FROM v_project_category WHERE `name` IN
        (SELECT `name` FROM sys_org
        <where>deleted = 0
            <if test="
            orgIds !=null and orgIds.size()>0">
                and id in
                <foreach collection="orgIds" open="(" close=")" separator="," item="id">
                    #{id}
                </foreach>
            </if>
        </where>
        )
    </select>
    <select id="transOrgId" resultType="java.lang.String">
        SELECT DISTINCT id
        FROM v_project_category
        WHERE `name` IN
        (SELECT `name`
        FROM sys_org
        <where>
            deleted = 0
            <if test="orgIds !=null and orgIds.size()>0">
                and id in
                <foreach collection="orgIds" open="(" close=")" separator="," item="id">
                    #{id}
                </foreach>
            </if>
        </where>
        )

    </select>
    <select id="getContractIdByPlantId" resultType="java.lang.String">
        select contract_id
        from v_plant_base
        where plant_uid = #{plantId}
    </select>
    <select id="getElectricalDiagram" resultType="java.lang.String">
        select electrical_diagram
        from ${electricalTable}
        where order_id = #{contractId};
    </select>

    <select id="getSiteDiagramYx" resultType="java.lang.String">
        select file_addr
        from ${siteTable}
        where order_id = #{contractId}
    </select>
    <select id="getSiteDiagram" resultType="com.botong.workorder.vo.SiteDiagramVO">
        select file_name, file_addr
        from ${siteTable}
        where order_id = #{contractId}
    </select>
    <select id="getOrgIdByProjectIds" resultType="java.lang.String">
        WITH RECURSIVE sub_nodes AS (SELECT id,
                                            pid,
                                            NAME
                                     FROM sys_org
                                     WHERE id = #{plantType}
                                     UNION ALL
                                     SELECT t.id,
                                            t.pid,
                                            t.NAME
                                     FROM sys_org t
                                              JOIN sub_nodes sn ON t.pid = sn.id)
        SELECT v_project_category.id
        FROM sub_nodes
                 LEFT JOIN v_project_category ON sub_nodes.NAME = v_project_category.`name`
    </select>
    <select id="getLowPowerPlant" resultType="com.botong.workorder.vo.LowPowerPlantVO">
        WITH plant_efficiency AS (
        SELECT
        t1.city,
        t1.project_special,
        t1.plant_uid,
        t1.plant_name,
        t1.plant_capacity,
        t1.total_electricity / 100 as total_electricity,
        t1.plant_status status,
        t1.create_time,
        SUM(t2.electricity)/ 100 AS today_electricity,
        ROUND( (( SUM(t2.electricity) ) / 100 ) / ( t1.plant_capacity / 1000 ), 4 ) AS equivalent_hours
        FROM bto_plant_base t1
        LEFT JOIN bto_plant_day t2
        ON t1.plant_uid = t2.plant_uid
        AND t2.collect BETWEEN #{query.startTime} and #{query.endTime}
        <where>
            t1.is_deleted = '0'
            <if test="scope !=null and scope.size()>0 ">
                AND t1.project_special IN
                <foreach collection="scope" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
        </where>
        GROUP BY t1.plant_uid, t1.plant_name, t1.city, t1.plant_capacity, t1.project_special
        ),
        city_average AS (
        SELECT
        city,
        ROUND(SUM(equivalent_hours) / COUNT(*), 4) AS city_avg_hours
        FROM plant_efficiency
        WHERE equivalent_hours &gt; 0
        AND project_special LIKE '40%'
        GROUP BY city
        )
        SELECT
        p.city,
        p.project_special,
        p.plant_uid,
        p.plant_name,
        p.plant_capacity,
        p.total_electricity,
        p.status,
        p.create_time,
        p.today_electricity,
        p.equivalent_hours,
        c.city_avg_hours,
        ROUND(p.equivalent_hours / (c.city_avg_hours * 1.25), 4) AS city_generation_rate
        FROM plant_efficiency p
        JOIN city_average c ON p.city = c.city
        <where>
            <if test="query.greaterThan != null">
                AND ROUND(p.equivalent_hours / c.city_avg_hours,4) &gt; #{query.greaterThan}
            </if>
            <if test="query.lessThan != null">
                AND ROUND(p.equivalent_hours / c.city_avg_hours,4) &lt; #{query.lessThan}
            </if>
            <if test="orgIds != null and orgIds.size()>0 ">
                AND p.project_special IN
                <foreach collection="orgIds" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="query.city != null and query.city.trim()!='' ">
                AND p.city LIKE CONCAT('%',#{query.city},'%')
            </if>
            <if test="query.plantName != null and query.plantName.trim()!='' ">
                AND p.plant_name LIKE CONCAT('%',#{query.plantName},'%')
            </if>
            <if test="query.status != null">
                AND p.status = #{query.status}
            </if>
        </where>

        <if test="query.order != null and query.order.trim() != ''">
        ORDER BY
            ${query.order}
        </if>
        <if test="query.asc != null">
            <choose>
                <when test="query.asc">
                    ASC
                </when>
                <otherwise>
                    DESC
                </otherwise>
            </choose>
        </if>

    </select>
    <select id="getAlarmStr" resultType="java.lang.String">
        SELECT
            GROUP_CONCAT( DISTINCT alarm_mean SEPARATOR '、' ) AS combined_alarm
        FROM
            v_plant_alarm
        WHERE plant_uid = #{plantId}
    </select>


</mapper>