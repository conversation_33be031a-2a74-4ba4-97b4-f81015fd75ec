server:
  port: 58085

spring:
  servlet:
    multipart:
      max-file-size: 20MB
      max-request-size: 20MB
  application:
    name: work-order-cloud-workorder
  profiles:
    active: dev
  cloud:
    nacos:
      discovery:
        server-addr: ${nacos_host:127.0.0.1}:${nacos_port:8848}
        # 命名空间，默认：public
        namespace: ${nacos_namespace:}
        service: ${spring.application.name}
      config:
        server-addr: ${spring.cloud.nacos.discovery.server-addr}
        namespace: ${spring.cloud.nacos.discovery.namespace}
        file-extension: yaml
        # 指定配置
        extension-configs:
          - data-id: datasource.yaml
            refresh: true
          - data-id: common.yaml
            refresh: true

mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
logging:
  level:
    com.botong.api.module: DEBUG