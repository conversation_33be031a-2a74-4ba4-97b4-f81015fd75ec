version: '3.8'
services:

  gateway:
    build:
      context: ./bto-cloud-gateway
    ports:
      - "58080:58080"

  system:
    build:
      context: ./bto-cloud-system
    ports:
      - "58081:58081"

  quartz:
    build:
      context: ./bto-cloud-module/bto-module-quartz
    ports:
      - "58082:58082"

  message:
    build:
      context: ./bto-cloud-module/bto-module-message
    ports:
      - "58083:58083"

  monitor:
    build:
      context: ./bto-cloud-module/bto-module-monitor
    ports:
      - "58084:58084"

  workorder:
    build:
      context: ./bto-cloud-workorder
    ports:
      - "58085:58085"