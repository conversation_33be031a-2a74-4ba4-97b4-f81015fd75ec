server:
  port: 58080

spring:
  application:
    name: work-order-cloud-gateway
  profiles:
    active: dev
  cloud:
    gateway:
      metrics:
        enabled: true
      discovery:
        locator:
          enabled: true
      routes:
        - id: work-order-cloud-system
          uri: lb://work-order-cloud-system
          order: 1
          predicates:
            - Path=/sys/**
          filters:
            - StripPrefix=1
        - id: bto-module-quartz
          uri: lb://bto-module-quartz
          order: 2
          predicates:
            - Path=/schedule/**
          filters:
            - StripPrefix=1
        - id: bto-module-message
          uri: lb://bto-module-message
          order: 3
          predicates:
            - Path=/message/**
          filters:
            - StripPrefix=1
        - id: bto-module-monitor
          uri: lb://bto-module-monitor
          order: 4
          predicates:
            - Path=/monitor/**
          filters:
            - StripPrefix=1
        - id: work-order-cloud-workder
          uri: lb://work-order-cloud-workorder
          order: 5
          predicates:
            - Path=/workorder/**
          filters:
            - StripPrefix=1
    nacos:
      discovery:
        server-addr: ${nacos_host:127.0.0.1}:${nacos_port:8848}
        # 命名空间，默认：public
        namespace: ${nacos_namespace:}
        service: ${spring.application.name}
      config:
        server-addr: ${spring.cloud.nacos.discovery.server-addr}
        namespace: ${spring.cloud.nacos.discovery.namespace}
        file-extension: yaml
        # 指定配置
        extension-configs:
          - data-id: datasource.yaml
            refresh: true
          - data-id: common.yaml
            refresh: true

logging:
  level:
    org:
      springframework:
        cloud:
          gateway: TRACE
knife4j:
  gateway:
    enabled: true
    # 指定服务发现的模式聚合微服务文档，并且是默认`default`分组
    strategy: discover
    discover:
      enabled: true
      # 指定版本号(Swagger2|OpenAPI3)
      version: openapi3
      excluded-services:
        # 排除order开头的配置
        - work-order-cloud-gateway