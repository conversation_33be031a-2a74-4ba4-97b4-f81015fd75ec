package com.botong.framework.security.operatelog.service;

import cn.hutool.core.util.StrUtil;
import com.botong.framework.common.cache.RedisCache;
import com.botong.framework.common.cache.RedisKeys;
import com.botong.framework.common.utils.AddressUtils;
import com.botong.framework.security.operatelog.dto.OperateLogDTO;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
@Slf4j
public class OperateLogService {
    private final RedisCache redisCache;

    @Async("operateLogExecutor")
    public void enqueue(OperateLogDTO logDto, boolean fillAddress) {
        try {
            if (fillAddress && StrUtil.isNotBlank(logDto.getIp())) {
                // 本地/分布式缓存一层，防止每次都查第三方
                logDto.setAddress(resolveAddressCached(logDto.getIp()));
            } else {
                logDto.setAddress(null);
            }
            String key = RedisKeys.getLogKey();
            redisCache.leftPush(key, logDto, RedisCache.NOT_EXPIRE);
        } catch (Exception e) {
            // 只打日志，绝不影响业务线程
            log.warn("save operate log failed: {}", e.getMessage());
        }
    }

    // 如你启用了 Spring Cache，配置合适的缓存管理器（Caffeine/Redis 皆可）
    @Cacheable(value = "ip2addr", key = "#ip", unless = "#result == null")
    public String resolveAddressCached(String ip) {
        try {
            return AddressUtils.getAddressByIP(ip);
        } catch (Exception e) {
            log.debug("ip2addr failed for {}: {}", ip, e.getMessage());
            return null; // 失败就返回 null，不阻塞
        }
    }
}
