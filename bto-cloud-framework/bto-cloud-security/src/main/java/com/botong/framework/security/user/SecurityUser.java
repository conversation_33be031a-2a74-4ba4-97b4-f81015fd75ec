package com.botong.framework.security.user;

import cn.hutool.core.collection.CollUtil;
import com.botong.framework.common.constant.BtoConstant;
import com.botong.framework.common.exception.ServerException;
import org.springframework.security.core.context.SecurityContextHolder;

import java.util.List;

/**
 * 用户
 */
public class SecurityUser {
    /**
     * 获取用户信息
     */
    public static UserDetail getUser() {
        UserDetail user;
        try {
            user = (UserDetail) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        } catch (Exception e) {
            return new UserDetail();
        }

        return user;
    }

    /**
     * 获取用户ID
     */
    public static Long getUserId() {
        return getUser().getId();
    }

    /**
     * 获取用户名称
     */
    public static String getUsername() {
        return getUser().getUsername();
    }

    public static List<Long> getDataScopeList() {
        UserDetail user = getUser();
        List<Long> dataScopeList = user.getDataScopeList();
        if (CollUtil.isEmpty(dataScopeList) && !user.getSuperAdmin().equals(BtoConstant.SUPER_ADMIN)) {
            throw new ServerException("数据权限未配置");
        }
        return dataScopeList;
    }
}