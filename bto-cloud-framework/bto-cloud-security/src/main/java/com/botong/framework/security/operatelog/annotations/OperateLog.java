package com.botong.framework.security.operatelog.annotations;

import com.botong.framework.security.operatelog.enums.OperateTypeEnum;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 操作日志
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
public @interface OperateLog {
    /**
     * 模块名
     */
    String module() default "";

    /**
     * 操作名
     */
    String name() default "";

    /**
     * 操作类型
     */
    OperateTypeEnum[] type() default OperateTypeEnum.OTHER;

    // 采样比例（0~1），默认全量
    double sample() default 1.0;

    // 是否记录请求参数
    boolean logParams() default true;

    // 是否解析IP归属地（较耗时）
    boolean logAddress() default true;

    // 参数JSON最大长度（防止超大对象）
    int paramMaxLen() default 2000;
}