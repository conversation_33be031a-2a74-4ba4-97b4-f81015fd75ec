package com.botong.framework.security.operatelog.aspect;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.botong.framework.common.utils.HttpContextUtils;
import com.botong.framework.common.utils.IpUtils;
import com.botong.framework.common.utils.JsonUtils;
import com.botong.framework.security.operatelog.annotations.OperateLog;
import com.botong.framework.security.operatelog.dto.OperateLogDTO;
import com.botong.framework.security.operatelog.service.OperateLogService;
import com.botong.framework.security.user.SecurityUser;
import com.botong.framework.security.user.UserDetail;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;
import org.springframework.validation.BindingResult;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.lang.annotation.Annotation;
import java.lang.reflect.Array;
import java.util.Collection;
import java.util.Map;
import java.util.stream.IntStream;

@Aspect
@Component
@AllArgsConstructor
@Slf4j
@Order(Integer.MAX_VALUE) // 让日志切面尽量后执行，降低对业务的干扰
public class OperateLogAspect {
    private final OperateLogService operateLogService;

    @Around("@annotation(operateLog)")
    public Object around(ProceedingJoinPoint joinPoint, OperateLog operateLog) throws Throwable {
        final long startNs = System.nanoTime();
        Object result;
        try {
            result = joinPoint.proceed();
            // 成功：异步投递（不阻塞业务）
            asyncLog(joinPoint, operateLog, startNs, 1, "success", null);
            return result;
        } catch (Exception e) {
            // 失败：异步投递错误信息，但仍然把异常抛给业务（不吞异常）
            asyncLog(joinPoint, operateLog, startNs, 0, null, e);
            throw e;
        } catch (Throwable t) {
            // 任意Throwable都保护业务语义
            asyncLog(joinPoint, operateLog, startNs, 0, null, new Exception(t));
            throw t;
        }
    }

    private void asyncLog(ProceedingJoinPoint joinPoint, OperateLog operateLog,
                          long startNs, int status, String successMsg, Exception ex) {
        try {
            // 采样（避免高QPS全量打点）
            if (operateLog.sample() < 1.0 && RandomUtil.randomDouble(0,1) > operateLog.sample()) {
                return;
            }

            OperateLogDTO dto = new OperateLogDTO();
            // 时间：用纳秒计时，减少开销
            int durationMs = (int) ((System.nanoTime() - startNs) / 1_000_000);
            dto.setDuration(durationMs);

            // 用户
            UserDetail user = SecurityUser.getUser();
            if (user != null) {
                dto.setUserId(user.getId());
                dto.setRealName(user.getRealName());
            }

            // operate type/module/name
            dto.setOperateType(operateLog.type()[0].getValue());
            dto.setModule(StrUtil.nullToDefault(operateLog.module(), ""));
            dto.setName(StrUtil.nullToDefault(operateLog.name(), ""));

            if (StrUtil.isBlank(dto.getModule())) {
                Tag tag = getClassAnnotation(joinPoint, Tag.class);
                if (tag != null) dto.setModule(tag.name());
            }
            if (StrUtil.isBlank(dto.getName())) {
                Operation operation = getMethodAnnotation(joinPoint, Operation.class);
                if (operation != null) dto.setName(operation.summary());
            }

            // 请求元信息
            HttpServletRequest request = HttpContextUtils.getHttpServletRequest();
            if (request != null) {
                dto.setIp(IpUtils.getIpAddr(request));
                dto.setUserAgent(request.getHeader(HttpHeaders.USER_AGENT));
                dto.setReqUri(request.getRequestURI());
                dto.setReqMethod(request.getMethod());
            }

            // 参数记录（可关闭、可截断；仅做轻量 JSON）
            if (operateLog.logParams()) {
                String params = obtainMethodArgsLight(joinPoint, operateLog.paramMaxLen());
                dto.setReqParams(params);
            } else {
                dto.setReqParams(null);
            }

            dto.setStatus(status);
            dto.setResultMsg(status == 1 ? (successMsg != null ? successMsg : "success")
                    : buildErrorMessage(ex));

            // **关键：异步去补齐地址并入队**
            operateLogService.enqueue(dto, operateLog.logAddress());
        } catch (Exception err) {
            // 绝不影响业务
            log.warn("OperateLog enqueue failed: {}", err.getMessage());
        }
    }

    private String obtainMethodArgsLight(ProceedingJoinPoint joinPoint, int maxLen) {
        try {
            MethodSignature ms = (MethodSignature) joinPoint.getSignature();
            String[] argNames = ms.getParameterNames();
            Object[] argValues = joinPoint.getArgs();
            if (argNames == null || argValues == null) return "{}";

            Map<String, Object> args = MapUtil.newHashMap(Math.min(argValues.length, 8));
            for (int i = 0; i < argNames.length && i < argValues.length; i++) {
                String name = argNames[i];
                Object value = argValues[i];
                if (name != null) {
                    args.put(name, ignoreArgs(value) ? "[ignore]" : value);
                }
            }
            String json = JsonUtils.toJsonString(args);
            return json.length() > maxLen ? json.substring(0, maxLen) + "..." : json;
        } catch (Exception e) {
            log.warn("obtain args failed: {}", e.getMessage());
            return "{\"error\":\"参数获取失败\"}";
        }
    }

    private static boolean ignoreArgs(Object object) {
        if (object == null) return false;
        Class<?> clazz = object.getClass();
        if (clazz.isArray()) {
            return IntStream.range(0, Array.getLength(object))
                    .anyMatch(i -> ignoreArgs(Array.get(object, i)));
        }
        if (Collection.class.isAssignableFrom(clazz)) {
            return ((Collection<?>) object).stream().anyMatch(OperateLogAspect::ignoreArgs);
        }
        if (Map.class.isAssignableFrom(clazz)) {
            return ignoreArgs(((Map<?, ?>) object).values());
        }
        return object instanceof MultipartFile
                || object instanceof HttpServletRequest
                || object instanceof HttpServletResponse
                || object instanceof BindingResult;
    }

    private static <T extends Annotation> T getMethodAnnotation(ProceedingJoinPoint jp, Class<T> c) {
        return ((MethodSignature) jp.getSignature()).getMethod().getAnnotation(c);
    }
    private static <T extends Annotation> T getClassAnnotation(ProceedingJoinPoint jp, Class<T> c) {
        return ((MethodSignature) jp.getSignature()).getMethod().getDeclaringClass().getAnnotation(c);
    }

    private String buildErrorMessage(Exception exception) {
        if (exception == null) return "未知错误";
        try {
            StringBuilder sb = new StringBuilder();
            sb.append("异常类型: ").append(exception.getClass().getSimpleName());
            if (exception.getMessage() != null) sb.append(", 错误信息: ").append(exception.getMessage());
            StackTraceElement[] st = exception.getStackTrace();
            if (st != null && st.length > 0) {
                StackTraceElement top = st[0];
                sb.append(", 位置: ")
                        .append(top.getClassName()).append(".").append(top.getMethodName())
                        .append("(").append(top.getFileName()).append(":").append(top.getLineNumber()).append(")");
            }
            String s = sb.toString();
            return s.length() > 1000 ? s.substring(0, 997) + "..." : s;
        } catch (Exception e) {
            return "错误信息构建失败: " + (exception.getMessage() != null ? exception.getMessage() : "未知错误");
        }
    }
}
