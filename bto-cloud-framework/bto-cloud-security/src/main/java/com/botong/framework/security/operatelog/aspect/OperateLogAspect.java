package com.botong.framework.security.operatelog.aspect;

import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.botong.framework.common.utils.AddressUtils;
import com.botong.framework.common.utils.HttpContextUtils;
import com.botong.framework.common.utils.IpUtils;
import com.botong.framework.common.utils.JsonUtils;
import com.botong.framework.security.operatelog.annotations.OperateLog;
import com.botong.framework.security.operatelog.dto.OperateLogDTO;
import com.botong.framework.security.operatelog.service.OperateLogService;
import com.botong.framework.security.user.SecurityUser;
import com.botong.framework.security.user.UserDetail;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;
import org.springframework.validation.BindingResult;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.lang.annotation.Annotation;
import java.lang.reflect.Array;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.Map;
import java.util.function.Predicate;
import java.util.stream.IntStream;

/**
 * 操作日志，切面处理类
 */
@Aspect
@Component
@AllArgsConstructor
@Slf4j
public class OperateLogAspect {
    private final OperateLogService operateLogService;

    @Around("@annotation(operateLog)")
    public Object around(ProceedingJoinPoint joinPoint, OperateLog operateLog) throws Throwable {
        // 记录开始时间
        LocalDateTime startTime = LocalDateTime.now();
        try {
            //执行方法
            Object result = joinPoint.proceed();

            //保存日志
            saveLog(joinPoint, operateLog, startTime, 1, "success", null);

            return result;
        } catch (Exception e) {
            //保存日志
            saveLog(joinPoint, operateLog, startTime, 0, null, e);
            throw e;
        }
    }


    private void saveLog(ProceedingJoinPoint joinPoint, OperateLog operateLog, LocalDateTime startTime, Integer status, String successResponse, Exception exception) {
        try {
            OperateLogDTO log = new OperateLogDTO();

            // 执行时长
            long duration = LocalDateTimeUtil.between(startTime, LocalDateTime.now()).toMillis();
            log.setDuration((int) duration);
            // 用户信息
            UserDetail user = SecurityUser.getUser();
            if (user != null) {
                log.setUserId(user.getId());
                log.setRealName(user.getRealName());
            }
            // 操作类型
            log.setOperateType(operateLog.type()[0].getValue());
            // 设置module值
            log.setModule(operateLog.module());
            // 设置name值
            log.setName(operateLog.name());

        // 如果没有指定module值，则从tag读取
        if (StrUtil.isBlank(log.getModule())) {
            Tag tag = getClassAnnotation(joinPoint, Tag.class);
            if (tag != null) {
                log.setModule(tag.name());
            }
        }

        // 如果没有指定name值，则从operation读取
        if (StrUtil.isBlank(log.getName())) {
            Operation operation = getMethodAnnotation(joinPoint, Operation.class);
            if (operation != null) {
                log.setName(operation.summary());
            }
        }

        // 请求相关
        HttpServletRequest request = HttpContextUtils.getHttpServletRequest();
        if (request != null) {
            log.setIp(IpUtils.getIpAddr(request));
            log.setAddress(AddressUtils.getAddressByIP(log.getIp()));
            log.setUserAgent(request.getHeader(HttpHeaders.USER_AGENT));
            log.setReqUri(request.getRequestURI());
            log.setReqMethod(request.getMethod());
        }

        log.setReqParams(obtainMethodArgs(joinPoint));
        log.setStatus(status);

            // 设置响应信息
            if (status == 1) {
                // 成功时：只保存 "success" 字符串
                log.setResultMsg(successResponse != null ? successResponse : "success");
            } else {
                // 失败时：保存详细的错误信息
                log.setResultMsg(buildErrorMessage(exception));
            }

            // 保存操作日志
            operateLogService.saveLog(log);
        } catch (Exception e) {
            this.log.error("操作日志处理失败: {}", e.getMessage(), e);
        }
    }

    private String obtainMethodArgs(ProceedingJoinPoint joinPoint) {
        try {
            MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
            String[] argNames = methodSignature.getParameterNames();
            Object[] argValues = joinPoint.getArgs();

            // 增加空值检查
            if (argNames == null || argValues == null) {
                return "{}";
            }

            // 拼接参数
            Map<String, Object> args = MapUtil.newHashMap(argValues.length);
            for (int i = 0; i < argNames.length && i < argValues.length; i++) {
                String argName = argNames[i];
                Object argValue = argValues[i];
                // 增加参数名空值检查
                if (argName != null) {
                    args.put(argName, ignoreArgs(argValue) ? "[ignore]" : argValue);
                }
            }

            return JsonUtils.toJsonString(args);
        } catch (Exception e) {
            // 如果参数处理失败，返回错误信息而不是抛出异常
            log.warn("获取方法参数失败: {}", e.getMessage());
            return "{\"error\": \"参数获取失败\"}";
        }
    }

    private static boolean ignoreArgs(Object object) {
        // 增加空值检查，避免 NullPointerException
        if (object == null) {
            return false; // null 值不需要忽略，可以正常记录
        }

        Class<?> clazz = object.getClass();

        // 处理数组
        if (clazz.isArray()) {
            return IntStream.range(0, Array.getLength(object))
                    .anyMatch(index -> ignoreArgs(Array.get(object, index)));
        }

        // 处理集合
        if (Collection.class.isAssignableFrom(clazz)) {
            return ((Collection<?>) object).stream()
                    .anyMatch((Predicate<Object>) OperateLogAspect::ignoreArgs);
        }

        // 处理Map
        if (Map.class.isAssignableFrom(clazz)) {
            return ignoreArgs(((Map<?, ?>) object).values());
        }

        return object instanceof MultipartFile
                || object instanceof HttpServletRequest
                || object instanceof HttpServletResponse
                || object instanceof BindingResult;
    }

    private static <T extends Annotation> T getMethodAnnotation(ProceedingJoinPoint joinPoint, Class<T> annotationClass) {
        return ((MethodSignature) joinPoint.getSignature()).getMethod().getAnnotation(annotationClass);
    }

    private static <T extends Annotation> T getClassAnnotation(ProceedingJoinPoint joinPoint, Class<T> annotationClass) {
        return ((MethodSignature) joinPoint.getSignature()).getMethod().getDeclaringClass().getAnnotation(annotationClass);
    }

    /**
     * 构建错误信息
     * @param exception 异常对象
     * @return 错误信息字符串
     */
    private String buildErrorMessage(Exception exception) {
        if (exception == null) {
            return "未知错误";
        }

        try {
            StringBuilder errorMsg = new StringBuilder();

            // 异常类型
            errorMsg.append("异常类型: ").append(exception.getClass().getSimpleName());

            // 异常消息
            if (exception.getMessage() != null) {
                errorMsg.append(", 错误信息: ").append(exception.getMessage());
            }

            // 堆栈信息 - 取第一条，方便定位
            StackTraceElement[] stackTrace = exception.getStackTrace();
            if (stackTrace != null && stackTrace.length > 0) {
                StackTraceElement top = stackTrace[0];
                errorMsg.append(", 位置: ")
                        .append(top.getClassName()) // 类全名
                        .append(".")
                        .append(top.getMethodName()) // 方法名
                        .append("(")
                        .append(top.getFileName())   // 文件名
                        .append(":")
                        .append(top.getLineNumber()) // 行号
                        .append(")");
            }

            // 如果想要完整堆栈，可以追加全部
            // for (StackTraceElement element : stackTrace) {
            //     errorMsg.append("\n\tat ").append(element);
            // }

            // 特定业务异常标记
            if (exception instanceof org.springframework.web.client.RestClientException) {
                errorMsg.append(" [Feign调用异常]");
            } else if (exception instanceof com.fasterxml.jackson.databind.JsonMappingException) {
                errorMsg.append(" [JSON解析异常]");
            } else if (exception instanceof java.sql.SQLException) {
                errorMsg.append(" [数据库异常]");
            }

            // 限制长度
            String result = errorMsg.toString();
            if (result.length() > 1000) {
                result = result.substring(0, 997) + "...";
            }

            return result;
        } catch (Exception e) {
            return "错误信息构建失败: " + (exception.getMessage() != null ? exception.getMessage() : "未知错误");
        }
    }
}