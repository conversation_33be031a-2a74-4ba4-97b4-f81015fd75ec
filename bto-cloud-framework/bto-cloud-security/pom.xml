<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<parent>
		<groupId>com.botong</groupId>
		<artifactId>bto-cloud-framework</artifactId>
		<version>${revision}</version>
	</parent>
	<modelVersion>4.0.0</modelVersion>
	<artifactId>bto-cloud-security</artifactId>
	<packaging>jar</packaging>

	<dependencies>
		<dependency>
			<groupId>com.botong</groupId>
			<artifactId>bto-cloud-common</artifactId>
			<version>${revision}</version>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-security</artifactId>
		</dependency>
    </dependencies>
</project>