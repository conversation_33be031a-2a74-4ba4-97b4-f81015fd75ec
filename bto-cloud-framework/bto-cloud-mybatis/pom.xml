<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<parent>
		<groupId>com.botong</groupId>
		<artifactId>bto-cloud-framework</artifactId>
		<version>${revision}</version>
	</parent>
	<modelVersion>4.0.0</modelVersion>
	<artifactId>bto-cloud-mybatis</artifactId>
	<packaging>jar</packaging>

	<dependencies>
		<dependency>
			<groupId>com.github.yulichang</groupId>
			<artifactId>mybatis-plus-join-boot-starter</artifactId>
			<version>1.5.2</version>
		</dependency>
		<dependency>
			<groupId>com.botong</groupId>
			<artifactId>bto-cloud-security</artifactId>
			<version>${revision}</version>
		</dependency>
		<dependency>
			<groupId>com.baomidou</groupId>
			<artifactId>mybatis-plus-boot-starter</artifactId>
		</dependency>
		<dependency>
			<groupId>com.mysql</groupId>
			<artifactId>mysql-connector-j</artifactId>
		</dependency>
		<dependency>
			<groupId>com.dameng</groupId>
			<artifactId>DmJdbcDriver18</artifactId>
		</dependency>
		<dependency>
			<groupId>org.postgresql</groupId>
			<artifactId>postgresql</artifactId>
		</dependency>
	</dependencies>
</project>