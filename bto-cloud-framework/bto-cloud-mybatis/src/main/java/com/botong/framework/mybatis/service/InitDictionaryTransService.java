package com.botong.framework.mybatis.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.botong.framework.mybatis.dao.InitDictDataDao;
import com.botong.framework.mybatis.dao.InitDictTypeDao;
import com.botong.framework.mybatis.entity.system.SysDictDataEntity;
import com.botong.framework.mybatis.entity.system.SysDictTypeEntity;
import com.botong.framework.mybatis.service.impl.BaseServiceImpl;
import com.fhs.trans.service.impl.DictionaryTransService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <AUTHOR> by zhb on 2023/8/21.
 */
@Service
@AllArgsConstructor
public class InitDictionaryTransService extends BaseServiceImpl<InitDictTypeDao, SysDictTypeEntity> {
    private final InitDictDataDao initDictDataDao;
    private final DictionaryTransService dictionaryTransService;

    public void refreshTransCache() {
        // 异步不阻塞主线程，不会 增加启动用时
        CompletableFuture.supplyAsync(() -> {
            // 获取所有的字典项数据
            List<SysDictDataEntity> dataList = initDictDataDao.selectList(new LambdaQueryWrapper<>());
            // 根据类型分组
            Map<Long, List<SysDictDataEntity>> dictTypeDataMap = dataList.stream().collect(Collectors
                    .groupingBy(SysDictDataEntity::getDictTypeId));
            List<SysDictTypeEntity> dictTypeEntities = super.list();
            for (SysDictTypeEntity dictTypeEntity : dictTypeEntities) {
                if (dictTypeDataMap.containsKey(dictTypeEntity.getId())) {
                    dictionaryTransService.refreshCache(dictTypeEntity.getDictType(), dictTypeDataMap.get(dictTypeEntity.getId())
                            .stream().collect(Collectors.toMap(SysDictDataEntity::getDictValue, SysDictDataEntity::getDictLabel)));
                }
            }
            return null;
        });
    }
}