package com.botong.framework.common.cache;

import com.botong.framework.common.utils.StringUtils;
import org.springframework.data.redis.cache.RedisCache;
import org.springframework.data.redis.cache.RedisCacheConfiguration;
import org.springframework.data.redis.cache.RedisCacheWriter;

import java.time.Duration;

/**
 * redis管理，自定义过期时间
 * <AUTHOR> by zhb on 2023/8/17.
 */
public class RedisCacheManager extends org.springframework.data.redis.cache.RedisCacheManager {

    public RedisCacheManager(RedisCacheWriter cacheWriter, RedisCacheConfiguration defaultCacheConfiguration) {
        super(cacheWriter, defaultCacheConfiguration);
    }

    /**
     * 重写createRedisCache方法，并进行优化
     *
     * @param name        原name作为redis存储键名，重写的name通过"#"拼接过期时间
     *                    - 如果没有"#"则默认不设置过期时间
     *                    - 第一个"#"后面是过期时间，第二个"#"后面是时间单位
     *                    - 时间单位使用d(天)、h(小时)、m(分钟)、s(秒)，默认为h(小时)
     * @param cacheConfig Redis缓存配置
     * @return RedisCache对象
     */
    @Override
    protected RedisCache createRedisCache(String name, RedisCacheConfiguration cacheConfig) {
        if (StringUtils.isNotEmpty(name) && name.contains("#")) {
            String[] redisInfo = name.split("#");
            String cacheName = redisInfo[0];
            int expire = Integer.parseInt(redisInfo[1]);
            // 过期时间，默认为h
            Duration duration = Duration.ofHours(expire);
            // 判断是否存在时间单位
            if (redisInfo.length == 3) {
                switch (redisInfo[2]) {
                    case "d":
                        duration = Duration.ofDays(expire);
                        break;
                    case "m":
                        duration = Duration.ofMinutes(expire);
                        break;
                    case "s":
                        duration = Duration.ofSeconds(expire);
                        break;
                }
            }
            return super.createRedisCache(cacheName, cacheConfig.entryTtl(duration));
        }
        return super.createRedisCache(name, cacheConfig);
    }
}