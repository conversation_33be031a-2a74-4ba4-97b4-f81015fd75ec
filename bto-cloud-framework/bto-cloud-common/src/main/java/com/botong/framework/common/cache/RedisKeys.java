package com.botong.framework.common.cache;

/**
 * Redis Key管理
 */
public class RedisKeys {

    public final static String WORK_ORDER_PREFIX = "workOrder";

    public final static String PLANT_PREFIX = "plant";

    public final static String TIMEOUT_PREFIX = "timeout";

    public final static String COLON = ":";

    public final static String YUEXIU = "yuexiu";

    /**
     * 验证码Key
     */
    public static String getCaptchaKey(String key) {
        return "sys:captcha:" + key;
    }

    /**
     * accessToken Key
     */
    public static String getAccessTokenKey(String accessToken) {
        return "sys:access:" + accessToken;
    }

    public static String getYxAccessTokenKey() {
        return YUEXIU + COLON + "token";
    }

    public static String getYxResponseKey() {
        return YUEXIU + COLON + "response";
    }

    public static String getYxBillUpdateTimeKey() {
        return YUEXIU + COLON + "billUpdateTime";
    }

    public static String getPlantKey(String key) {
        return PLANT_PREFIX + COLON + key;
    }

    public static String getTimeoutWarningThresholdKey(String key) {
        return WORK_ORDER_PREFIX + COLON + TIMEOUT_PREFIX + COLON + key;
    }

    public static String getLogKey() {
        return "sys:log";
    }

}