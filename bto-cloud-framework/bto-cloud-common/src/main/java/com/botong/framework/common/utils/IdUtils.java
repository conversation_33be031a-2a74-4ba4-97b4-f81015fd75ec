package com.botong.framework.common.utils;

import cn.hutool.core.lang.UUID;

import java.text.SimpleDateFormat;

/**
 * 唯一ID工具
 * <AUTHOR> by zhb on 2023/10/25.
 */
public class IdUtils {

    private IdUtils() {
    }

    public static String randomUuid() {
        return UUID.randomUUID().toString();
    }

    public static String simpleUuid() {
        return UUID.randomUUID().toString(true);
    }

    public static String fastUuid() {
        return UUID.fastUUID().toString();
    }

    public static String fastSimpleUuid() {
        return UUID.fastUUID().toString(true);
    }

    /**
     * 根据时间戳生成唯一id
     */
    public static String combineTimestamps() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmssSSSS");
        return sdf.format(System.currentTimeMillis());
    }

    public static String fastUuidWithPrefix(String prefix) {
        if (StringUtils.isBlank(prefix)) {
            throw new IllegalArgumentException("invalid prefix");
        } else {
            return prefix + fastUuid();
        }
    }

    public static String timestampIdWithPrefix(String prefix) {
        if (StringUtils.isBlank(prefix)) {
            throw new IllegalArgumentException("invalid prefix");
        } else {
            return prefix + combineTimestamps();
        }
    }

}