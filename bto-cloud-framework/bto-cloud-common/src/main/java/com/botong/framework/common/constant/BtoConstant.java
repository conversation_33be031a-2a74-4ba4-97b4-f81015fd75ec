package com.botong.framework.common.constant;

/**
 * 系统全局常量
 */
public interface BtoConstant {
    /**
     * 根节点标识
     */
    Long ROOT = 0L;
    /**
     * 当前页码
     */
    String PAGE = "page";
    /**
     * 数据权限
     */
    String DATA_SCOPE = "dataScope";
    /**
     * 超级管理员
     */
    Integer SUPER_ADMIN = 1;
    /**
     * 禁用
     */
    Integer DISABLE = 0;
    /**
     * 启用
     */
    Integer ENABLE = 1;
    /**
     * 失败
     */
    Integer FAIL = 0;
    /**
     * 成功
     */
    Integer SUCCESS = 1;
    /**
     * OK
     */
    String OK = "OK";

    /**
     * 定时发送短信任务name
     */
    String SMS_JOB_NAME = "smsJob";

    /**
     * 字典标签未知
     */
    String UNKNOWN_REGION = "其他";

    /**
     * 光伏系统feign接口调用统一前缀
     */
    String FEIGN_INTERFACE_PREFIX = "/api";


    /**
     * 数据范围全部数据
     */
    Integer DATA_SCOPE_ALL = 0;

    /**
     * 数据字典电站类型key
     */
    String DICT_PLANT_TYPE_KEY = "plant_type";
    String RESPONSIBLE_NAME = "负责人";

}