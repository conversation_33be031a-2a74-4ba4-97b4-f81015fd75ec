package com.botong.framework.common.utils;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.builder.ExcelWriterBuilder;
import com.alibaba.excel.write.builder.ExcelWriterSheetBuilder;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.botong.framework.common.excel.ExcelDataListener;
import com.botong.framework.common.excel.ExcelFinishCallBack;
import com.botong.framework.common.exception.ServerException;
import com.fhs.common.utils.ConverterUtils;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.util.ReflectUtils;
import com.fhs.core.trans.vo.TransPojo;
import com.fhs.trans.service.impl.DictionaryTransService;
import lombok.SneakyThrows;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.lang.reflect.Field;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;

/**
 * The type Excel utils.
 * {@link <a href="https://easyexcel.opensource.alibaba.com/"></a>}
 *
 * <AUTHOR>
 */
public class ExcelUtils {

    public static final String EXPORT = "export";
    public static final String IMPORT = "import";

    public static void monthlyOpsWorkOrderSummary(HttpServletResponse response, String excelName, List<Map<String, Object>> data) {
        if (data == null || data.isEmpty()) {
            throw new RuntimeException("数据为空，无法导出");
        }

        // **打印数据，确认数据正确**
        // System.out.println("导出数据：" + JSON.toJSONString(data, SerializerFeature.PrettyFormat));

        // **获取所有列名（保证顺序）**
        Set<String> allKeys = data.stream()
                .flatMap(map -> map.keySet().stream())
                .collect(Collectors.toCollection(LinkedHashSet::new));

        // **调整列顺序：先“城市”，再其他字段，最后“合计”**
        List<String> headList = new ArrayList<>();

        if (allKeys.contains("城市")){
            headList.add("城市");
        }
        for (String key : allKeys) {
            if (!"城市".equals(key) && !"合计".equals(key)) {
                headList.add(key);
            }
        }
        headList.add("合计");

        // **构建表头**
        List<List<String>> headTitles = headList.stream()
                .map(Collections::singletonList)
                .collect(Collectors.toList());

        // **转换数据**
        List<List<Object>> dataList = new ArrayList<>();
        for (Map<String, Object> row : data) {
            List<Object> rowData = new ArrayList<>();
            for (String key : headList) {
                Object value = row.getOrDefault(key, "");
                if (value instanceof Date) {
                    value = DateUtil.formatDateTime((Date) value); // 格式化日期
                }
                rowData.add(value);
            }
            dataList.add(rowData);
        }

        try {
            // **设置 HTTP 头**
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("UTF-8");
            // 这里URLEncoder.encode可以防止中文乱码 当然和easy excel没有关系
            String fileName = URLUtil.encode(excelName).replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName+System.currentTimeMillis() + ".xlsx");
            // response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(excelName + ".xlsx", "UTF-8"));

            // **写入 Excel**
            EasyExcel.write(response.getOutputStream())
                    .head(headTitles)
                    .sheet("统计数据")
                    .doWrite(dataList);

            response.flushBuffer(); // 确保数据写入
        } catch (IOException e) {
            throw new RuntimeException("Excel 导出失败", e);
        }
    }


    /**
     * 读取excel文件
     *
     * @param <T>      数据类型
     * @param file     excel文件
     * @param head     列名
     * @param callBack 回调 导入时传入定义好的回调接口，excel数据解析完毕之后监听器将数据传入回调函数
     *                 这样调用工具类时可以通过回调函数获取导入的数据，如果数据量过大可根据实际情况进行分配入库
     */
    public static <T> void readAnalysis(MultipartFile file, Class<T> head, ExcelFinishCallBack<T> callBack) {
        try {
            // headRowNumber设置多行头情况
            EasyExcel.read(file.getInputStream(), head, new ExcelDataListener<>(callBack)).sheet().headRowNumber(2).doRead();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    public static <T> void readAnalysis(MultipartFile file, int headRow, Class<T> head, ExcelFinishCallBack<T> callBack) {
        if (headRow <= 0) {
            throw new ServerException("表头设置错误");
        }
        try {
            // headRowNumber设置多行头情况
            EasyExcel.read(file.getInputStream(), head, new ExcelDataListener<>(callBack)).sheet().headRowNumber(headRow).doRead();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }


    /**
     * 读取excel文件
     *
     * @param <T>      数据类型
     * @param file     excel文件
     * @param head     列名
     * @param callBack 回调 导入时传入定义好的回调接口，excel数据解析完毕之后监听器将数据传入回调函数
     *                 这样调用工具类时可以通过回调函数获取导入的数据，如果数据量过大可根据实际情况进行分配入库
     */
    public static <T> void readAnalysis(File file, Class<T> head, ExcelFinishCallBack<T> callBack) {
        try {
            EasyExcel.read(new FileInputStream(file), head, new ExcelDataListener<>(callBack)).sheet().doRead();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * 读取excel文件 同步
     *
     * @param <T>   数据类型
     * @param file  文件
     * @param clazz 模板类
     * @return java.util.List list
     */
    public static <T> List<T> readSync(File file, Class<T> clazz) {
        return readSync(file, clazz, 1, 0, ExcelTypeEnum.XLSX);
    }

    /**
     * 读取excel文件 同步
     *
     * @param <T>       数据类型
     * @param file      文件
     * @param clazz     模板类
     * @param rowNum    数据开始行 1
     * @param sheetNo   第几张表
     * @param excelType 数据表格式类型
     * @return java.util.List list
     */
    public static <T> List<T> readSync(File file, Class<T> clazz, Integer rowNum, Integer sheetNo, ExcelTypeEnum excelType) {
        return EasyExcel.read(file).headRowNumber(rowNum).excelType(excelType).head(clazz).sheet(sheetNo).doReadSync();
    }


    /**
     * 导出数据到文件
     *
     * @param <T>  数据类型
     * @param head 类名
     * @param file 导入到文件
     * @param data 数据
     */
    public static <T> void excelExport(Class<T> head, File file, List<T> data) {
        excelExport(head, file, "sheet1", data);
    }

    /**
     * 导出数据到文件
     *
     * @param <T>       写入格式
     * @param head      类名
     * @param file      写入到文件
     * @param sheetName sheet名称
     * @param data      数据列表
     */
    public static <T> void excelExport(Class<T> head, File file, String sheetName, List<T> data) {
        try {
            EasyExcel.write(file, head).sheet(sheetName).doWrite(data);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 导出数据到web
     * 文件下载（失败了会返回一个有部分数据的Excel）
     *
     * @param head      类名
     * @param excelName excel名字
     * @param sheetName sheet名称
     * @param data      数据
     */
    public static <T> void excelExport(Class<T> head, String excelName, String sheetName, List<T> data) {
        try {
            // 内容策略
            WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
            // 设置 水平居中
            contentWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
            HorizontalCellStyleStrategy horizontalCellStyleStrategy = new HorizontalCellStyleStrategy(null, contentWriteCellStyle);

            HttpServletResponse response = HttpContextUtils.getHttpServletResponse();
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            // 这里URLEncoder.encode可以防止中文乱码 当然和easy excel没有关系
            String fileName = URLUtil.encode(excelName).replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            EasyExcel.write(response.getOutputStream(), head).registerWriteHandler(horizontalCellStyleStrategy).sheet(StringUtils.isBlank(sheetName) ? "sheet1" : sheetName).doWrite(data);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 解析字典数据到字段上
     * 比如 T中有 genderLabel字段 为男 需要给 gender 字段自动设置为0
     *
     * @param dataList 需要被反向解析的数据
     */
    @SneakyThrows
    public static <T extends TransPojo> void parseDict(List<T> dataList) {
        // 没有数据就不需要初始化
        if (CollectionUtil.isEmpty(dataList)) {
            return;
        }
        Class<? extends TransPojo> clazz = dataList.get(0).getClass();
        // 拿到所有需要反向翻译的字段
        List<Field> fields = ReflectUtils.getAnnotationField(clazz, Trans.class);
        // 过滤出字典翻译
        fields = fields.stream().filter(field -> TransType.DICTIONARY.equals(field.getAnnotation(Trans.class).type())).collect(Collectors.toList());
        DictionaryTransService dictionaryTransService = SpringUtil.getBean(DictionaryTransService.class);
        for (T data : dataList) {
            for (Field field : fields) {
                Trans trans = field.getAnnotation(Trans.class);
                // key不能为空并且ref不为空的才自动处理
                if (StrUtil.isAllNotBlank(trans.key(), trans.ref())) {
                    Field ref = ReflectUtils.getDeclaredField(clazz, trans.ref());
                    ref.setAccessible(true);
                    // 获取字典反向值
                    String value = dictionaryTransService.getUnTransMap().get(trans.key() + "_" + ref.get(data));
                    if (StringUtils.isBlank(value)) {
                        continue;
                    }
                    // 一般目标字段是int或者string字段 后面有添加单独抽离方法
                    if (Integer.class.equals(field.getType())) {
                        field.setAccessible(true);
                        field.set(data, ConverterUtils.toInteger(value));
                    } else {
                        field.setAccessible(true);
                        field.set(data, ConverterUtils.toString(value));
                    }
                }
            }
        }

    }

}