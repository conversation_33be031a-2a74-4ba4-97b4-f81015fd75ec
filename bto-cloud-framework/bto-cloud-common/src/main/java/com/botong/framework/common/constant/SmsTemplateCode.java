package com.botong.framework.common.constant;

/**
 * 短信验证码模板常量
 * <AUTHOR> by zhb on 2023/9/16.
 */

public interface SmsTemplateCode {

    /**
     * 工单管理系统超时通知
     */
    String TIMEOUT_NOTICE_CODE = "SMS_463515122";

    /**
     * 工单管理系统派单通知
     */
    String DISPATCH_NOTICE_CODE = "SMS_463160519";

    /**
     * 工单管理系统派单客户通知
     */
    String CUSTOMER_NOTICE_CODE = "SMS_463682669";

    /**
     * 开放平台接口异常通知
     */
    String INTERFACE_EXCEPTION_NOTICE_CODE = "SMS_464031597";

}