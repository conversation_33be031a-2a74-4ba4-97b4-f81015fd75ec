package com.botong.framework.common.query;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * 查询公共参数
 */
@Data
public class Query {
    @NotNull(message = "页码不能为空")
    @Min(value = 1, message = "页码最小值为 1")
    @Schema(description = "当前页码", required = true)
    Integer page;

    @NotNull(message = "每页条数不能为空")
    @Range(min = 1, max = 200, message = "每页条数，取值范围 1-200")
    @Schema(description = "每页条数", required = true)
    Integer limit;

    @Schema(description = "排序字段 (数据库字段 eg: up_work_time,create_time)")
    String order;

    @Schema(description = "是否升序 true->是 false->否")
    boolean asc;
}