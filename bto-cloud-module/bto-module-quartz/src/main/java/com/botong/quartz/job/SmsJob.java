package com.botong.quartz.job;

import com.botong.api.module.message.SmsApi;
import com.botong.framework.common.constant.SmsTemplateCode;
import com.botong.framework.common.utils.StringUtils;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.quartz.Job;
import org.quartz.JobDataMap;
import org.quartz.JobExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 定时短信
 * <AUTHOR> by zhb on 2023/9/15.
 */
@Data
@Slf4j
@NoArgsConstructor
@Component
public class SmsJob implements Job {

    @Autowired
    private SmsApi smsApi;

    @Override
    public void execute(JobExecutionContext context) {
        log.info("开始发送超时短信");
        JobDataMap jobDataMap = context.getJobDetail().getJobDataMap();
        String mobile = jobDataMap.getString("mobile");

        Map<String, String> params = new HashMap<>();
        String workId = jobDataMap.getString("workId");
        params.put("workId", workId);
        params.put("maintainers", jobDataMap.getString("maintainers"));
        params.put("timeout", jobDataMap.getString("timeout"));

        // 手机号码不为空
        if (StringUtils.isNotBlank(mobile)) {
            smsApi.sendByTemplateCode(SmsTemplateCode.TIMEOUT_NOTICE_CODE, mobile, params);
        } else {
            log.warn("工单Id：" + workId + "派单人手机号码为空，超时提醒失败");
        }
    }
}