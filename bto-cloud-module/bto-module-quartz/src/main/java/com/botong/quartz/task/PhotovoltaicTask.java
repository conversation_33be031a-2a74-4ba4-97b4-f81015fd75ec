package com.botong.quartz.task;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.botong.api.module.photovoltaic.PhotovoltaicApi;
import com.botong.api.module.photovoltaic.dto.PlantQueryDTO;
import com.botong.api.module.photovoltaic.vo.PlantInfoVO;
import com.botong.api.module.utlis.RequestPhotovoltaicUtil;
import com.botong.api.module.workorder.PlantApi;
import com.botong.framework.common.utils.JsonUtils;
import com.botong.quartz.thread.SaveDataRunnable;
import com.botong.quartz.thread.ThreadLocalTransactionManager;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 定时获取光伏系统电站数据
 * <AUTHOR> by zhb on 2023/9/11.
 */
@Slf4j
@Service
@AllArgsConstructor
public class PhotovoltaicTask {

    private final PhotovoltaicApi photovoltaicApi;
    private final ThreadLocalTransactionManager transactionManager;
    private final PlantApi plantApi;

    /**
     * 定时任务一天一次
     * 请求光伏系统新增电站数据
     */
    public void requestPlantList() {
        PlantQueryDTO plantQueryDTO = new PlantQueryDTO();
        DateTime yesterday = DateUtil.yesterday();
        plantQueryDTO.setStartCreateTime(DateUtil.beginOfDay(yesterday).toString());
        plantQueryDTO.setEndCreateTime(DateUtil.endOfDay(yesterday).toString());
        savePlantList(plantQueryDTO, 1);
    }

    /**
     * 初始化光伏系统电站数据
     */
    public void initializePlantList() {
        savePlantList(new PlantQueryDTO(), 10);
    }

    /**
     * 保存光伏系统电站数据
     */
    public void savePlantList(PlantQueryDTO plantQueryDTO, Integer threadPoolNumber) {
        try {
            HashMap<String, Object> pageData = getPlantList(plantQueryDTO);
            long total = Long.parseLong(pageData.get("total").toString());
            long pages = Long.parseLong(pageData.get("pages").toString());
            // 创建线程池
            ExecutorService executor = Executors.newFixedThreadPool(threadPoolNumber);
            if (total > plantQueryDTO.getPageSize()) {
                for (int i = 1; i <= pages; i++) {
                    plantQueryDTO.setCurrentPage(i);
                    HashMap<String, Object> result = getPlantList(plantQueryDTO);
                    List<PlantInfoVO> list = JsonUtils.parseArray(result.get("list").toString(), PlantInfoVO.class);
                    // 提交任务至线程池
                    executor.execute(new SaveDataRunnable(transactionManager, plantApi, list));
                }
            } else {
                List<PlantInfoVO> list = JsonUtils.parseArray(pageData.get("list").toString(), PlantInfoVO.class);
                // 提交任务至线程池
                executor.execute(new SaveDataRunnable(transactionManager, plantApi, list));
            }
            executor.shutdown();
        } catch (Exception e) {
            log.error(e.getMessage());
        }
    }


    /**
     * 查询光伏系统电站数据
     */
    public HashMap<String, Object> getPlantList(PlantQueryDTO query) {
        try {
            Object result = photovoltaicApi.getPlantList(query);
            return RequestPhotovoltaicUtil.getPageData(result);
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return null;
    }
}