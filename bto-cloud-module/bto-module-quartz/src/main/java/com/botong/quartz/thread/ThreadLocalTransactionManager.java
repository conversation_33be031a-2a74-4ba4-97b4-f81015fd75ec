package com.botong.quartz.thread;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;

/**
 * 通过ThreadLocal维护当前线程中的事务状态
 * <AUTHOR>
 */
@Component
public class ThreadLocalTransactionManager {
    private static ThreadLocal<TransactionStatus> currentTransaction = new ThreadLocal<>();

    @Autowired
    private PlatformTransactionManager transactionManager;

    public void begin() {
        TransactionDefinition definition = new DefaultTransactionDefinition();
        TransactionStatus status = transactionManager.getTransaction(definition);
        currentTransaction.set(status);
    }

    public void commit() {
        TransactionStatus status = currentTransaction.get();
        transactionManager.commit(status);
        currentTransaction.remove();
    }

    public void rollback() {
        TransactionStatus status = currentTransaction.get();
        status.setRollbackOnly();
        transactionManager.rollback(status);
        currentTransaction.remove();
    }

}