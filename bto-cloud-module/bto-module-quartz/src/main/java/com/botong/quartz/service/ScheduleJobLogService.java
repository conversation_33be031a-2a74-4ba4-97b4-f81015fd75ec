package com.botong.quartz.service;

import com.botong.framework.common.page.PageResult;
import com.botong.framework.mybatis.service.BaseService;
import com.botong.quartz.entity.ScheduleJobLogEntity;
import com.botong.quartz.query.ScheduleJobLogQuery;
import com.botong.quartz.vo.ScheduleJobLogVO;

/**
 * 定时任务日志
 */
public interface ScheduleJobLogService extends BaseService<ScheduleJobLogEntity> {

    PageResult<ScheduleJobLogVO> page(ScheduleJobLogQuery query);

    void scheduleJobLogErrorSendSms();

}