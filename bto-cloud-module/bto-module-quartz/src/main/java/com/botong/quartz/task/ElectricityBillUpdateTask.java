package com.botong.quartz.task;

import com.botong.api.module.system.ElectricityBillApi;
import com.botong.framework.common.utils.Result;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 越秀电费账单定时更新任务
 * 定时调用越秀电费账单更新接口，每月6号、16号、26号执行
 *
 * <AUTHOR>
 * @since 2025-01-17
 */
@Slf4j
@Service
@AllArgsConstructor
public class ElectricityBillUpdateTask {

    private final ElectricityBillApi electricityBillApi;

    /**
     * 定时更新越秀电费账单数据
     * 执行时间：每月6号、16号、26号的凌晨2点执行
     * Cron表达式：0 0 2 6,16,26 * ?
     *
     * @param params 任务参数（可选）
     */
    public void run(String params) {
        log.info("开始执行越秀电费账单定时更新任务，参数：{}", params);

        try {
            log.info("正在调用越秀电费账单更新接口...");

            // 调用越秀电费账单更新接口
            Result<String> result = electricityBillApi.updateYxBill();

            log.info("接口调用完成，返回结果 - code: {}, msg: {}, data: {}",
                    result != null ? result.getCode() : "null",
                    result != null ? result.getMsg() : "null",
                    result != null ? result.getData() : "null");

            if (result != null && result.getCode() == 0) {
                log.info("越秀电费账单更新任务执行成功，结果：{}", result.getData());
            } else {
                String errorMsg = result != null ? result.getMsg() : "接口返回结果为空";
                String errorCode = result != null ? String.valueOf(result.getCode()) : "unknown";
                log.error("越秀电费账单更新任务业务执行失败，错误码：{}，错误信息：{}", errorCode, errorMsg);
                // 记录错误但不抛出异常，避免影响定时任务调度
                return;
            }

        } catch (RuntimeException e) {
            log.error("越秀电费账单更新任务运行时异常：{}", e.getMessage(), e);
            // 记录异常但不重新抛出，避免嵌套异常
            return;
        } catch (Exception e) {
            log.error("越秀电费账单更新任务执行异常，异常类型：{}，异常信息：{}",
                     e.getClass().getSimpleName(), e.getMessage(), e);

            // 查找根本原因
            Throwable rootCause = e;
            while (rootCause.getCause() != null) {
                rootCause = rootCause.getCause();
            }
            log.error("根本异常原因：{}，信息：{}", rootCause.getClass().getSimpleName(), rootCause.getMessage());
            return;
        }

        log.info("越秀电费账单定时更新任务执行完成");
    }
}
