package com.botong.quartz.thread;

import com.botong.api.module.photovoltaic.vo.PlantInfoVO;
import com.botong.api.module.workorder.PlantApi;
import com.botong.framework.common.exception.ServerException;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 手动提交事务进行保存数据
 * <AUTHOR> by zhb on 2023/9/12.
 */
@Slf4j
@Component
@AllArgsConstructor
public class SaveDataRunnable implements Runnable {

    private final ThreadLocalTransactionManager transactionManager;

    private final PlantApi plantApi;

    private final List<PlantInfoVO> list;


    @Override
    public void run() {
        // 开启事务
        transactionManager.begin();
        try {
            if (!list.isEmpty()) {
                plantApi.saveBatch(this.list);
            }
            // 事务提交
            transactionManager.commit();
        } catch (Exception e) {
            // 事务回滚
            transactionManager.rollback();
            throw new ServerException(e.getMessage());
        }
    }

}