package com.botong.quartz.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.botong.api.module.message.SmsApi;
import com.botong.framework.common.constant.SmsTemplateCode;
import com.botong.framework.common.page.PageResult;
import com.botong.framework.mybatis.service.impl.BaseServiceImpl;
import com.botong.quartz.convert.ScheduleJobLogConvert;
import com.botong.quartz.dao.ScheduleJobLogDao;
import com.botong.quartz.entity.ScheduleJobLogEntity;
import com.botong.quartz.query.ScheduleJobLogQuery;
import com.botong.quartz.service.ScheduleJobLogService;
import com.botong.quartz.vo.ScheduleJobLogVO;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.HashMap;

/**
 * 定时任务日志
 */
@Service
@AllArgsConstructor
public class ScheduleJobLogServiceImpl extends BaseServiceImpl<ScheduleJobLogDao, ScheduleJobLogEntity> implements ScheduleJobLogService {

    private final SmsApi smsApi;

    @Override
    public PageResult<ScheduleJobLogVO> page(ScheduleJobLogQuery query) {
        IPage<ScheduleJobLogEntity> page = baseMapper.selectPage(getPage(query), getWrapper(query));

        return new PageResult<>(ScheduleJobLogConvert.INSTANCE.convertList(page.getRecords()), page.getTotal());
    }

    private LambdaQueryWrapper<ScheduleJobLogEntity> getWrapper(ScheduleJobLogQuery query) {
        LambdaQueryWrapper<ScheduleJobLogEntity> wrapper = Wrappers.lambdaQuery();
        wrapper.like(StrUtil.isNotBlank(query.getJobName()), ScheduleJobLogEntity::getJobName, query.getJobName());
        wrapper.like(StrUtil.isNotBlank(query.getJobGroup()), ScheduleJobLogEntity::getJobGroup, query.getJobGroup());
        wrapper.eq(query.getJobId() != null, ScheduleJobLogEntity::getJobId, query.getJobId());
        wrapper.orderByDesc(ScheduleJobLogEntity::getId);
        return wrapper;
    }

    public void scheduleJobLogErrorSendSms() {
        HashMap<String, String> map = new HashMap<>();
        map.put("errorMessage", "请求开放平台接口出错");
        smsApi.sendByTemplateCode(SmsTemplateCode.INTERFACE_EXCEPTION_NOTICE_CODE, "17875776464", map);
    }


}