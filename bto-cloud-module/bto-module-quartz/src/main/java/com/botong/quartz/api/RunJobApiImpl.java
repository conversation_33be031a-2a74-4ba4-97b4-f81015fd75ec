package com.botong.quartz.api;

import com.botong.api.module.quartz.RunJobApi;
import com.botong.api.module.quartz.dto.RunSmsJobDTO;
import com.botong.framework.common.constant.BtoConstant;
import com.botong.quartz.job.SmsJob;
import lombok.AllArgsConstructor;
import org.quartz.*;
import org.springframework.web.bind.annotation.RestController;

import java.time.ZoneId;
import java.util.Date;

/**
 * 定时短信任务
 * <AUTHOR> by zhb on 2023/9/15.
 */
@RestController
@AllArgsConstructor
public class RunJobApiImpl implements RunJobApi {

    private final Scheduler scheduler;

    @Override
    public void runSmsJob(RunSmsJobDTO runSmsJobDTO) throws SchedulerException {
        String workId = runSmsJobDTO.getParams().get("workId");
        JobKey jobKey = new JobKey(BtoConstant.SMS_JOB_NAME, workId);
        // 判断是否存在指定标识的任务
        if (scheduler.checkExists(jobKey)) {
            this.deleteSmsJobByWorkId(workId);
        }
        JobDataMap jobDataMap = new JobDataMap();
        jobDataMap.putAll(runSmsJobDTO.getParams());
        JobDetail job = JobBuilder.newJob(SmsJob.class)
                .storeDurably()
                .withIdentity(jobKey)
                .usingJobData(jobDataMap)
                .build();

        Trigger trigger = TriggerBuilder.newTrigger()
                .startAt(Date.from(runSmsJobDTO.getScheduledTime().atZone(ZoneId.systemDefault()).toInstant()))
                .forJob(job)
                .build();
        scheduler.scheduleJob(job, trigger);
    }

    @Override
    public void deleteSmsJobByWorkId(String workId) throws SchedulerException {
        JobKey jobKey = new JobKey(BtoConstant.SMS_JOB_NAME, workId);
        scheduler.deleteJob(jobKey);
    }
}