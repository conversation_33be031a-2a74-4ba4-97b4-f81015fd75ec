package com.botong.quartz.task;

import com.alibaba.fastjson.JSONObject;
import com.botong.api.module.message.SmsApi;
import com.botong.api.module.open.OpenApi;
import com.botong.api.module.utlis.RequestPhotovoltaicUtil;
import com.botong.framework.common.constant.SmsTemplateCode;
import com.botong.framework.common.exception.ServerException;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Objects;

/**
 * 开放平台接口稳定性测试
 * <AUTHOR>
 */
@Slf4j
@Service
@AllArgsConstructor
public class OpenRequestTask {

    private OpenApi openApi;
    private SmsApi smsApi;


    public void run(String params) {
        ArrayList<String> list = new ArrayList<>();
        String message = "请求开放平台接口出错";
        // 责任人联系电话
        list.add("17875776464");
        Object one = openApi.getPlantList();
        Object two = openApi.getPlantList();
        try {
            Object result = openApi.getPlantList();
            if (Objects.nonNull(result)) {
                JSONObject jsonObject = RequestPhotovoltaicUtil.objectToJsonObject(result);
                int code = (int) jsonObject.get("code");
                String msg = (String) jsonObject.get("message");
                if (code != 2000 || !"请求成功".equals(msg)) {
                    throw new ServerException(message);

                }
            }
        } catch (Exception e) {
            log.error(message);
            HashMap<String, String> map = new HashMap<>();
            map.put("errorMessage", message);
            list.forEach(phone -> {
                smsApi.sendByTemplateCode(SmsTemplateCode.INTERFACE_EXCEPTION_NOTICE_CODE, phone, map);
            });
        }
        log.info("定时执行成功");
    }
}