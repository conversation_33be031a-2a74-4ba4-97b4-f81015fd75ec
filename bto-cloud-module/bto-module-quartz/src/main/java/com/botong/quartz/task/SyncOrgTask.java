package com.botong.quartz.task;

import cn.hutool.core.util.StrUtil;
import com.botong.api.module.system.SysOrgApi;
import com.botong.api.module.workorder.PlantApi;
import com.botong.api.module.workorder.entity.OrgBaseEntity;
import com.botong.framework.common.cache.RedisCache;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> by zhb on 2025/1/13.
 */
@Slf4j
@Service
@AllArgsConstructor
public class SyncOrgTask {

    private final RedisCache redisCache;
    private final PlantApi plantApi;
    private final SysOrgApi sysOrgApi;

    public void run(String params) {
        List<OrgBaseEntity> redisList = (List<OrgBaseEntity>) redisCache.get("dict");
        List<OrgBaseEntity> allProject = plantApi.getAllProject();
        if (Objects.isNull(redisList)) {
            redisCache.set("dict", allProject);
            return;
        }
        List<OrgBaseEntity> orgList = sysOrgApi.getAllOrg();
        for (OrgBaseEntity project : allProject) {
            boolean nameExistsInRedis = redisList.stream().anyMatch(redisEntity -> redisEntity.getName().equals(project.getName()));
            boolean idExistsInRedis = redisList.stream().anyMatch(redisEntity -> redisEntity.getId().equals(project.getId()));
            if (!nameExistsInRedis && !idExistsInRedis) {
                // 新增逻辑
                String pid = project.getPid();
                String parentName = findParentName(pid, redisList);
                String orgId = findOrgIdByName(parentName, orgList);
                if (orgId != null) {
                    System.out.println("需要新增的元素pid为：" + orgId);
                    //     执行添加操作
                    sysOrgApi.addOrg(project.getName(), orgId);
                }
            } else {
                // 更新逻辑
                OrgBaseEntity redisEntity = redisList.stream()
                        .filter(entity -> entity.getId().equals(project.getId()))
                        .findFirst()
                        .orElse(null);
                if (redisEntity != null && !redisEntity.getName().equals(project.getName())) {
                    redisList.remove(redisEntity);
                    String orgId = findOrgIdByName(redisEntity.getName(), orgList);
                    if (orgId != null) {
                        System.out.println("需要修改的orgId为：" + orgId + "新的名字为：" + project.getName());
                        //     执行修改操作
                        sysOrgApi.updateNameById(orgId, project.getName());
                    }
                }
            }
            boolean existsDuplicate = redisList.stream().anyMatch(redisEntity ->
                    redisEntity.getId().equals(project.getId()) &&
                            redisEntity.getName().equals(project.getName()) &&
                            redisEntity.getPid().equals(project.getPid())
            );

            if (!existsDuplicate) {
                redisList.add(project);
            }
        }
        redisCache.set("dict", redisList);
    //     todo 光云删除组织同步工单删除组织
    }

    // 递归查找父级名称
    private String findParentName(String pid, List<OrgBaseEntity> list) {
        for (OrgBaseEntity entity : list) {
            if (entity.getId().equals(pid)) {
                String name = entity.getName();
                if (StrUtil.isNotBlank(name)) {
                    System.out.println("需要新增的父级名称为：" + entity.getName());
                    return entity.getName();
                } else {
                    // 如果没有找到，查找父级的pid
                    return findParentName(entity.getPid(), list);
                }
            }
        }
        return null;
    }

    // 根据名称查找orgList中的元素
    private String findOrgIdByName(String name, List<OrgBaseEntity> orgList) {
        for (OrgBaseEntity org : orgList) {
            if (org.getName().equals(name)) {
                return org.getId();
            }
        }
        return null;
    }


}
