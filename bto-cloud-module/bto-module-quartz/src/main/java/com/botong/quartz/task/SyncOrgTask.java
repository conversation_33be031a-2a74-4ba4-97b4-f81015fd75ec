package com.botong.quartz.task;

import cn.hutool.core.util.StrUtil;
import com.botong.api.module.system.SysOrgApi;
import com.botong.api.module.workorder.PlantApi;
import com.botong.api.module.workorder.entity.OrgBaseEntity;
import com.botong.framework.common.cache.RedisCache;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 保证 allProject 的 name 层级在 orgList 中一一存在（按 name 对齐）
 * 1) 处理重命名：通过 Redis 快照(项目id->上次名称)发现改名，然后 org 里按旧名改为新名
 * 2) 处理新增：从根到叶，若 org 中不存在该 name，则在父 org 下新增
 */
@Slf4j
@Service
@AllArgsConstructor
public class SyncOrgTask {

    private static final String SNAPSHOT_KEY = "project-name-snapshot"; // Redis: Map<String projectId, String lastName>
    private static final String ORG_ROOT_ID = "0"; // org 顶层父 id

    private final RedisCache redisCache;
    private final PlantApi plantApi;
    private final SysOrgApi sysOrgApi;

    public void run(String params) {
        // 1) 拉取当前数据
        List<OrgBaseEntity> allProject = plantApi.getAllProject();
        List<OrgBaseEntity> orgList = sysOrgApi.getAllOrg();

        // 2) 建索引
        Map<String, OrgBaseEntity> projectById = allProject.stream()
                .collect(Collectors.toMap(OrgBaseEntity::getId, e -> e, (a, b) -> a));
        Map<String, String> orgNameToId = orgList.stream()
                .collect(Collectors.toMap(OrgBaseEntity::getName, OrgBaseEntity::getId, (a, b) -> a));

        // 3) 从 Redis 取历史快照（用于检测重命名）
        @SuppressWarnings("unchecked")
        Map<String, String> lastNameByProjectId = (Map<String, String>) redisCache.get(SNAPSHOT_KEY);
        if (lastNameByProjectId == null) lastNameByProjectId = new HashMap<>();

        // 4) 先处理“重命名”（保证父子查找时名字一致）
        for (OrgBaseEntity p : allProject) {
            String pid = p.getId();
            String currentName = p.getName();
            String lastName = lastNameByProjectId.get(pid);
            if (StrUtil.isNotBlank(lastName) && !Objects.equals(lastName, currentName)) {
                // 按旧名在 org 中找到对应节点并改名
                String orgId = orgNameToId.get(lastName);
                if (StrUtil.isNotBlank(orgId)) {
                    try {
                        sysOrgApi.updateNameById(orgId, currentName);
                        // 同步本地映射
                        orgNameToId.remove(lastName);
                        orgNameToId.put(currentName, orgId);
                        log.info("Org rename: {} -> {} (orgId={})", lastName, currentName, orgId);
                    } catch (Exception e) {
                        log.error("Rename org failed: {} -> {}, cause: {}", lastName, currentName, e.getMessage(), e);
                    }
                } else {
                    // 找不到旧名：可能之前没建/被手工改动；交给后续“确保存在”逻辑补齐
                    log.warn("Old name not found in org for rename, oldName={}, newName={}", lastName, currentName);
                }
            }
        }

        // 5) 自顶向下，确保每个项目节点（按 name）在 org 中存在于正确父节点下
        //    这里用递归 + 记忆化，保证父先建
        Map<String, String> ensuredOrgIdByProjectId = new HashMap<>();

        for (OrgBaseEntity p : allProject) {
            ensureOrgNodeExists(p.getId(), projectById, orgNameToId, ensuredOrgIdByProjectId);
        }

        // 6) 更新快照（仅存项目 id -> 当前名称）
        Map<String, String> newSnapshot = allProject.stream()
                .collect(Collectors.toMap(OrgBaseEntity::getId, OrgBaseEntity::getName, (a, b) -> a));
        redisCache.set(SNAPSHOT_KEY, newSnapshot);

        // done
        log.info("SyncOrgTask finished. projects={}, orgsNow={}", allProject.size(), orgNameToId.size());
    }

    /**
     * 确保指定项目 id 的节点（按 name）在 org 中存在，若不存在则在其父 org 下创建
     * 返回该节点在 org 中的 id
     */
    private String ensureOrgNodeExists(String projectId,
                                       Map<String, OrgBaseEntity> projectById,
                                       Map<String, String> orgNameToId,
                                       Map<String, String> ensuredOrgIdByProjectId) {

        // 记忆化：同一轮避免重复递归
        if (ensuredOrgIdByProjectId.containsKey(projectId)) {
            return ensuredOrgIdByProjectId.get(projectId);
        }

        OrgBaseEntity p = projectById.get(projectId);
        if (p == null) {
            log.warn("Project not found by id: {}", projectId);
            return null;
        }

        // 若 org 已有同名节点，直接返回
        String orgId = orgNameToId.get(p.getName());
        if (StrUtil.isNotBlank(orgId)) {
            ensuredOrgIdByProjectId.put(projectId, orgId);
            return orgId;
        }

        // 计算父 orgId：项目根(pid=-1)映射到 org 根("0")
        String parentOrgId;
        String pid = p.getPid();
        if (StrUtil.isBlank(pid) || "-1".equals(pid)) {
            parentOrgId = ORG_ROOT_ID;
        } else {
            OrgBaseEntity parentProject = projectById.get(pid);
            if (parentProject == null) {
                // 异常：项目树断裂，降级挂在根下
                log.warn("Parent project missing. child={}, pid={}. Fallback to root.", p.getName(), pid);
                parentOrgId = ORG_ROOT_ID;
            } else {
                parentOrgId = ensureOrgNodeExists(parentProject.getId(), projectById, orgNameToId, ensuredOrgIdByProjectId);
                if (StrUtil.isBlank(parentOrgId)) {
                    // 父找不到/创建失败也挂根，避免全链失败
                    log.warn("Parent orgId missing for parentProject={}, fallback to root.", parentProject.getName());
                    parentOrgId = ORG_ROOT_ID;
                }
            }
        }

        // 在父 org 下创建当前节点
        String newOrgId = null;
        try {
            // 建议让 addOrg 返回新建的 orgId；若返回值为 void/boolean，则用“重拉 + 按名查找”兜底
            newOrgId = sysOrgApi.addOrg(p.getName(), parentOrgId);
        } catch (NoSuchMethodError | UnsupportedOperationException e) {
            // 兼容老接口：先调用，再重拉
            log.info("sysOrgApi.addOrg likely returns void/boolean, will refetch org list to resolve id. cause={}", e.getMessage());
            sysOrgApi.addOrg(p.getName(), parentOrgId);
        } catch (Exception e) {
            log.error("Add org failed for name={}, parentOrgId={}, cause={}", p.getName(), parentOrgId, e.getMessage(), e);
        }

        if (StrUtil.isBlank(newOrgId)) {
            // 兜底：重拉一次 orgList 再按名称取 id
            List<OrgBaseEntity> freshOrgs = sysOrgApi.getAllOrg();
            newOrgId = freshOrgs.stream()
                    .filter(o -> p.getName().equals(o.getName()))
                    .map(OrgBaseEntity::getId)
                    .findFirst()
                    .orElse(null);
        }

        if (StrUtil.isNotBlank(newOrgId)) {
            orgNameToId.put(p.getName(), newOrgId);            // 立刻刷新本地映射，后续子节点可用
            ensuredOrgIdByProjectId.put(projectId, newOrgId);  // 记忆化
            log.info("Org created: name={}, parentOrgId={}, orgId={}", p.getName(), parentOrgId, newOrgId);
        } else {
            log.error("Failed to resolve new orgId for name={}, parentOrgId={}", p.getName(), parentOrgId);
        }
        return newOrgId;
    }
}
