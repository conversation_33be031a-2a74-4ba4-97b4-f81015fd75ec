package com.botong.quartz.service;

import com.botong.framework.common.page.PageResult;
import com.botong.framework.mybatis.service.BaseService;
import com.botong.quartz.entity.ScheduleJobEntity;
import com.botong.quartz.query.ScheduleJobQuery;
import com.botong.quartz.vo.ScheduleJobVO;

import java.util.List;

/**
 * 定时任务
 */
public interface ScheduleJobService extends BaseService<ScheduleJobEntity> {

    PageResult<ScheduleJobVO> page(ScheduleJobQuery query);

    void save(ScheduleJobVO vo);

    void update(ScheduleJobVO vo);

    void delete(List<Long> idList);

    void run(ScheduleJobVO vo);

    void changeStatus(ScheduleJobVO vo);
}