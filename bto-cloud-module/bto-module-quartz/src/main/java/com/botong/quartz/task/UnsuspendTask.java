package com.botong.quartz.task;

import com.botong.api.module.photovoltaic.PhotovoltaicApi;
import com.botong.api.module.workorder.FlowApi;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2025/1/14 10:59
 */
@Slf4j
@Service
@AllArgsConstructor
public class UnsuspendTask {

    private final FlowApi flowApi;


    public void run(String params) {
        flowApi.unsuspend();
    }

}
