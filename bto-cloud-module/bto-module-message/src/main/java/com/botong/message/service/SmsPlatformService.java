package com.botong.message.service;

import com.botong.framework.common.page.PageResult;
import com.botong.framework.mybatis.service.BaseService;
import com.botong.message.entity.SmsPlatformEntity;
import com.botong.message.query.SmsPlatformQuery;
import com.botong.message.sms.config.SmsConfig;
import com.botong.message.vo.SmsPlatformVO;

import java.util.List;

/**
 * 短信平台
 */
public interface SmsPlatformService extends BaseService<SmsPlatformEntity> {

    PageResult<SmsPlatformVO> page(SmsPlatformQuery query);

    /**
     * 启用的短信平台列表
     */
    List<SmsConfig> listByEnable();

    void save(SmsPlatformVO vo);

    void update(SmsPlatformVO vo);

    void delete(List<Long> idList);

    SmsConfig getSmsConfigByTemplateCode(String templateCode);

}