server:
  port: 58083

spring:
  application:
    name: bto-module-message
  profiles:
    active: dev
  cloud:
    nacos:
      discovery:
        server-addr: ${nacos_host:127.0.0.1}:${nacos_port:8848}
        # 命名空间，默认：public
        namespace: ${nacos_namespace:}
        service: ${spring.application.name}
      config:
        server-addr: ${spring.cloud.nacos.discovery.server-addr}
        namespace: ${spring.cloud.nacos.discovery.namespace}
        file-extension: yaml
        # 指定配置
        extension-configs:
          - data-id: datasource.yaml
            refresh: true
          - data-id: common.yaml
            refresh: true