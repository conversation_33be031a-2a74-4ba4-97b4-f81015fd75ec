package com.botong.system.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 字典数据来源
 */
@Getter
@AllArgsConstructor
public enum UserRemarkEnum {
    /**
     * 字典数据
     */
    REPAIR(0,"维修"),
    /**
     * 动态SQL
     */
    MAINTAIN(1,"运维");

    private final int value;
    private final String remark;
    public static String getNameByValue(int value) {
        for (UserRemarkEnum s : UserRemarkEnum.values()) {
            if (s.getValue() == value) {
                return s.getRemark();
            }
        }
        return "";
    }

    public static Integer getValueByName(String name) {
        for (UserRemarkEnum s : UserRemarkEnum.values()) {
            if (Objects.equals(s.getRemark(), name)) {
                return s.getValue();
            }
        }
        return null;
    }
}
