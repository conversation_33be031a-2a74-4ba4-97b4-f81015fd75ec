package com.botong.system.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.botong.framework.mybatis.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 附件分类管理
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-03-04
 */
@EqualsAndHashCode(callSuper=false)
@Data
@TableName("sys_attachment_type")
public class SysAttachmentTypeEntity extends BaseEntity {

	/**
	* 分类名称
	*/
	private String name;

	/**
	 * 备注
	 */
	private String remark;







}