package com.botong.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 运维费单价
 *
 * <AUTHOR>
 * @since 1.0.0 2025-01-09
 */

@Data
@TableName("bill_price_maintenance")
public class BillPriceMaintenanceEntity {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 运维费方案ID
     */
    private String schemeId;

    /**
     * 开始时间
     */
    private Date stageStart;

    /**
     * 结束时间
     */
    private Date stageEnd;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

}