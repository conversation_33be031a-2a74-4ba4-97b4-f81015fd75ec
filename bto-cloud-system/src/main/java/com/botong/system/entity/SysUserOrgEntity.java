package com.botong.system.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.botong.framework.mybatis.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 用户机构关系
 * <AUTHOR> by zhb on 2023/9/19.
 */
@EqualsAndHashCode(callSuper = false)
@Data
@TableName("sys_user_org")
public class SysUserOrgEntity extends BaseEntity {

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 菜单ID
     */
    private Long orgId;


}