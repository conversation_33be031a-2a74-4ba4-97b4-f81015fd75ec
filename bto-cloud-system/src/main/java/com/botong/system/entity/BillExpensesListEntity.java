package com.botong.system.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 运维费账单
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-01-09
 */

@Data
@TableName("bill_expenses_list")
public class BillExpensesListEntity {
	/**
	* 电站ID
	*/
	@TableId
	private String plantUid;

	/**
	* 年份
	*/
	private Date closingYear;

	/**
	* 一月
	*/
	private String january;

	/**
	* 二月
	*/
	private String february;

	/**
	* 三月
	*/
	private String march;

	/**
	* 四月
	*/
	private String april;

	/**
	* 五月
	*/
	private String may;

	/**
	* 六月
	*/
	private String june;

	/**
	* 七月
	*/
	private String july;

	/**
	* 八月
	*/
	private String august;

	/**
	* 九月
	*/
	private String september;

	/**
	* 十月
	*/
	private String october;

	/**
	* 十一月
	*/
	private String november;

	/**
	* 十二月
	*/
	private String december;

	/**
	* 创建时间
	*/
	private Date createTime;

	/**
	* 更新时间
	*/
	private Date updateTime;

}