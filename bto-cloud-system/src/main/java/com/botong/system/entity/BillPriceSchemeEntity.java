package com.botong.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 运维费用方案表
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-01-11
 */

@Data
@TableName("bill_price_scheme")
public class BillPriceSchemeEntity {
	/**
	* 主键ID
	*/
	@TableId(type = IdType.AUTO)
	private Integer id;

	/**
	* 运维方案ID
{
生成规则：资方+生效日期（YUEXIU20250110）
}
	*/
	private String schemeId;

	/**
	* 资方ID
{
越秀：YUEXIU,
能发：ZSGY,
南网：NW

}
	*/
	private String investorId;

	/**
	* 运维方案开始阶段
	*/
	private Integer stageFrom;

	/**
	* 运维方案结束阶段
	*/
	private Integer stageTo;

	/**
	* 运维单价(元/年/W)
	*/
	private BigDecimal price;

	/**
	* 省份
	*/
	private String province;

	/**
	* 市/州
	*/
	private String city;

	/**
	* 逻辑删除
	*/
	private Integer isDeleted;

	/**
	* 创建时间
	*/
	private Date createTime;

	/**
	* 更新时间
	*/
	private Date updateTime;

}