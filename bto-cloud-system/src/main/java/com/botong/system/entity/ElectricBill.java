package com.botong.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Objects;

/**
 * <AUTHOR> by 2022 on 2024/12/25.
 */

@TableName("bill_yx_electricity_summary")
public class ElectricBill {
    @TableId(value = "bill_id", type = IdType.INPUT)
    @TableField(value = "bill_id"   )
    // 账单记录ID
    private String bill_id;
    // 厂商名称
    @TableField("manufacturer_name")
    private String manufacturer_name;
    // 发电户号
    @TableField("power_generation_no")
    private String power_generation_no;
    // 电费渠道
    @TableField("source")
    private String source;
    // 电费金额
    @TableField("amount")
    private BigDecimal amount;
    // 账单日期
    @TableField("bill_time")
    private String bill_time;
    // 结算日期
    @TableField("payment_time")
    private String payment_time;
    // 电站编号
    @TableField("partners_contract_number")
    private String partners_contract_number;
    // 工行二类卡卡号
    @TableField("bank_account_num_second")
    private String bank_account_num_second;
    // 数据日期
    @TableField("ds")
    private String ds;
    // 户名
    @TableField("medium_name")
    private String medium_name;
    // 越秀进件编号
    @TableField("entry_number")
    private String entry_number;
    // 退费后电费金额
    @TableField("amount_after_refund")
    private BigDecimal amount_after_refund;

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass()!= o.getClass()) {
            return false;
        }
        ElectricBill that = (ElectricBill) o;
        return Objects.equals(bill_id, that.bill_id) &&
                Objects.equals(manufacturer_name, that.manufacturer_name) &&
                Objects.equals(power_generation_no, that.power_generation_no) &&
                Objects.equals(source, that.source) &&
                amount.setScale(4, RoundingMode.HALF_UP).compareTo(that.amount.setScale(4, RoundingMode.HALF_UP)) == 0 &&
                Objects.equals(bill_time, that.bill_time) &&
                Objects.equals(payment_time, that.payment_time) &&
                Objects.equals(partners_contract_number, that.partners_contract_number) &&
                Objects.equals(bank_account_num_second, that.bank_account_num_second) &&
                Objects.equals(ds, that.ds) &&
                Objects.equals(medium_name, that.medium_name) &&
                Objects.equals(entry_number, that.entry_number);
    }

    public String getBill_id() {
        return bill_id;
    }

    public void setBill_id(String bill_id) {
        this.bill_id = bill_id;
    }

    public String getManufacturer_name() {
        return manufacturer_name;
    }

    public void setManufacturer_name(String manufacturer_name) {
        this.manufacturer_name = manufacturer_name;
    }

    public String getPower_generation_no() {
        return power_generation_no;
    }

    public void setPower_generation_no(String power_generation_no) {
        this.power_generation_no = power_generation_no;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public String getBill_time() {
        return bill_time;
    }

    public void setBill_time(String bill_time) {
        this.bill_time = bill_time;
    }

    public String getPayment_time() {
        return payment_time;
    }

    public void setPayment_time(String payment_time) {
        this.payment_time = payment_time;
    }

    public String getPartners_contract_number() {
        return partners_contract_number;
    }

    public void setPartners_contract_number(String partners_contract_number) {
        this.partners_contract_number = partners_contract_number;
    }

    public String getBank_account_num_second() {
        return bank_account_num_second;
    }

    public void setBank_account_num_second(String bank_account_num_second) {
        this.bank_account_num_second = bank_account_num_second;
    }

    public String getDs() {
        return ds;
    }

    public void setDs(String ds) {
        this.ds = ds;
    }

    public String getMedium_name() {
        return medium_name;
    }

    public void setMedium_name(String medium_name) {
        this.medium_name = medium_name;
    }

    public String getEntry_number() {
        return entry_number;
    }

    public void setEntry_number(String entry_number) {
        this.entry_number = entry_number;
    }

    public BigDecimal getAmount_after_refund() {
        return amount_after_refund;
    }

    public void setAmount_after_refund(BigDecimal amount_after_refund) {
        this.amount_after_refund = amount_after_refund;
    }

    @Override
    public int hashCode() {
        return Objects.hash(bill_id, manufacturer_name, power_generation_no, source, amount, bill_time, payment_time,
                partners_contract_number, bank_account_num_second, ds, medium_name, entry_number);
    }

}
