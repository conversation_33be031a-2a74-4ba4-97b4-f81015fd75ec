package com.botong.system.entity;

import                                                                         com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 越秀电费账单
 *
 * <AUTHOR> 
 * @since 1.0.0 2024-12-31
 */

@Data
@TableName("yx_electricity_bill_summary")
public class YxElectricityBillSummary {
	/**
	* ID
	*/
	@TableId
	private Integer id;

	/**
	* 账单记录ID
	*/
	private String billId;

	/**
	* 数据日期
	*/
	private Date dataDate;

	/**
	* 厂商
	*/
	private String manufacturerName;

	/**
	* 户名
	*/
	private String mediumName;

	/**
	* 电费渠道
1、国家电网
2、二类卡南方
3、二类卡非南方
4、项目公司备案
5、代收
6、其他
	*/
	private String source;

	/**
	* 发电户号
	*/
	private String powerGenerationNo;

	/**
	* 工行二类卡卡号
	*/
	private String bankAccountNumSecond;

	/**
	* 电站编号
	*/
	private String partnersContractNumber;

	/**
	* 越秀进件编号
	*/
	private String entryNumber;

	/**
	* 账单日期
	*/
	private Date billTime;

	/**
	* 结算日期
	*/
	private Date paymentTime;

	/**
	* 电费金额
	*/
	private BigDecimal amount;

	/**
	* 账单状态
	*/
	private String billStatus;

}