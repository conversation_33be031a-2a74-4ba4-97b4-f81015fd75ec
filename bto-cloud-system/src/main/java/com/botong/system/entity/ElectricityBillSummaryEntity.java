package com.botong.system.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 上网电量电费管理
 *
 * <AUTHOR>
 * @since 2024-12-24
 */

@Data
@TableName("bill_bto_electricity_summary")
public class ElectricityBillSummaryEntity {

    /**
     * 电厂（交易对象）编号
     */
    @TableId
    private String meterId;

    /**
     * 电厂（交易对象）名称
     */
    private String factoryName;

    /**
     * 结算时间段
     */
    private String settlementPeriod;

    /**
     * 卖电量（kWh）
     */
    private BigDecimal electricity;

    /**
     * 税率
     */
    private BigDecimal taxRate;

    /**
     * 单价
     */
    private BigDecimal electrovalence;

    /**
     * 不含税电费
     */
    private BigDecimal electricityWithoutTax;

    /**
     * 税金
     */
    private BigDecimal taxAmount;

    /**
     * 含税电费
     */
    private BigDecimal electricityWithTax;

    /**
     * 管理单位
     */
    private String administrator;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

}