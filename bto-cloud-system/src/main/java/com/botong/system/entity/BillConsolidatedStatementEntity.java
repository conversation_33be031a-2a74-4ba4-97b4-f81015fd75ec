package com.botong.system.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 电费账单
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-01-04
 */
@Data
@TableName("bill_consolidated_statement")
public class BillConsolidatedStatementEntity {
	/**
	* 电站uid
	*/
	private String plantUid;

	/**
	* 电量
	*/
	private BigDecimal electricity;

	/**
	* 结算日期(按月结算)
	*/
	private String settlementDate;

	/**
	* 税率
	*/
	private BigDecimal taxRate;

	/**
	* 税金
	*/
	private BigDecimal taxAmount;

	/**
	* 单价（不含税）
	*/
	private BigDecimal electrovalence;

	/**
	* 电费（含税）
	*/
	private BigDecimal electricityWithTax;

	/**
	* 电费（不含税）
	*/
	private BigDecimal electricityWithoutTax;

	/**
	* 管理单位
	*/
	private String administrator;

	/**
	* 越秀结算电费（含税）
	*/
	private BigDecimal electricityWithTaxYx;

	/**
	* 项目公司（优先级排序：供电局>越秀>博光）
	*/
	private String projectCompany;

	/**
	* 电表编号
	*/
	private String meterId;

	/**
	* 进件编号
	*/
	private String orderId;

	/**
	* 合同编号
	*/
	private String contractId;

	/**
	* 是否按月结算{
		按月结算：1
		不按月结算：？？（几个月一结就是几。例：{
		1、按季度结算第一季度：3
		2、按2、3月份结算：2
		}）
		}
	*/
	private Integer isMonth;

	/**
	 * 越秀博通是否一致：一致为0 不一致为1
	 */
	private Integer accord;


	/**
	* 创建时间
	*/
	private Date createTime;

	/**
	* 更新时间
	*/
	private Date updateTime;

}