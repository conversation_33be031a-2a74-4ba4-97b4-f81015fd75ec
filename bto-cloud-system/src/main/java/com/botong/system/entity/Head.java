package com.botong.system.entity;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class Head {

    /**
     * 系统标识
     * 标识接口对接双方，联系越秀申请，格式示例：YX-XX
     */
    private String systemId;

    /**
     * 流水号
     * 必须为未处理过的32位UUID，即32位16进制的数字，用分隔符分成8-4-4-4-12的格式
     */
    private String serialNumber;

    /**
     * 交易码
     * 以固定字符“YX”开头，拼接业务编码+环境。例如：测试环境进件接口为：YX_FINANCE_PROJECT_INFO_SIT
     * 测试环境：SIT   用户验收环境：UAT   生产环境：PROD
     * 业务编码见《接口清单》
     */
    private String transactionCode;
    /**
     * 交易时间
     * 发送时间，必须为yyyyMMdd HH:mm:ss的格式
     */
    private String transactionTime;

    /**
     * 版本号
     * 发送时间，必须为yyyyMMdd HH:mm:ss的格式
     */
    private int version;

    /**
     * 错误编码
     * 接口调用方非必传，返回时 01成功/02失败，发送可为空，返回不为空
     */
    private String errorCode;

    /**
     * 错误信息
     * 接口调用方非必传，返回错误消息
     */
    private String errorMessage;

}