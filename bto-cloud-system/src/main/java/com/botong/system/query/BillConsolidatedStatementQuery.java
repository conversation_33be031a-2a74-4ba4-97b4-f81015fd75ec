package com.botong.system.query;

import com.botong.framework.common.query.Query;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 电费账单查询
 *
 * <AUTHOR>
 * @since 1.0.0 2025-01-04
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Schema(description = "电费账单查询")
public class BillConsolidatedStatementQuery extends Query {

    @Schema(description = "电站名称")
    private String plantName;

    @Schema(description = "结算月份（格式：yyyy-MM）")
    private String settlementDate;

    @Schema(description = "管理单位")
    private String administrator;

    @Schema(description = "项目公司")
    private String projectCompany;

    @Schema(description = "电表编号")
    private String meterId;

    @Schema(description = "合同编号")
    private String contractId;

    @Schema(description = "进件编号")
    private String orderId;

    @Schema(description = "是否按月结算对应数据字典：settlement_month_type")
    private String settlementType;

    @Schema(description = "城市")
    private String city;

    @Schema(description = "账单校验")
    private String accord;

}