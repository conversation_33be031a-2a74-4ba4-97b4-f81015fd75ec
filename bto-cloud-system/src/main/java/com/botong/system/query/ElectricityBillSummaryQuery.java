package com.botong.system.query;

import com.botong.framework.common.query.Query;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 上网电量电费管理查询
 *
 * <AUTHOR>
 * @since 2024-12-24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Schema(description = "上网电量电费管理查询")
public class ElectricityBillSummaryQuery extends Query {

    @Schema(description = "电厂（交易对象）编号")
    private String meterId;

    @Schema(description = "电厂（交易对象）名称")
    private String factoryName;

    @Schema(description = "结算时间段")
    private String settlementPeriod;

    @Schema(description = "管理单位")
    private String administrator;
}