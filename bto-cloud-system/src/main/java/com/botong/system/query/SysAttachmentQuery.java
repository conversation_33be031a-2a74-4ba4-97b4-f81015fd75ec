package com.botong.system.query;

import com.botong.framework.common.query.Query;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 附件管理查询
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Schema(description = "附件管理查询")
public class SysAttachmentQuery extends Query {

    @Schema(description = "附件类型")
    private Long typeId;

    @Schema(description = "附件名称")
    private String name;

    @Schema(description = "存储平台")
    private String platform;

}