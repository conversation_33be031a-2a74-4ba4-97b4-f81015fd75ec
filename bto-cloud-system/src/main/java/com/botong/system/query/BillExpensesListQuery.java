package com.botong.system.query;

import com.botong.framework.common.query.Query;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
* 运维费账单查询
*
* <AUTHOR> 
* @since 1.0.0 2025-01-09
*/
@Data
@EqualsAndHashCode(callSuper = false)
@Schema(description = "运维费账单查询")
public class BillExpensesListQuery extends Query {

    @Schema(description = "电站名称")
    private String plantName;

    @Schema(description = "城市")
    private String city;

    @Schema(description = "开始年份（格式：yyyy）")
    private String startYear;

    @Schema(description = "结束年份（格式：yyyy）")
    private String endYear;

    @Schema(description = "电表编号")
    private String meterId;

    @Schema(description = "合同编号")
    private String contractId;

    @Schema(description = "进件编号")
    private String orderId;

}