package com.botong.system.controller;

import com.botong.framework.common.utils.Result;
import com.botong.framework.security.operatelog.annotations.OperateLog;
import com.botong.framework.security.operatelog.enums.OperateTypeEnum;
import com.botong.framework.security.utils.TokenUtils;
import com.botong.system.service.SysAuthService;
import com.botong.system.service.SysCaptchaService;
import com.botong.system.vo.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.hibernate.validator.constraints.Range;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * 认证管理
 */
@RestController
@RequestMapping("auth")
@Tag(name = "认证管理")
@Validated
@AllArgsConstructor
public class SysAuthController {
    private final SysCaptchaService sysCaptchaService;
    private final SysAuthService sysAuthService;

    @GetMapping("captcha")
    @Operation(summary = "验证码")
    public Result<SysCaptchaVO> captcha(@Range(min = 0, max = 1, message = "类型不正确") @Parameter(description = "响应类型：0->picture 1->number") Integer responseType) {
        SysCaptchaVO captchaVO = sysCaptchaService.generate(responseType);

        return Result.ok(captchaVO);
    }

    @PostMapping("login")
    @Operation(summary = "账号密码登录")
    public Result<SysUserTokenVO> login(@RequestBody SysAccountLoginVO login) {
        SysUserTokenVO token = sysAuthService.loginByAccount(login);

        return Result.ok(token);
    }

    @PostMapping("send/code")
    @Operation(summary = "发送短信验证码")
    @OperateLog(type = OperateTypeEnum.OTHER)
    public Result<String> sendCode(String mobile) {
        sysAuthService.sendCode(mobile);

        return Result.ok();
    }

    @PostMapping("mobile")
    @Operation(summary = "手机号登录")
    public Result<SysUserTokenVO> mobile(@RequestBody SysMobileLoginVO login) {
        SysUserTokenVO token = sysAuthService.loginByMobile(login);

        return Result.ok(token);
    }

    @PostMapping("token")
    @Operation(summary = "获取 accessToken")
    public Result<AccessTokenVO> token(String refreshToken) {
        AccessTokenVO token = sysAuthService.getAccessToken(refreshToken);

        return Result.ok(token);
    }

    @PostMapping("logout")
    @Operation(summary = "退出")
    @OperateLog(type = OperateTypeEnum.OTHER)
    public Result<String> logout(HttpServletRequest request) {
        sysAuthService.logout(TokenUtils.getAccessToken(request));

        return Result.ok();
    }
}
