package com.botong.system.controller;

import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONObject;
import com.botong.framework.common.cache.RedisCache;
import com.botong.framework.common.cache.RedisKeys;
import com.botong.framework.common.page.PageResult;
import com.botong.framework.common.utils.Result;
import com.botong.framework.security.operatelog.annotations.OperateLog;
import com.botong.framework.security.operatelog.enums.OperateTypeEnum;
import com.botong.system.constant.ElectricChargeConstant;
import com.botong.system.entity.BodyElement;
import com.botong.system.entity.RequestBody;
import com.botong.system.query.BillConsolidatedStatementQuery;
import com.botong.system.query.ElectricChargeQuery;
import com.botong.system.query.ElectricityBillSummaryQuery;
import com.botong.system.service.BillConsolidatedStatementService;
import com.botong.system.service.ElectricityBillSummaryService;
import com.botong.system.utils.ElectricChargeUtil;
import com.botong.system.vo.CombinedBillPageVO;
import com.botong.system.vo.ElectricityBillSummaryVO;
import com.google.gson.Gson;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 上网电量电费管理
 *
 * <AUTHOR>
 * @since 2024-12-24
 */
@RestController
@RequestMapping("system/electricity_bill")
@Tag(name = "上网电量电费管理")
@AllArgsConstructor
public class ElectricityBillSummaryController {

    private final ElectricityBillSummaryService electricityBillSummaryService;

    private final BillConsolidatedStatementService billConsolidatedStatementService;

    private final ElectricChargeUtil electricChargeUtil;

    private final RedisCache redisCache;

    @GetMapping("page")
    @Operation(summary = "博通电费账单分页")
    @PreAuthorize("hasAuthority('system:electricityBill:page')")
    public Result<PageResult<ElectricityBillSummaryVO>> page(@Valid ElectricityBillSummaryQuery query) {
        PageResult<ElectricityBillSummaryVO> page = electricityBillSummaryService.page(query);

        return Result.ok(page);
    }


    @PostMapping("import")
    @Operation(summary = "博通电费账单excel导入")
    @PreAuthorize("hasAuthority('system:electricityBill:import')")
    @OperateLog(type = OperateTypeEnum.IMPORT)
    public Result<String> importExcel(@RequestParam("file") MultipartFile file) {
        if (file.isEmpty()) {
            return Result.error("请选择需要上传的文件");
        }
        electricityBillSummaryService.importByExcel(file);

        return Result.ok();
    }

    @GetMapping("exportToExcel")
    @Operation(summary = "博通电费账单excel导出")
    @OperateLog(type = OperateTypeEnum.EXPORT)
    @PreAuthorize("hasAuthority('system:electricity_bill:export')")
    public void exportToExcel(@ParameterObject  @Valid ElectricityBillSummaryQuery query) {
        electricityBillSummaryService.exportToExcel(query);
    }


    @GetMapping("mergePage")
    @Operation(summary = "合并账单分页")
    @OperateLog(type = OperateTypeEnum.OTHER)
    @PreAuthorize("hasAuthority('system:mergeBill:page')")
    public Result<CombinedBillPageVO> page(@Valid BillConsolidatedStatementQuery query){
        CombinedBillPageVO combinedBillPageVO = billConsolidatedStatementService.page(query);
        return Result.ok(combinedBillPageVO);
    }

    @GetMapping("export")
    @Operation(summary = "导出合并账单")
    @OperateLog(type = OperateTypeEnum.EXPORT)
    @PreAuthorize("hasAuthority('system:mergeBill:export')")
    public void export(@Valid BillConsolidatedStatementQuery query) {
        billConsolidatedStatementService.export(query);
    }

    @PostMapping("updateYxBill")
    @Operation(summary = "更新越秀账单数据")
    @OperateLog(type = OperateTypeEnum.UPDATE)
    @PreAuthorize("hasAuthority('system:mergeBill:update')")
    public Result<String> updateYxBill() {
        // 一个月只允许更新一次，越秀每月5号滚动更新
        Date o = (Date) redisCache.get(RedisKeys.getYxBillUpdateTimeKey());
        if (o != null) {
            // 获取当前日期的年月
            LocalDate now = LocalDate.now();
            // 获取本月6号零点时间
            LocalDateTime month6thStart = LocalDateTime.of(now.getYear(), now.getMonth(), 6, 0, 0);
            Date month6thDate = Date.from(month6thStart.atZone(ZoneId.systemDefault()).toInstant());
            if (!o.before(month6thDate)) {
                return Result.ok("本月已更新，更新时间为：" + DateUtil.format(o, "yyyy-MM-dd HH:mm:ss"));
            }
        }
        // 获取token
        String token = electricChargeUtil.getToken();
        // 获取电费账单
        ElectricChargeQuery query = new ElectricChargeQuery();
        query.setInterfaceUrl(ElectricChargeConstant.ELECTRIC_CHARGE_URL);
        query.setUrlParam("?access_token=" + token);
        BodyElement bodyElement = new BodyElement();
        bodyElement.setFileType("electricChargeBill");
        bodyElement.setQueryType("bill_inc");

        List<BodyElement> bodyList = new ArrayList<>();
        bodyList.add(bodyElement);
        RequestBody requestBody = new RequestBody();
        requestBody.setBody(bodyList);

        requestBody.setHead(electricChargeUtil.getHeadParam());
        Gson gson = new Gson();
        query.setBodyParam(gson.toJson(requestBody));
        System.out.println(query);
        JSONObject request = electricChargeUtil.request(query);
        System.out.println(request);
        // 记录数据更新日期
        redisCache.set(RedisKeys.getYxBillUpdateTimeKey(), new Date());
        // 更新数据库
        billConsolidatedStatementService.updateDataBases();
        return Result.ok(Objects.isNull(request) ? "更新失败" : request.getStr("result"));
    }

}