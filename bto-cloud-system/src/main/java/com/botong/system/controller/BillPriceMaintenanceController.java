package com.botong.system.controller;

import com.botong.framework.common.page.PageResult;
import com.botong.framework.common.utils.Result;
import com.botong.framework.security.operatelog.annotations.OperateLog;
import com.botong.framework.security.operatelog.enums.OperateTypeEnum;
import com.botong.system.query.BillPriceMaintenanceQuery;
import com.botong.system.service.BillPriceMaintenanceService;
import com.botong.system.vo.BillPriceMaintenanceVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
* 运维费单价
*
* <AUTHOR> 
* @since 1.0.0 2025-01-09
*/
@RestController
@RequestMapping("system/maintenance")
@Tag(name="运维费单价")
@AllArgsConstructor
public class BillPriceMaintenanceController {
    private final BillPriceMaintenanceService billPriceMaintenanceService;

    @GetMapping("page")
    @Operation(summary = "分页")
    @PreAuthorize("hasAuthority('system:price:page')")
    public Result<PageResult<BillPriceMaintenanceVO>> page(@Valid BillPriceMaintenanceQuery query){
        PageResult<BillPriceMaintenanceVO> page = billPriceMaintenanceService.page(query);
        return Result.ok(page);
    }

    @PostMapping
    @Operation(summary = "保存")
    @PreAuthorize("hasAuthority('system:price:save')")
    @OperateLog(type = OperateTypeEnum.INSERT)
    public Result<String> save(@RequestBody BillPriceMaintenanceVO vo){
        billPriceMaintenanceService.save(vo);

        return Result.ok();
    }

    @PutMapping
    @Operation(summary = "修改")
    @PreAuthorize("hasAuthority('system:price:update')")
    @OperateLog(type = OperateTypeEnum.UPDATE)
    public Result<String> update(@RequestBody @Valid BillPriceMaintenanceVO vo){
        billPriceMaintenanceService.update(vo);

        return Result.ok();
    }

    @DeleteMapping
    @Operation(summary = "删除")
    @PreAuthorize("hasAuthority('system:price:delete')")
    @OperateLog(type = OperateTypeEnum.DELETE)
    public Result<String> delete(@RequestBody List<Long> idList){
        billPriceMaintenanceService.delete(idList);

        return Result.ok();
    }
}