package com.botong.system.vo;

import com.botong.framework.common.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
* 运维费账单
*
* <AUTHOR> 
* @since 1.0.0 2025-01-09
*/
@Data
@Schema(description = "运维费账单")
public class BillExpensesListVO implements Serializable {
	private static final long serialVersionUID = 1L;

	@Schema(description = "电站ID")
	private String plantUid;

	@Schema(description = "年份")
	@JsonFormat(pattern = "yyyy")
	private Date closingYear;

	@Schema(description = "一月")
	private String january;

	@Schema(description = "二月")
	private String february;

	@Schema(description = "三月")
	private String march;

	@Schema(description = "四月")
	private String april;

	@Schema(description = "五月")
	private String may;

	@Schema(description = "六月")
	private String june;

	@Schema(description = "七月")
	private String july;

	@Schema(description = "八月")
	private String august;

	@Schema(description = "九月")
	private String september;

	@Schema(description = "十月")
	private String october;

	@Schema(description = "十一月")
	private String november;

	@Schema(description = "十二月")
	private String december;

	@Schema(description = "更新时间")
	@JsonFormat(pattern = DateUtils.DATE_TIME_PATTERN)
	private Date updateTime;


}