package com.botong.system.vo;

import com.botong.framework.common.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR> by zhb on 2025/1/9.
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "运维费账单")
public class MaintenanceBillVO extends BillExpensesListVO implements Serializable {

    private static final long serialVersionUID = -1467970931735459397L;

    @Schema(description = "电站名称")
    private String plantName;

    @Schema(description = "装机容量")
    private BigDecimal plantCapacity;

    @Schema(description = "市/州")
    private String city;

    @Schema(description = "电表编号")
    private String meterId;

    @Schema(description = "合同编号")
    private String contractId;

    @Schema(description = "进件编号")
    private String orderId;

    @Schema(description = "博通合同编号")
    private String btoContractId;

    @Schema(description = "项目公司")
    private String projectCompany;

    @Schema(description = "并网日期")
    @JsonFormat(pattern = DateUtils.DATE_TIME_PATTERN)
    private Date createTime;

}
