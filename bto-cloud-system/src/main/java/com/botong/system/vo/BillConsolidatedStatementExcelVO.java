package com.botong.system.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.TransPojo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;

/**
* 电费账单
*
* <AUTHOR> 
* @since 1.0.0 2025-01-04
*/
@Data
@EqualsAndHashCode
@ContentRowHeight(25)
@HeadRowHeight(40)
@ColumnWidth(25)
public class BillConsolidatedStatementExcelVO implements Serializable, TransPojo {
	private static final long serialVersionUID = 1L;

	/**
	 * 本属性对于导出无用，只是用于翻译
	 */
	@ExcelIgnore
	private Long id;

	@ExcelProperty("电站名称")
	private String plantName;

	@ExcelProperty("并网日期")
	private String connectionDate;

	@ExcelProperty("装机容量")
	private String plantCapacity;

	@ExcelProperty("项目公司")
	private String projectCompany;

	@ExcelProperty("电表编号")
	private String meterId;

	@ExcelProperty("进件编号")
	private String orderId;

	@ExcelProperty("合同编号")
	private String contractId;

	@ExcelProperty("管理单位")
	private String administrator;

	@ExcelProperty("结算月份")
	private String settlementDate;

	@ExcelProperty("卖电量")
	private BigDecimal electricity;

	@ExcelProperty("含税单价")
	private BigDecimal electrovalence;

	@ExcelProperty("税率")
	private BigDecimal taxRate;

	@ExcelProperty("税金")
	private BigDecimal taxAmount;

	@ExcelProperty("不含税电费")
	private BigDecimal electricityWithoutTax;

	@ExcelProperty("含税电费")
	private BigDecimal electricityWithTax;

	@ExcelProperty("越秀结算含税电费")
	private BigDecimal electricityWithTaxYx;

	@ExcelIgnore
	@Trans(type = TransType.DICTIONARY, key = "settlement_month_type", ref = "isMonthLabel")
	private Integer isMonth;

	@ExcelProperty(value = "月度结算类型")
	private String isMonthLabel;

	@ExcelIgnore
	@Trans(type = TransType.DICTIONARY, key = "bill_verification", ref = "accordLabel")
	private Integer accord;

	@ExcelProperty(value = "账单校验")
	private String accordLabel;

}