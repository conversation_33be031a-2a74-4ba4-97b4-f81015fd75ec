package com.botong.system.vo;

/**
 * <AUTHOR>
 * @since 2024/12/24 17:24
 */

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.botong.framework.common.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * excel表
 */
@Data
@EqualsAndHashCode
@ContentRowHeight(25)
@HeadRowHeight(40)
@ColumnWidth(25)
public class ElectricityBillSummaryExcelVO {
    @ExcelProperty("电厂（交易对象）编号")
    private String meterId;

    @ExcelProperty("电厂（交易对象）名称")
    private String factoryName;

    @ExcelProperty("购电月份")
    private String settlementPeriod;

    @ExcelProperty("总电量（kWh）")
    private BigDecimal electricity;

    @ExcelProperty("税率")
    private String taxRate;

    @ExcelIgnore
    private BigDecimal electrovalence;

    @ExcelProperty("不含税电费")
    private BigDecimal electricityWithoutTax;

    @ExcelProperty("税金")
    private BigDecimal taxAmount;

    @ExcelProperty("含税电费")
    private BigDecimal electricityWithTax;

    @ExcelProperty("管理单位")
    private String administrator;
}
