package com.botong.system.vo;

import com.botong.framework.common.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 运维费单价
 *
 * <AUTHOR>
 * @since 1.0.0 2025-01-09
 */
@Data
@Schema(description = "运维费列表")
public class BillPriceMaintenanceVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(description = "主键ID")
    private Integer id;

    @Schema(description = "资方ID")
    private String investorId;

    @JsonIgnore
    private String schemeId;

    @Schema(description = "开始时间：yyyy-MM-dd")
    @JsonFormat(pattern = DateUtils.DATE_PATTERN)
    private Date stageStart;

    @Schema(description = "结束时间：yyyy-MM-dd")
    @JsonFormat(pattern = DateUtils.DATE_PATTERN)
    private Date stageEnd;

    @Schema(description = "运维费方案")
    private List<BillPriceSchemeVO> billPriceScheme;

    @JsonIgnore
    private String parentId;

    @JsonIgnore
    private Byte stageFrom;

    @JsonIgnore
    private Byte stageTo;

    @JsonIgnore
    private BigDecimal price;

    @JsonIgnore
    private String province;

    @JsonIgnore
    private String city;

}