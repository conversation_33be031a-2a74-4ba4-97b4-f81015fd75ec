package com.botong.system.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "运维费方案")
public class BillPriceSchemeVO {

    @Schema(description = "父级ID")
    private String parentId;

    @Schema(description = "运维费方案ID")
    private String schemeId;

    @Schema(description = "资方ID")
    private String investorId;

    @Schema(description = "运维开始方案阶段")
    private Byte stageFrom;

    @Schema(description = "运维结束方案阶段")
    private Byte stageTo;

    @Schema(description = "运维阶段")
    private String maintenancePhase;

    @Schema(description = "运维单价(元/年/W)")
    private BigDecimal price;

    @Schema(description = "省")
    private String province;

    @Schema(description = "市/州")
    private String city;
}