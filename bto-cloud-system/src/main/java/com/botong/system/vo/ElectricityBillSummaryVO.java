package com.botong.system.vo;

import com.botong.framework.common.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
* 上网电量电费管理
*
* <AUTHOR> 
* @since  2024-12-24
*/
@Data
@Schema(description = "上网电量电费管理")
public class ElectricityBillSummaryVO implements Serializable {
	private static final long serialVersionUID = 1L;

	@Schema(description = "电厂（交易对象）编号")
	private String meterId;

	@Schema(description = "电厂（交易对象）名称")
	private String factoryName;

	@Schema(description = "结算时间段")
	@JsonFormat(pattern = DateUtils.DATE_PATTERN)
	private String settlementPeriod;

	@Schema(description = "卖电量（kWh）")
	private BigDecimal electricity;

	@Schema(description = "税率")
	private String taxRate;

	@Schema(description = "单价")
	private BigDecimal electrovalence;

	@Schema(description = "不含税电费")
	private BigDecimal electricityWithoutTax;

	@Schema(description = "税金")
	private BigDecimal taxAmount;

	@Schema(description = "含税电费")
	private BigDecimal electricityWithTax;

	@Schema(description = "管理单位")
	private String administrator;

	@Schema(description = "创建时间")
	@JsonFormat(pattern = DateUtils.DATE_PATTERN, timezone = "GMT+8")
	private Date createTime;


}