package com.botong.system.vo;

import com.botong.framework.common.page.PageResult;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * <AUTHOR> by zhb on 2025/1/8.
 */
@Data
@Schema(description = "合并电费账单分页统计")
@AllArgsConstructor
public class CombinedBillPageVO {

    @Schema(description = "分页数据")
    private PageResult<BillConsolidatedStatementVO> pageResult;

    @Schema(description = "账单统计结果")
    private ElectricitySummaryResultVO electricitySummaryResultVO;

}