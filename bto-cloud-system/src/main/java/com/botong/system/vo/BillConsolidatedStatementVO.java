package com.botong.system.vo;

import com.botong.framework.common.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
* 电费账单
*
* <AUTHOR> 
* @since 1.0.0 2025-01-04
*/
@Data
@Schema(description = "电费账单")
public class BillConsolidatedStatementVO implements Serializable {
	private static final long serialVersionUID = 1L;

	@Schema(description = "电站uid")
	private String plantUid;

	@Schema(description = "电站名称")
	private String plantName;

	@Schema(description = "并网日期")
	@JsonFormat(pattern = DateUtils.DATE_PATTERN)
	private Date connectionDate;

	@Schema(description = "装机容量")
	private String plantCapacity;

	@Schema(description = "电量")
	private BigDecimal electricity;

	@Schema(description = "结算月份")
	private String settlementDate;

	@Schema(description = "税率")
	private BigDecimal taxRate;

	@Schema(description = "税金")
	private BigDecimal taxAmount;

	@Schema(description = "含税单价")
	private BigDecimal electrovalence;

	@Schema(description = "含税电费")
	private BigDecimal electricityWithTax;

	@Schema(description = "不含税电费")
	private BigDecimal electricityWithoutTax;

	@Schema(description = "管理单位")
	private String administrator;

	@Schema(description = "越秀结算含税电费")
	private BigDecimal electricityWithTaxYx;

	@Schema(description = "项目公司（优先级排序：供电局>越秀>博光）")
	private String projectCompany;

	@Schema(description = "电表编号")
	private String meterId;

	@Schema(description = "进件编号")
	private String orderId;

	@Schema(description = "合同编号")
	private String contractId;

	@Schema(description = "是否按月结算{按月结算：1 不按月结算：？？（几个月一结就是几。例：{ 1、按季度结算第一季度：3 2、按2、3月份结算：2 }） }")
	private Integer isMonth;

	@Schema(description = "越秀博通是否一致：一致为0 不一致为1")
	private Integer accord;

	@Schema(description = "创建时间")
	@JsonFormat(pattern = DateUtils.DATE_TIME_PATTERN)
	private Date createTime;

	@Schema(description = "更新时间")
	@JsonFormat(pattern = DateUtils.DATE_TIME_PATTERN)
	private Date updateTime;


}