package com.botong.system.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.botong.framework.common.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR> by zhb on 2025/1/10.
 */
@Data
@EqualsAndHashCode
@ContentRowHeight(25)
@HeadRowHeight(40)
@ColumnWidth(20)
public class MaintenanceBillExcelVO {

    @ExcelProperty("电站名称")
    private String plantName;

    @ExcelProperty("装机容量")
    private BigDecimal plantCapacity;

    @ExcelProperty("市/州")
    private String city;

    @ExcelProperty("电表编号")
    private String meterId;

    @ExcelProperty("合同编号")
    private String contractId;

    @ExcelProperty("进件编号")
    private String orderId;

    @ExcelProperty("项目公司")
    private String projectCompany;

    @ExcelProperty("并网日期")
    @JsonFormat(pattern = DateUtils.DATE_TIME_PATTERN)
    private Date createTime;

    @ExcelProperty("年份")
    @JsonFormat(pattern = "yyyy")
    private Date closingYear;

    @ExcelProperty("一月")
    private String january;

    @ExcelProperty("二月")
    private String february;

    @ExcelProperty("三月")
    private String march;

    @ExcelProperty("四月")
    private String april;

    @ExcelProperty("五月")
    private String may;

    @ExcelProperty("六月")
    private String june;

    @ExcelProperty("七月")
    private String july;

    @ExcelProperty("八月")
    private String august;

    @ExcelProperty("九月")
    private String september;

    @ExcelProperty("十月")
    private String october;

    @ExcelProperty("十一月")
    private String november;

    @ExcelProperty("十二月")
    private String december;

    @ExcelProperty("更新时间")
    @JsonFormat(pattern = DateUtils.DATE_TIME_PATTERN)
    private Date updateTime;
    
}
