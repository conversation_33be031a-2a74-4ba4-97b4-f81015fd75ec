package com.botong.system.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "电费账单统计结果")
public class ElectricitySummaryResultVO {

    @Schema(description = "总记录数", hidden = true)
    @JsonIgnore
    private int totalCount;

    @Schema(description = "越秀含税电费总和")
    private double sumElectricityWithTaxYx;

    @Schema(description = "卖电量总和")
    private double sumElectricity;

    @Schema(description = "税额总和")
    private double sumTaxAmount;

    @Schema(description = "含税电费总和")
    private double sumElectricityWithTax;

    @Schema(description = "不含税电费总和")
    private double sumElectricityWithoutTax;
}