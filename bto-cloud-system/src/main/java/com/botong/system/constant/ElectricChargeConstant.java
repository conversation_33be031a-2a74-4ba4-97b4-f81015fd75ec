package com.botong.system.constant;

/**
 * <AUTHOR> by 2022 on 2024/11/11.
 */

public class ElectricChargeConstant {

    /**
     * 开发环境基础URL
     */
    public static final String BASE_URL_SIT = "https://59.37.128.138:442/out/retail_leasing/";

    /**
     * 测试环境基础URL
     */
    public static final String BASE_URL_UAT = "https://59.37.128.138:442/retail_leasing/";

    /**
     * 生产环境基础URL
     */
    public static final String BASE_URL_PROD = "https://carleasing.yuexiu-finance.com:442/retail_leasing/";

    /**
     * 系统标识
     */
    public static final String SYSTEM_ID = "YX-GDBT";

    /**
     * 测试环境证书Key
     */
    public static final String CERT_KEY_DEV = "Yx12345678";

    /**
     * 测试环境用户名
     */
    public static final String USER_NAME_DEV = "gdbt001";

    /**
     * 测试环境密码
     */
    public static final String PASSWORD_DEV = "Password@123";

    /**
     * 生产环境证书Key
     */
    public static final String CERT_KEY_PROD = "qczl#321";

    /**
     * 生产环境用户名
     */
    public static final String USER_NAME_PROD = "gdbt001";

    /**
     * 生产环境密码
     */
    public static final String PASSWORD_PROD = "CV@268!Hj53";

    /**
     * 获取token路径
     */
    public static final String TOKEN_URL = "oauth/token";

    /**
     * 获取电费账单接口路径
     */
    public static final String ELECTRIC_CHARGE_URL = "r/api/finance/project/operating/sunnyday/getelectriccharge";

}