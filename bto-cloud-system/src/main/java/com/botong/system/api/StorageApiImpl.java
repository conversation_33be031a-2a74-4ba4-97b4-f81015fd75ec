package com.botong.system.api;

import com.botong.api.module.system.StorageApi;
import com.botong.api.module.system.dto.StorageDTO;
import com.botong.framework.common.utils.Result;
import com.botong.storage.service.StorageService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

/**
 * 文件上传
  */
@RestController
@AllArgsConstructor
public class StorageApiImpl implements StorageApi {
    private final StorageService storageService;

    @Override
    public Result<StorageDTO> upload(MultipartFile file) throws IOException {
        // 是否为空
        if (file.isEmpty()) {
            return Result.error("文件不能为空");
        }

        // 上传路径
        String path = storageService.getPath(file.getOriginalFilename());
        // 上传文件
        String url = storageService.upload(file.getBytes(), path);

        // 上传信息
        StorageDTO storage = new StorageDTO();
        storage.setUrl(url);
        storage.setSize(file.getSize());

        return Result.ok(storage);
    }

    @Override
    public Result<String> delete(String[] filePaths) throws Exception {
        String result = storageService.deleteBatchFile(filePaths);
        return Result.ok(result);
    }
}
