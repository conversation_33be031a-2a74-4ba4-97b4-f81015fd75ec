package com.botong.system.api;

import cn.hutool.core.collection.CollUtil;
import com.botong.api.module.system.SysDictDataApi;
import com.botong.api.module.system.SysOrgApi;
import com.botong.api.module.system.vo.DictDataVO;
import com.botong.api.module.system.vo.ProjectConversionVO;
import com.botong.api.module.system.vo.RepairUserVO;
import com.botong.api.module.workorder.entity.OrgBaseEntity;
import com.botong.framework.common.constant.BtoConstant;
import com.botong.framework.common.exception.ServerException;
import com.botong.framework.common.utils.AssertUtils;
import com.botong.framework.common.utils.Result;
import com.botong.system.entity.SysOrgEntity;
import com.botong.system.service.SysOrgService;
import com.botong.system.service.SysUserService;
import com.botong.system.vo.SysOrgVO;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 机构数据管理
 *
 * <AUTHOR> by zhb on 2023/9/19.
 */
@RestController
@AllArgsConstructor
public class SysOrgApiImpl implements SysOrgApi {
    @Resource
    private SysOrgService sysOrgService;
    @Resource
    private SysDictDataApi sysDictDataApi;
    @Resource
    private SysOrgApi sysOrgApi;
    @Resource
    private SysUserService sysUserService;

    @Override
    public Long getOrgIdByName(String name) {
        return sysOrgService.getOrgIdByName(name);
    }

    @Override
    public List<Long> getSubOrgIdList(List<Long> ids) {
        List<Long> result = new ArrayList<>();
        ids.forEach(id -> {
            List<Long> subOrgIds = sysOrgService.getSubOrgIdList(id);
            result.addAll(subOrgIds);
        });
        return result.stream().distinct().collect(Collectors.toList());
    }

    @Override
    public Result<ProjectConversionVO> getOrgIdWithRepairUserByProjectSpecial(Long projectSpecial) {
        AssertUtils.isNull(projectSpecial, "projectSpecial");
        ProjectConversionVO project = new ProjectConversionVO();
        String plantType = BtoConstant.DICT_PLANT_TYPE_KEY;
        List<DictDataVO> dictDataVos = sysDictDataApi.getDictDataByTypeId(plantType);

        List<SysOrgVO> trees = sysOrgService.getList();
        for (DictDataVO dictData : dictDataVos) {
            // 项目类型转换，光云系统 projectId --> 工单系统 orgId
            if (dictData.getDictValue().equals(projectSpecial.toString())) {
                Long orgId = sysOrgApi.getOrgIdByName(dictData.getDictLabel());
                AssertUtils.isNull(orgId, "orgId");
                project.setPlantType(Math.toIntExact(orgId));
                project.setPlantTypeName(dictData.getDictLabel());
                List<RepairUserVO> responsibleUser = getResponsibleUserByOrgId(orgId, trees);
                if (CollUtil.isEmpty(responsibleUser)) {
                    throw new ServerException("该项目类型下不存在责任人");
                }
                project.setRepairUserVO(responsibleUser);
            }
        }
        if (CollUtil.isEmpty(project.getRepairUserVO())) {
            throw new ServerException("工单系统未找到对应项目，请联系工作人员添加");
        }

        return Result.ok(project);
    }

    @Override
    public List<OrgBaseEntity> getAllOrg() {
        return sysOrgService.getAllOrg();
    }

    @Override
    public String addOrg(String name, String pid) {
        SysOrgVO sysOrgVO = new SysOrgVO();
        sysOrgVO.setName(name);
        sysOrgVO.setPid(Long.valueOf(pid));
        return sysOrgService.saveReturnId(sysOrgVO);
    }

    @Override
    public void updateNameById(String orgId, String name) {
        SysOrgEntity sysOrgEntity = sysOrgService.getById(orgId);
        sysOrgEntity.setName(name);
        sysOrgService.updateById(sysOrgEntity);
    }

    private List<RepairUserVO> getResponsibleUserByOrgId(Long id, List<SysOrgVO> trees) {
        List<RepairUserVO> users = sysUserService.getUserListByOrgId(Collections.singletonList(id));
        List<RepairUserVO> responsibleUser = users.stream()
                .filter(user -> user.getResponsibleFlag().equals(1))
                .collect(Collectors.toList());

        if (users.size() == 1) {
            return users;
        }
        if (CollUtil.isNotEmpty(responsibleUser)) {
            return responsibleUser;
        }

        // TODO 递归上级结构找负责人
        // for (SysOrgVO tree : trees) {
        //     if (tree.getId().equals(id)) {
        //         return getResponsibleUserByOrgId(tree.getPid(), trees);
        //     }
        // }

        return null;
    }


}
