package com.botong.system.api;

import com.botong.api.module.system.SysParamsApi;
import com.botong.system.enums.SysParamsEnum;
import com.botong.system.service.SysParamsService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.RestController;

/**
 * 参数管理
 * <AUTHOR> by zhb on 2023/9/19.
 */
@RestController
@AllArgsConstructor
public class SysParamsApiImpl implements SysParamsApi {

    private final SysParamsService sysParamsService;

    /**
     * 短信通知
     * 生产环境开启
     * 为避免产生过多的短信费用，开发环境关闭短信通知
     * @return true：开启  false：关闭
     */
    @Override
    public Boolean smsNotifyEnabled() {

        return sysParamsService.getBoolean(SysParamsEnum.DISPATCH_SMS.name());
    }
}