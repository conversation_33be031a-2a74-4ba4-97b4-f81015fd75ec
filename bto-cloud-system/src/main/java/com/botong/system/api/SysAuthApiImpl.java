package com.botong.system.api;

import cn.hutool.core.bean.BeanUtil;
import com.botong.api.module.system.SysAuthApi;
import com.botong.api.module.system.vo.LoginVO;
import com.botong.api.module.system.vo.SysUserTokenVO;
import com.botong.framework.common.utils.Result;
import com.botong.system.service.SysAuthService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2023/11/9 9:46
 */
@RestController
@AllArgsConstructor
public class SysAuthApiImpl implements SysAuthApi {
    @Resource
    private SysAuthService sysAuthService;

    @Override
    public Result<SysUserTokenVO> getAccessToken(LoginVO login) {
        com.botong.system.vo.SysUserTokenVO access = sysAuthService.login(login);
        return Result.ok(BeanUtil.copyProperties(access, SysUserTokenVO.class));
    }
}
