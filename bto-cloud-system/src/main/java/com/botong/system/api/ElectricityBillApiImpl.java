package com.botong.system.api;

import com.botong.api.module.system.ElectricityBillApi;
import com.botong.framework.common.utils.Result;
import com.botong.system.controller.ElectricityBillSummaryController;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.RestController;

/**
 * 电费账单API实现类
 * 
 * <AUTHOR>
 * @since 2025-01-17
 */
@RestController
@AllArgsConstructor
public class ElectricityBillApiImpl implements ElectricityBillApi {

    private final ElectricityBillSummaryController electricityBillSummaryController;

    @Override
    public Result<String> updateYxBill() {
        return electricityBillSummaryController.updateYxBill();
    }
}
