package com.botong.system.api;

import com.botong.api.module.system.SysDictDataApi;
import com.botong.api.module.system.vo.DictDataVO;
import com.botong.system.service.SysDictDataService;
import com.botong.system.service.SysDictTypeService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 字典数据管理
 * <AUTHOR> by zhb on 2023/10/18.
 */
@RestController
@AllArgsConstructor
public class SysDictDataApiImpl implements SysDictDataApi {

    @Resource
    private SysDictDataService sysDictDataService;

    @Resource
    private SysDictTypeService sysDictTypeService;

    @Override
    public List<DictDataVO> getDictDataByTypeId(String dictType) {
        return sysDictDataService.getDictDataByTypeId(sysDictTypeService.getDictIdByType(dictType));
    }

    @Override
    public List<DictDataVO> getDictSqlByType(String dictType) {
        return sysDictTypeService.getDictSqlByTypeId(dictType);
    }

}
