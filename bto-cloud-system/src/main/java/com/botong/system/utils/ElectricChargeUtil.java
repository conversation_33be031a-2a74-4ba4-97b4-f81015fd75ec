package com.botong.system.utils;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.botong.framework.common.cache.RedisCache;
import com.botong.framework.common.cache.RedisKeys;
import com.botong.system.constant.ElectricChargeConstant;
import com.botong.system.entity.ElectricBill;
import com.botong.system.entity.Head;
import com.botong.system.query.ElectricChargeQuery;
import com.botong.system.service.YxElectricityBillService;
import com.opencsv.bean.CsvToBean;
import com.opencsv.bean.CsvToBeanBuilder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;

import javax.net.ssl.KeyManagerFactory;
import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import java.io.*;
import java.security.KeyStore;
import java.security.SecureRandom;
import java.security.cert.X509Certificate;
import java.util.*;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;

/**
 * <AUTHOR> by zhb on 2024/11/11.
 * 该工具类用于处理与电费相关的网络请求操作，包含了设置SSL连接、发送请求等功能。
 */
@Slf4j
@Component
public class ElectricChargeUtil {

    @Value("${yuexiu.environment}")
    private String environment;

    @Value("${yuexiu.file.tempDirectory}")
    private String tempDirectory;

    @Autowired
    private YxElectricityBillService yxElectricityBillService;

    @Autowired
    private RedisCache redisCache;

    public JSONObject request(ElectricChargeQuery query) {
        try {
            String url = getBaseUrl() + query.getInterfaceUrl();
            if (StrUtil.isNotEmpty(query.getUrlParam())) {
                url += query.getUrlParam();
            }
            ClassPathResource resource = new ClassPathResource(getCertificateName());
            // 获取文件对应的输入流
            InputStream sslInputStream = resource.getInputStream();
            if (sslInputStream == null) {
                throw new IOException("无法找到p12证书文件");
            }
            String sslPassword = getSslPassword();

            // KeyStore用于存储密钥和证书等信息
            KeyStore clientKeyStore = KeyStore.getInstance("PKCS12");
            // 读取证书文件内容并加载到KeyStore中
            clientKeyStore.load(new BufferedInputStream(sslInputStream), sslPassword.toCharArray());
            // 创建管理密钥的KeyManager
            KeyManagerFactory keyManagerFactory = KeyManagerFactory.getInstance(KeyManagerFactory.getDefaultAlgorithm());
            // 证书密码初始化KeyManagerFactory
            keyManagerFactory.init(clientKeyStore, sslPassword.toCharArray());
            // 信任管理器数组
            TrustManager[] trustAllCerts = new TrustManager[]{new X509TrustManager() {
                @Override
                public void checkClientTrusted(X509Certificate[] x509Certificates, String s) {
                }

                @Override
                public void checkServerTrusted(X509Certificate[] x509Certificates, String s) {
                }

                @Override
                public X509Certificate[] getAcceptedIssuers() {
                    return new X509Certificate[0];
                }
            }};

            SSLContext sslContext = SSLContext.getInstance("TLS");
            sslContext.init(keyManagerFactory.getKeyManagers(), trustAllCerts, new SecureRandom());
            HttpRequest request = HttpUtil.createPost(url);
            request.setSSLSocketFactory(sslContext.getSocketFactory());
            request.header("Content-Type", "application/json");
            String bodyParam = query.getBodyParam();
            if (StrUtil.isNotEmpty(bodyParam)) {
                request.body(JSONUtil.toJsonStr(bodyParam));
            }
            HttpResponse response = request.execute();
            if (ElectricChargeConstant.ELECTRIC_CHARGE_URL.equals(query.getInterfaceUrl())) {
                InputStream inputStream = response.bodyStream();
                String result = analyticStorage(inputStream);
                inputStream.close();
                return JSONUtil.createObj().put("result", result);
            }
            String result = response.body();
            return JSONUtil.parseObj(result);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public String analyticStorage(InputStream inputStream) {
        String destDirectory = tempDirectory;
        String result = "";
        try {
            File destDir = new File(destDirectory);
            if (!destDir.exists()) {
                destDir.mkdir();
            }

            ZipInputStream zipIn = new ZipInputStream(inputStream);
            ZipEntry entry = zipIn.getNextEntry();
            while (entry != null) {
                String filePath = destDirectory + File.separator + entry.getName();
                // 调用方法创建完整目录结构
                createDirectoriesForFile(filePath);

                if (!entry.isDirectory()) {
                    // 如果是文件，解压文件
                    FileOutputStream fos = new FileOutputStream(filePath);
                    byte[] buffer = new byte[1024];
                    int len;
                    while ((len = zipIn.read(buffer)) > 0) {
                        fos.write(buffer, 0, len);
                    }
                    fos.close();

                    // 判断解压出的文件是否是bill_all.csv，如果是则读取其内容
                    if (filePath.endsWith("bill_all.csv") || filePath.endsWith("bill_inc.csv")) {
                        FileInputStream fileIn = new FileInputStream(filePath);

                        BufferedReader reader = new BufferedReader(new InputStreamReader(fileIn));
                        // 创建CsvToBean对象，指定CSV文件的映射规则
                        CsvToBean<ElectricBill> csvToBean = new CsvToBeanBuilder<ElectricBill>(reader)
                                .withType(ElectricBill.class)
                                .withSeparator(',')
                                .build();

                        // 将CSV数据转换为Java bean的列表
                        List<ElectricBill> electricBills = csvToBean.parse();
                        result = yxElectricityBillService.saveBills(electricBills);
                        reader.close();
                        fileIn.close();
                    }
                }
                zipIn.closeEntry();
                entry = zipIn.getNextEntry();
            }
            zipIn.close();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            deleteFolder(new File(tempDirectory));
        }
        return result;
    }

    public static void deleteFolder(File folder) {
        if (folder.isDirectory()) {
            File[] files = folder.listFiles();
            if (files != null) {
                for (File file : files) {
                    deleteFolder(file);
                }
            }
        }
        folder.delete();
    }

    private static void createDirectoriesForFile(String filePath) {
        File file = new File(filePath);
        File parentDir = file.getParentFile();
        if (parentDir != null && !parentDir.exists()) {
            parentDir.mkdirs();
        }
    }

    public String getToken() {
        Object o = redisCache.get(RedisKeys.getYxAccessTokenKey());
        if (ObjectUtil.isNotEmpty(o)) {
            return o.toString();
        }
        ElectricChargeQuery electricChargeQuery = new ElectricChargeQuery();
        electricChargeQuery.setInterfaceUrl(ElectricChargeConstant.TOKEN_URL);
        HashMap<String, String> queryParam = new HashMap<>();
        queryParam.put("client_id", "Finance");
        queryParam.put("client_secret", "53e18ab1-67cc-475f-b54b-32c00f8b9bd6");
        queryParam.put("grant_type", "password");
        queryParam.put("username", getUserName());
        queryParam.put("password", getPassword());
        String queryParamStr = mapToString(queryParam);
        electricChargeQuery.setUrlParam(queryParamStr);
        JSONObject jsonObject = request(electricChargeQuery);
        Object accessToken = jsonObject.get("access_token");
        if (ObjectUtil.isNotEmpty(accessToken)) {
            redisCache.set(RedisKeys.getYxAccessTokenKey(), accessToken.toString(), 2888);
            return accessToken.toString();
        }
        return null;
    }

    public String mapToString(Map<String, String> map) {
        StringBuilder sb = new StringBuilder();
        sb.append("?");
        for (Map.Entry<String, String> entry : map.entrySet()) {
            sb.append(entry.getKey()).append("=").append(entry.getValue()).append("&");
        }
        if (sb.length() > 0) {
            sb.setLength(sb.length() - 1);
        }
        return sb.toString();
    }

    String getUserName() {
        String result = null;
        switch (environment) {
            case "SIT":
            case "UAT":
                result = ElectricChargeConstant.USER_NAME_DEV;
                break;
            case "PROD":
                result = ElectricChargeConstant.USER_NAME_PROD;
                break;
        }
        return result;
    }

    public String getPassword() {
        String result = null;
        switch (environment) {
            case "SIT":
            case "UAT":
                result = ElectricChargeConstant.PASSWORD_DEV;
                break;
            case "PROD":
                result = ElectricChargeConstant.PASSWORD_PROD;
                break;
        }
        return result;
    }

    public String getBaseUrl() {
        if ("SIT".equals(environment)) {
            return ElectricChargeConstant.BASE_URL_SIT;
        } else if ("UAT".equals(environment)) {
            return ElectricChargeConstant.BASE_URL_UAT;
        } else if ("PROD".equals(environment)) {
            return ElectricChargeConstant.BASE_URL_PROD;
        } else {
            log.error("environment is not support");
            return null;
        }
    }

    public String getSslPassword() {
        String result = null;
        switch (environment) {
            case "SIT":
            case "UAT":
                result = ElectricChargeConstant.CERT_KEY_DEV;
                break;
            case "PROD":
                result = ElectricChargeConstant.CERT_KEY_PROD;
                break;
        }
        return result;
    }

    public String getCertificateName() {
        String result = null;
        switch (environment) {
            case "SIT":
            case "UAT":
                result = "client.p12";
                break;
            case "PROD":
                result = "prod_client.p12";
                break;
        }
        return result;
    }

    public Head getHeadParam() {
        Head head = new Head();
        head.setErrorCode("01");
        head.setSerialNumber(UUID.randomUUID().toString());
        head.setSystemId(ElectricChargeConstant.SYSTEM_ID);
        head.setTransactionCode("YX_PROJECT_BILL_ATTACHMENT_" + environment);
        head.setTransactionTime(DateUtil.format(new Date(), "yyyyMMdd HH:mm:ss"));
        head.setVersion(1);
        return head;
    }

}