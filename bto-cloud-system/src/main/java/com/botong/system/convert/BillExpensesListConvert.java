package com.botong.system.convert;

import com.botong.system.entity.BillExpensesListEntity;
import com.botong.system.vo.BillExpensesListVO;
import com.botong.system.vo.MaintenanceBillExcelVO;
import com.botong.system.vo.MaintenanceBillVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
* 运维费账单
*
* <AUTHOR> 
* @since 1.0.0 2025-01-09
*/
@Mapper
public interface BillExpensesListConvert {
    BillExpensesListConvert INSTANCE = Mappers.getMapper(BillExpensesListConvert.class);

    MaintenanceBillExcelVO convert(MaintenanceBillVO vo);

    BillExpensesListVO convert(BillExpensesListEntity entity);

    List<MaintenanceBillExcelVO> convertList(List<MaintenanceBillVO> list);

}