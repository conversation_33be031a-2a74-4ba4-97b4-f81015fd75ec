package com.botong.system.convert;

import com.botong.system.entity.BillPriceMaintenanceEntity;
import com.botong.system.vo.BillPriceMaintenanceVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
* 运维费单价
*
* <AUTHOR> 
* @since 1.0.0 2025-01-09
*/
@Mapper
public interface BillPriceMaintenanceConvert {
    BillPriceMaintenanceConvert INSTANCE = Mappers.getMapper(BillPriceMaintenanceConvert.class);

    BillPriceMaintenanceEntity convert(BillPriceMaintenanceVO vo);

    BillPriceMaintenanceVO convert(BillPriceMaintenanceEntity entity);

    List<BillPriceMaintenanceVO> convertList(List<BillPriceMaintenanceEntity> list);

}