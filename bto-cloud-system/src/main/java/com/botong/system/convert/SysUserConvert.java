package com.botong.system.convert;

import com.botong.framework.security.user.UserDetail;
import com.botong.system.entity.SysUserEntity;
import com.botong.api.module.system.vo.RepairUserVO;
import com.botong.system.vo.SysUserExcelVO;
import com.botong.system.vo.SysUserVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;


@Mapper
public interface SysUserConvert {
    SysUserConvert INSTANCE = Mappers.getMapper(SysUserConvert.class);

    SysUserVO convert(SysUserEntity entity);

    SysUserEntity convert(SysUserVO vo);

    SysUserVO convert(UserDetail userDetail);

    UserDetail convertDetail(SysUserEntity entity);

    List<SysUserVO> convertList(List<SysUserEntity> list);

    List<SysUserExcelVO> convert2List(List<SysUserEntity> list);

    List<RepairUserVO> convert3List(List<SysUserEntity> list);
    @Mapping(target = "name",source = "realName")
    RepairUserVO sysUserEntityToRepairUserVO(SysUserEntity sysUserEntity);
    List<SysUserEntity> convertListEntity(List<SysUserExcelVO> list);

}
