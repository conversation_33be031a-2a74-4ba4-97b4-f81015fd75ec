package com.botong.system.convert;

import com.botong.system.entity.BillConsolidatedStatementEntity;
import com.botong.system.vo.BillConsolidatedStatementExcelVO;
import com.botong.system.vo.BillConsolidatedStatementVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
* 电费账单
*
* <AUTHOR> 
* @since 1.0.0 2025-01-04
*/
@Mapper
public interface BillConsolidatedStatementConvert {
    BillConsolidatedStatementConvert INSTANCE = Mappers.getMapper(BillConsolidatedStatementConvert.class);

    BillConsolidatedStatementExcelVO convert(BillConsolidatedStatementVO vo);

    BillConsolidatedStatementVO convert(BillConsolidatedStatementEntity entity);

    @Mapping(target = "plantUid", ignore = true)
    List<BillConsolidatedStatementExcelVO> convertList(List<BillConsolidatedStatementVO> list);

}