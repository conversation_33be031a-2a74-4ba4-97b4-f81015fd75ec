package com.botong.system.convert;

import com.botong.system.entity.SysAttachmentTypeEntity;
import com.botong.system.vo.SysAttachmentTypeVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
* 附件分类管理
*
* <AUTHOR> 
* @since 1.0.0 2025-03-04
*/
@Mapper
public interface SysAttachmentTypeConvert {
    SysAttachmentTypeConvert INSTANCE = Mappers.getMapper(SysAttachmentTypeConvert.class);

    SysAttachmentTypeEntity convert(SysAttachmentTypeVO vo);

    SysAttachmentTypeVO convert(SysAttachmentTypeEntity entity);

    List<SysAttachmentTypeVO> convertList(List<SysAttachmentTypeEntity> list);

}