package com.botong.system.convert;

import com.botong.system.entity.BillPriceSchemeEntity;
import com.botong.system.vo.BillPriceSchemeVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
* 运维费用方案表
*
* <AUTHOR> 
* @since 1.0.0 2025-01-11
*/
@Mapper
public interface BillPriceSchemeConvert {
    BillPriceSchemeConvert INSTANCE = Mappers.getMapper(BillPriceSchemeConvert.class);

    BillPriceSchemeEntity convert(BillPriceSchemeVO vo);

    BillPriceSchemeVO convert(BillPriceSchemeEntity entity);

    List<BillPriceSchemeEntity> convertList(List<BillPriceSchemeVO> list);

}