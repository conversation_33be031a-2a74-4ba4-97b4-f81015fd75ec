package com.botong.system.convert;

import com.botong.api.module.system.vo.DictDataVO;
import com.botong.system.entity.SysDictDataEntity;
import com.botong.system.vo.SysDictDataVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface SysDictDataConvert {
    SysDictDataConvert INSTANCE = Mappers.getMapper(SysDictDataConvert.class);

    SysDictDataVO convert(SysDictDataEntity entity);

    SysDictDataEntity convert(SysDictDataVO vo);
    
    List<SysDictDataVO> convertList(List<SysDictDataEntity> list);

    DictDataVO convertDictDataVO(SysDictDataEntity entity);

    List<DictDataVO> convertDictDataVOList(List<SysDictDataEntity> list);

}
