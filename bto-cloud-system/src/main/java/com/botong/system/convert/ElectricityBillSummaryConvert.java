package com.botong.system.convert;

import com.botong.system.entity.ElectricityBillSummaryEntity;
import com.botong.system.vo.ElectricityBillSummaryExcelVO;
import com.botong.system.vo.ElectricityBillSummaryVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;


/**
 * 电费汇总转换
 *
 * <AUTHOR>
 * @since 2024-12-25 11:48:55
 */
@Mapper
public interface ElectricityBillSummaryConvert {
    ElectricityBillSummaryConvert INSTANCE = Mappers.getMapper(ElectricityBillSummaryConvert.class);

    @Mappings({
            @Mapping(target = "taxRate", expression = "java(convertTaxRateToPercentage(entity.getTaxRate()))"),
            @Mapping(target = "settlementPeriod", expression = "java(converterSettlementPeriod(entity.getSettlementPeriod(), false))")

    })
    ElectricityBillSummaryVO convert(ElectricityBillSummaryEntity entity);

    @Mappings({
            @Mapping(target = "settlementPeriod", expression = "java(converterSettlementPeriod(entity.getSettlementPeriod(), true))")
    })
    ElectricityBillSummaryEntity convert(ElectricityBillSummaryExcelVO entity);

    @Mappings({
            @Mapping(target = "taxRate", expression = "java(convertTaxRateToDecimal(vo.getTaxRate()))"),
            @Mapping(target = "settlementPeriod", expression = "java(converterSettlementPeriod(vo.getSettlementPeriod(), true))")
    })
    ElectricityBillSummaryEntity convert(ElectricityBillSummaryVO vo);


    @Mappings({
            @Mapping(target = "taxRate", expression = "java(convertTaxRateToPercentage(vo.getTaxRate()))"),
            @Mapping(target = "settlementPeriod", expression = "java(converterSettlementPeriod(vo.getSettlementPeriod(), false))")
    })
    ElectricityBillSummaryExcelVO convert2Excel(ElectricityBillSummaryEntity vo);

    List<ElectricityBillSummaryVO> convertList(List<ElectricityBillSummaryEntity> list);

    List<ElectricityBillSummaryEntity> convertExcel2List(List<ElectricityBillSummaryExcelVO> list);

    List<ElectricityBillSummaryExcelVO> convertList2Excel(List<ElectricityBillSummaryEntity> list);

    // 将小数转为百分比字符串
    default String convertTaxRateToPercentage(BigDecimal taxRate) {
        if (taxRate == null) {
            return null;
        }
        return taxRate.multiply(BigDecimal.valueOf(100)).stripTrailingZeros().toPlainString() + "%";
    }

    // 将百分比字符串转为小数
    default BigDecimal convertTaxRateToDecimal(String taxRate) {
        if (taxRate == null || !taxRate.endsWith("%")) {
            return BigDecimal.ZERO;
        }
        String numericValue = taxRate.replace("%", "");
        return new BigDecimal(numericValue).divide(BigDecimal.valueOf(100));
    }
    // 修改后的方法，用于转换 settlementPeriod
    default String converterSettlementPeriod(String settlementPeriod, boolean toEntityFormat) {
        if (settlementPeriod == null || settlementPeriod.isEmpty()) {
            return null;
        }

        try {
            if (toEntityFormat) {
                // VO 或 Excel 格式 ("yyyy年MM月") 转 Entity 格式 ("yyyy-MM")
                SimpleDateFormat inputFormat = new SimpleDateFormat("yyyy年MM月");
                SimpleDateFormat outputFormat = new SimpleDateFormat("yyyy-MM");
                Date date = inputFormat.parse(settlementPeriod);
                return outputFormat.format(date);
            } else {
                // Entity 格式 ("yyyy-MM") 转 VO 或 Excel 格式 ("yyyy年MM月")
                SimpleDateFormat inputFormat = new SimpleDateFormat("yyyy-MM");
                SimpleDateFormat outputFormat = new SimpleDateFormat("yyyy年MM月");
                Date date = inputFormat.parse(settlementPeriod);
                return outputFormat.format(date);
            }
        } catch (ParseException e) {
            throw new RuntimeException("日期格式转换错误: " + settlementPeriod, e);
        }
    }

}