package com.botong.system.dao;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.botong.framework.mybatis.dao.BaseDao;
import com.botong.system.entity.BillPriceMaintenanceEntity;
import com.botong.system.query.BillPriceMaintenanceQuery;
import com.botong.system.vo.BillPriceMaintenanceVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
* 运维费单价
*
* <AUTHOR> 
* @since 1.0.0 2025-01-09
*/
@Mapper
public interface BillPriceMaintenanceDao extends BaseDao<BillPriceMaintenanceEntity> {

    IPage<BillPriceMaintenanceVO> page(@Param("query") BillPriceMaintenanceQuery query, @Param("page") Page<BillPriceMaintenanceVO> page);
}