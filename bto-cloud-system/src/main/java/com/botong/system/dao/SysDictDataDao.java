package com.botong.system.dao;

import com.botong.api.module.system.vo.DictDataVO;
import com.botong.framework.mybatis.dao.BaseDao;
import com.botong.system.entity.SysDictDataEntity;
import com.botong.system.vo.SysDictVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 字典数据
 */
@Mapper
public interface SysDictDataDao extends BaseDao<SysDictDataEntity> {

    @Select("${sql}")
    List<SysDictVO.DictData> getListForSql(@Param("sql") String sql);

    @Select("SELECT dict_sql FROM sys_dict_type WHERE deleted = 0 AND dict_type = #{dictType}")
    String getDictSqlByTypeId(@Param("dictType") String dictType);

    @Select("${sql}")
    List<DictDataVO> executeSql(@Param("sql") String sql);
}
