package com.botong.system.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.botong.system.entity.ElectricBill;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface YxElectricBillDao extends BaseMapper<ElectricBill> {

    List<ElectricBill> selectAllElectricBill();

    String selectElectricBillByMonth(@Param("month") String month, @Param("entryNumber") String entryNumber);

    Date selectPlantCreateTime(@Param("entryNumber")String entryNumber);

    LocalDate getPlantCreateTime(@Param("entryNumber")String entryNumber);
}