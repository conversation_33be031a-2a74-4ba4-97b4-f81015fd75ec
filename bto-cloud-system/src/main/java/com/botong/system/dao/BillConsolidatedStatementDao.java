package com.botong.system.dao;

import com.botong.framework.mybatis.dao.BaseDao;
import com.botong.system.entity.BillConsolidatedStatementEntity;
import com.botong.system.query.BillConsolidatedStatementQuery;
import com.botong.system.vo.BillConsolidatedStatementVO;
import com.botong.system.vo.ElectricitySummaryResultVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 电费账单
 *
 * <AUTHOR>
 * @since 1.0.0 2025-01-04
 */
@Mapper
public interface BillConsolidatedStatementDao extends BaseDao<BillConsolidatedStatementEntity> {

    List<BillConsolidatedStatementVO> selectList(@Param("query") BillConsolidatedStatementQuery query);

    ElectricitySummaryResultVO getStatisticalResult(@Param("query") BillConsolidatedStatementQuery query);

    void updateDataBases();
}