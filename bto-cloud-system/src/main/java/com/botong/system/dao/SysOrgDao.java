package com.botong.system.dao;

import com.botong.api.module.workorder.entity.OrgBaseEntity;
import com.botong.framework.mybatis.dao.BaseDao;
import com.botong.system.entity.SysOrgEntity;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 机构管理
 */
@Mapper
public interface SysOrgDao extends BaseDao<SysOrgEntity> {

    List<SysOrgEntity> getList(Map<String, Object> params);

    /**
     * 获取所有机构的id、pid列表
     */
    List<SysOrgEntity> getIdAndPidList();

    List<OrgBaseEntity> getAllOrg();
}