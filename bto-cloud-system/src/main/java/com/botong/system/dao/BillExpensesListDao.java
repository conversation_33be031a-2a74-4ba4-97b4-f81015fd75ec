package com.botong.system.dao;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.botong.framework.mybatis.dao.BaseDao;
import com.botong.system.entity.BillExpensesListEntity;
import com.botong.system.query.BillExpensesListQuery;
import com.botong.system.vo.MaintenanceBillVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 运维费账单
 *
 * <AUTHOR>
 * @since 1.0.0 2025-01-09
 */
@Mapper
public interface BillExpensesListDao extends BaseDao<BillExpensesListEntity> {

    IPage<MaintenanceBillVO> page(@Param("query") BillExpensesListQuery query, Page<MaintenanceBillVO> page);
}