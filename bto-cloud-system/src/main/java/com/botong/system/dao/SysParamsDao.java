package com.botong.system.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.botong.framework.mybatis.dao.BaseDao;
import com.botong.system.entity.SysParamsEntity;
import org.apache.ibatis.annotations.Mapper;

/**
 * 参数管理
 */
@Mapper
public interface SysParamsDao extends BaseDao<SysParamsEntity> {

    default boolean isExist(String paramKey) {
        return this.exists(new QueryWrapper<SysParamsEntity>().eq("param_key" , paramKey));
    }
}