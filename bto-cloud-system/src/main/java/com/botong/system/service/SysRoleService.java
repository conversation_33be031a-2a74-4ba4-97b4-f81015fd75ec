package com.botong.system.service;

import com.botong.framework.common.page.PageResult;
import com.botong.framework.mybatis.service.BaseService;
import com.botong.system.entity.SysRoleEntity;
import com.botong.system.query.SysRoleQuery;
import com.botong.system.vo.SysRoleDataScopeVO;
import com.botong.system.vo.SysRoleVO;

import java.util.List;

/**
 * 角色
 */
public interface SysRoleService extends BaseService<SysRoleEntity> {

	PageResult<SysRoleVO> page(SysRoleQuery query);

	List<SysRoleVO> getList(SysRoleQuery query);

	void save(SysRoleVO vo);

	void update(SysRoleVO vo);

	void dataScope(SysRoleDataScopeVO vo);

	void delete(List<Long> idList);

	List<Long> getRepairRole(SysRoleQuery query);

    List<SysRoleVO> getCurrentUserRole();

    List<Long> getIdsByName(String remark);
}
