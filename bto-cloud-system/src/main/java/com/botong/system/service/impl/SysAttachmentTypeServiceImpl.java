package com.botong.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.botong.framework.common.exception.ServerException;
import com.botong.framework.common.page.PageResult;
import com.botong.framework.mybatis.service.impl.BaseServiceImpl;
import com.botong.system.convert.SysAttachmentTypeConvert;
import com.botong.system.dao.SysAttachmentTypeDao;
import com.botong.system.entity.SysAttachmentEntity;
import com.botong.system.entity.SysAttachmentTypeEntity;
import com.botong.system.query.SysAttachmentQuery;
import com.botong.system.query.SysAttachmentTypeQuery;
import com.botong.system.service.SysAttachmentService;
import com.botong.system.service.SysAttachmentTypeService;
import com.botong.system.vo.SysAttachmentTypeVO;
import com.botong.system.vo.SysAttachmentVO;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 附件分类管理
 *
 * <AUTHOR>
 * @since 1.0.0 2025-03-04
 */
@Service
@AllArgsConstructor
public class SysAttachmentTypeServiceImpl extends BaseServiceImpl<SysAttachmentTypeDao, SysAttachmentTypeEntity> implements SysAttachmentTypeService {


    private SysAttachmentService sysAttachmentService;

    @Override
    public PageResult<SysAttachmentTypeVO> page(SysAttachmentTypeQuery query) {
        IPage<SysAttachmentTypeEntity> page = baseMapper.selectPage(getPage(query), getWrapper(query));

        return new PageResult<>(SysAttachmentTypeConvert.INSTANCE.convertList(page.getRecords()), page.getTotal());
    }

    private LambdaQueryWrapper<SysAttachmentTypeEntity> getWrapper(SysAttachmentTypeQuery query) {
        LambdaQueryWrapper<SysAttachmentTypeEntity> wrapper = Wrappers.lambdaQuery();

        return wrapper;
    }

    @Override
    public void save(SysAttachmentTypeVO vo) {
        SysAttachmentTypeEntity entity = SysAttachmentTypeConvert.INSTANCE.convert(vo);

        baseMapper.insert(entity);
    }

    @Override
    public void update(SysAttachmentTypeVO vo) {
        SysAttachmentTypeEntity entity = SysAttachmentTypeConvert.INSTANCE.convert(vo);

        updateById(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(List<Long> idList) {
        for (Long l : idList) {
            LambdaQueryWrapper<SysAttachmentEntity> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(SysAttachmentEntity::getTypeId, l);
            if (!sysAttachmentService.list(wrapper).isEmpty()){
                throw new ServerException("分类下存在文件，请删除文件后再试");
            }
        }
        removeByIds(idList);
    }

    @Override
    public PageResult<SysAttachmentVO> getAttachmentByTypeId(SysAttachmentQuery query) {
        return sysAttachmentService.page(query);
    }

}