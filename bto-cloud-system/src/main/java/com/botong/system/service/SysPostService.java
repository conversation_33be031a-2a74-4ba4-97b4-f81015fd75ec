package com.botong.system.service;

import com.botong.framework.common.page.PageResult;
import com.botong.framework.mybatis.service.BaseService;
import com.botong.system.entity.SysPostEntity;
import com.botong.system.query.SysPostQuery;
import com.botong.system.vo.SysPostVO;

import java.util.List;

/**
 * 岗位管理
 */
public interface SysPostService extends BaseService<SysPostEntity> {

    PageResult<SysPostVO> page(SysPostQuery query);

    List<SysPostVO> getList();

    void save(SysPostVO vo);

    void update(SysPostVO vo);

    void delete(List<Long> idList);

    /**
     * 根据岗位ID获取岗位名称
     * @param postId 岗位
     * @return 岗位名称
     */
    List<String> getNamesByIds(List<Long> postId);
}