package com.botong.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.botong.api.module.system.vo.RepairUserVO;
import com.botong.framework.common.constant.BtoConstant;
import com.botong.framework.common.excel.ExcelFinishCallBack;
import com.botong.framework.common.exception.ServerException;
import com.botong.framework.common.page.PageResult;
import com.botong.framework.common.utils.AssertUtils;
import com.botong.framework.common.utils.DateUtils;
import com.botong.framework.common.utils.ExcelUtils;
import com.botong.framework.common.utils.Result;
import com.botong.framework.mybatis.service.impl.BaseServiceImpl;
import com.botong.framework.security.user.SecurityUser;
import com.botong.framework.security.user.UserDetail;
import com.botong.system.convert.SysUserConvert;
import com.botong.system.dao.SysUserDao;
import com.botong.system.entity.SysRoleEntity;
import com.botong.system.entity.SysUserEntity;
import com.botong.system.entity.SysUserOrgEntity;
import com.botong.system.enums.SuperAdminEnum;
import com.botong.system.enums.UserRemarkEnum;
import com.botong.system.enums.UserStatusEnum;
import com.botong.system.query.SysRoleQuery;
import com.botong.system.query.SysRoleUserQuery;
import com.botong.system.query.SysUserQuery;
import com.botong.system.service.*;
import com.botong.system.vo.OrgWithUserVO;
import com.botong.system.vo.SysOrgVO;
import com.botong.system.vo.SysUserExcelVO;
import com.botong.system.vo.SysUserVO;
import com.fhs.trans.service.impl.TransService;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.botong.framework.common.constant.BtoConstant.RESPONSIBLE_NAME;

/**
 * 用户管理
 */
@Service
@AllArgsConstructor
public class SysUserServiceImpl extends BaseServiceImpl<SysUserDao, SysUserEntity> implements SysUserService {
    private final SysUserRoleService sysUserRoleService;
    private final SysUserPostService sysUserPostService;
    private final SysUserOrgService sysUserOrgService;
    private final TransService transService;
    private final SysOrgService sysOrgService;
    @Resource
    private SysUserDao sysUserDao;
    private final SysRoleService sysRoleService;

    @Override
    public PageResult<SysUserVO> page(SysUserQuery query) {
        // 查询参数
        Map<String, Object> params = getParams(query);

        // 分页查询
        IPage<SysUserEntity> page = getPage(query);
        params.put(BtoConstant.PAGE, page);

        // 数据列表
        List<SysUserEntity> list = baseMapper.getList(params);

        List<SysUserVO> sysUserVos = SysUserConvert.INSTANCE.convertList(list);
        for (SysUserVO sysUserVO : sysUserVos) {
            sysUserVO.setOrgIds(sysUserOrgService.getOrgIdList(sysUserVO.getId()));
            sysUserVO.setOrgNames(sysOrgService.getOrgNamesByIds(sysUserVO.getOrgIds()));
        }

        return new PageResult<>(sysUserVos, page.getTotal());
    }

    private Map<String, Object> getParams(SysUserQuery query) {
        Map<String, Object> params = new HashMap<>();
        params.put("username", query.getUsername());
        params.put("mobile", query.getMobile());
        params.put("gender", query.getGender());

        // 数据权限
        params.put(BtoConstant.DATA_SCOPE, getDataScope("t1", null));

        return params;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void save(SysUserVO vo) {
        SysUserEntity entity = SysUserConvert.INSTANCE.convert(vo);
        // entity.setSuperAdmin(SuperAdminEnum.NO.getValue());

        // 判断用户名是否存在
        SysUserEntity user = baseMapper.getByUsername(entity.getUsername());
        if (user != null) {
            throw new ServerException("用户名已经存在");
        }

        // 判断手机号是否存在
        user = baseMapper.getByMobile(entity.getMobile());
        if (user != null) {
            throw new ServerException("手机号已经存在");
        }

        // 默认头像
        entity.setAvatar("https://qiniu.btosolarman.com/work-order-cloud/common/default_avatar.png");

        // 保存用户
        baseMapper.insert(entity);

        // 保存用户角色关系
        sysUserRoleService.saveOrUpdate(entity.getId(), vo.getRoleIdList());

        // 更新用户岗位关系
        sysUserPostService.saveOrUpdate(entity.getId(), vo.getPostIdList());

        // 更新用户机构关系
        sysUserOrgService.saveOrUpdate(entity.getId(), vo.getOrgIds());
    }

    @Override
    public void update(SysUserVO vo) {
        SysUserEntity entity = SysUserConvert.INSTANCE.convert(vo);

        // 判断用户名是否存在
        SysUserEntity user = baseMapper.getByUsername(entity.getUsername());
        if (user != null && !user.getId().equals(entity.getId())) {
            throw new ServerException("用户名已经存在");
        }

        // 判断手机号是否存在
        user = baseMapper.getByMobile(entity.getMobile());
        if (user != null && !user.getId().equals(entity.getId())) {
            throw new ServerException("手机号已经存在");
        }

        // 更新用户
        updateById(entity);

        // 更新用户角色关系
        sysUserRoleService.saveOrUpdate(entity.getId(), vo.getRoleIdList());

        // 更新用户岗位关系
        sysUserPostService.saveOrUpdate(entity.getId(), vo.getPostIdList());

        // 更新用户机构关系
        sysUserOrgService.saveOrUpdate(entity.getId(), vo.getOrgIds());
    }

    @Override
    public void delete(List<Long> idList) {
        // 删除用户
        removeByIds(idList);

        // 删除用户角色关系
        sysUserRoleService.deleteByUserIdList(idList);

        // 删除用户岗位关系
        sysUserPostService.deleteByUserIdList(idList);

        // 删除用户机构关系
        sysUserOrgService.deleteByUserIdList(idList);
    }

    @Override
    public SysUserVO getByMobile(String mobile) {
        SysUserEntity user = baseMapper.getByMobile(mobile);

        return SysUserConvert.INSTANCE.convert(user);
    }

    @Override
    public void updatePassword(Long id, String newPassword) {
        // 修改密码
        SysUserEntity user = getById(id);
        user.setPassword(newPassword);

        updateById(user);
    }

    @Override
    public PageResult<SysUserVO> roleUserPage(SysRoleUserQuery query) {
        // 查询参数
        Map<String, Object> params = getParams(query);
        params.put("roleId", query.getRoleId());

        // 分页查询
        IPage<SysUserEntity> page = getPage(query);
        params.put(BtoConstant.PAGE, page);

        // 数据列表
        List<SysUserEntity> list = baseMapper.getRoleUserList(params);

        return new PageResult<>(SysUserConvert.INSTANCE.convertList(list), page.getTotal());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void importByExcel(MultipartFile file, String password) {

        ExcelUtils.readAnalysis(file, SysUserExcelVO.class, new ExcelFinishCallBack<SysUserExcelVO>() {
            @Override
            public void doAfterAllAnalysed(List<SysUserExcelVO> result) {
                saveUser(result);
            }

            @Override
            public void doSaveBatch(List<SysUserExcelVO> result) {
                saveUser(result);
            }

            private void saveUser(List<SysUserExcelVO> result) {
                ExcelUtils.parseDict(result);
                List<SysUserEntity> sysUserEntities = SysUserConvert.INSTANCE.convertListEntity(result);
                sysUserEntities.forEach(user -> {
                    user.setPassword(password);
                    user.setAvatar("https://qiniu.btosolarman.com/work-order-cloud/common/default_avatar.png");
                    user.setSuperAdmin(SuperAdminEnum.NO.getValue());
                });
                saveBatch(sysUserEntities);
            }
        });

    }

    @Override
    @SneakyThrows
    public void export() {
        List<SysUserEntity> list = list(Wrappers.lambdaQuery(SysUserEntity.class).eq(SysUserEntity::getSuperAdmin, SuperAdminEnum.NO.getValue()));
        List<SysUserExcelVO> userExcelVOS = SysUserConvert.INSTANCE.convert2List(list);
        transService.transBatch(userExcelVOS);
        // 写到浏览器打开
        ExcelUtils.excelExport(SysUserExcelVO.class, "system_user_excel" + DateUtils.format(new Date()), null, userExcelVOS);
    }

    @Override
    public List<RepairUserVO> getUserByIdList(List<Long> userIdList, List<Long> orgIdList) {
        if (CollUtil.isEmpty(userIdList)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<SysUserOrgEntity> userOrgWrapper = new LambdaQueryWrapper<>();
        userOrgWrapper
                .in(CollUtil.isNotEmpty(userIdList), SysUserOrgEntity::getUserId, userIdList)
                .in(CollUtil.isNotEmpty(orgIdList), SysUserOrgEntity::getOrgId, orgIdList);
        List<Long> users = sysUserOrgService.list(userOrgWrapper).stream().map(SysUserOrgEntity::getUserId).collect(Collectors.toList());
        if (CollUtil.isEmpty(users)) {
            return Collections.emptyList();
        }

        LambdaQueryWrapper<SysUserEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper
                .in(CollUtil.isNotEmpty(users), SysUserEntity::getId, users)
                .eq(SysUserEntity::getStatus, UserStatusEnum.ENABLED.getValue());

        List<SysUserEntity> list = this.list(wrapper);
        if (CollUtil.isEmpty(list)) {
            return Collections.emptyList();
        }
        return SysUserConvert.INSTANCE.convert3List(list);
    }

    @Override
    public List<RepairUserVO> repairUserByWorkId(Long workId) {
        AssertUtils.isNull(workId, "工单id");
        List<Long> userIdList = sysUserDao.getRepairIdByWorkId(workId);
        return getUserByIdList(userIdList, null);
    }


    @Override
    public List<RepairUserVO> companionUserByWorkId(Long workId) {
        AssertUtils.isNull(workId, "工单id");
        String json = sysUserDao.getCompanionByWorkId(workId);
        List<Long> userIdList = JSON.parseArray(json, Long.class);
        return getUserByIdList(userIdList, null);
    }



    @Override
    public List<RepairUserVO> repairByName(Integer dictValue) {
        List<Long> userIdList = getUserIdListByRoleId(dictValue);
        return this.getUserByIdList(userIdList, null);
    }

    @Override
    public Map<String, List<RepairUserVO>> listByRemarkWithCity(Integer dictValue) {
        List<Long> userIdList = null;
        if (Objects.isNull(dictValue)) {
            userIdList = baseMapper.selectList(null).stream().map(SysUserEntity::getId).collect(Collectors.toList());
        } else {
            userIdList = getUserIdListByRoleId(dictValue);
        }
        return this.getUserWithCityByIdList(userIdList, null);
    }

    private Map<String, List<RepairUserVO>> getUserWithCityByIdList(List<Long> userIdList, List<Long> orgIdList) {
        if (CollUtil.isEmpty(userIdList)) {
            return Collections.emptyMap();
        }
        LambdaQueryWrapper<SysUserOrgEntity> userOrgWrapper = new LambdaQueryWrapper<>();
        userOrgWrapper
                .in(CollUtil.isNotEmpty(userIdList), SysUserOrgEntity::getUserId, userIdList)
                .in(CollUtil.isNotEmpty(orgIdList), SysUserOrgEntity::getOrgId, orgIdList);
        List<Long> users = sysUserOrgService.list(userOrgWrapper).stream().map(SysUserOrgEntity::getUserId).collect(Collectors.toList());
        if (CollUtil.isEmpty(users)) {
            return Collections.emptyMap();
        }

        LambdaQueryWrapper<SysUserEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper
                .in(CollUtil.isNotEmpty(users), SysUserEntity::getId, users)
                .eq(SysUserEntity::getStatus, UserStatusEnum.ENABLED.getValue());

        List<SysUserEntity> list = this.list(wrapper);
        if (CollUtil.isEmpty(list)) {
            return Collections.emptyMap();
        }

        // 转换为 RepairUserVO 列表
        List<RepairUserVO> repairUserVOList = SysUserConvert.INSTANCE.convert3List(list);

        // 处理 city 为 null 的情况，并按照城市进行分组
        Map<String, List<RepairUserVO>> cityMap = repairUserVOList.stream()
                .collect(Collectors.groupingBy(user -> Optional.ofNullable(user.getCity()).orElse("未知")));

        return cityMap;
        // 转换为 RepairUserWithCityVO 列表

        // return cityMap.entrySet().stream()
        //         .map(entry -> new RepairUserWithCityVO(entry.getKey(), entry.getValue()))
        //         .collect(Collectors.toList());
    }

    private List<Long> getUserIdListByRoleId(Integer dictValue) {
        SysRoleQuery sysRoleQuery = new SysRoleQuery();
        String name = UserRemarkEnum.getNameByValue(dictValue);
        sysRoleQuery.setName(name);
        List<Long> repairRole = sysRoleService.getRepairRole(sysRoleQuery);
        if (CollUtil.isEmpty(repairRole)) {
            return Collections.emptyList();
        }
        return sysUserRoleService.getUserIdListByRoleId(repairRole);
    }

    @Override
    public List<RepairUserVO> getUserListByOrgId(List<Long> orgIdList) {
        UserDetail user = SecurityUser.getUser();
        List<Long> dataScopeList = user.getDataScopeList();
        if (!SuperAdminEnum.YES.getValue().equals(user.getSuperAdmin()) && !dataScopeList.containsAll(orgIdList)) {
            throw new ServerException("权限不足");
        }

        List<Long> userIdList = getUserIdListByRoleId(0);
        List<RepairUserVO> repairs = this.getUserByIdList(userIdList, orgIdList);
        List<Long> responsibleId = sysRoleService.lambdaQuery().like(SysRoleEntity::getName, RESPONSIBLE_NAME).list().stream().map(SysRoleEntity::getId).collect(Collectors.toList());

        if (CollUtil.isEmpty(repairs)) {
            return Collections.emptyList();
        }

        // 负责人标识 0：否   1：是
        repairs.forEach(repair -> {
            if (CollUtil.isNotEmpty(responsibleId)) {
                List<Long> roleIdList = sysUserRoleService.getRoleIdList(repair.getId());
                if (CollUtil.containsAny(roleIdList, responsibleId)) {
                    repair.setResponsibleFlag(1);
                } else {
                    repair.setResponsibleFlag(0);
                }
            }

            List<Long> userOrg = sysUserOrgService.getOrgIdList(repair.getId());
            repair.setOrgIds(userOrg);
        });
        return repairs;
    }

    @Override
    public List<OrgWithUserVO> getOrgWithUserByWorkId(Long workId) {
        List<Long> userIdList = sysUserDao.getRepairIdByWorkId(workId);
        List<RepairUserVO> userList = getUserByIdList(userIdList, null);

        List<SysOrgVO> orgList = sysOrgService.getList();
        if (CollUtil.isEmpty(orgList)) {
            return Collections.singletonList(new OrgWithUserVO(userList));
        }

        List<OrgWithUserVO> list = BeanUtil.copyToList(orgList, OrgWithUserVO.class);
        // if (CollUtil.isNotEmpty(userList)) {
        //     matchRepairUsers(list, userList);
        // }
        return list;
    }

    @Override
    public SysUserVO info() {
        UserDetail userDetail = SecurityUser.getUser();
        SysUserVO user = SysUserConvert.INSTANCE.convert(userDetail);
        user.setPostNames(userDetail.getPostNames());
        List<Long> roleIds = userDetail.getRoleIds();
        user.setRoleIdList(roleIds);
        List<Integer> roleType = new ArrayList<>();
        List<Long> maintains = sysRoleService.getIdsByName(UserRemarkEnum.MAINTAIN.getRemark());
        List<Long> repairs = sysRoleService.getIdsByName(UserRemarkEnum.REPAIR.getRemark());
        if (CollUtil.containsAny(roleIds, maintains) && CollUtil.isNotEmpty(maintains)) {
            roleType.add(UserRemarkEnum.MAINTAIN.getValue());
        }

        if (CollUtil.containsAny(roleIds, repairs) && CollUtil.isNotEmpty(maintains)) {
            roleType.add(UserRemarkEnum.REPAIR.getValue());
        }
        user.setRoleType(roleType);
        return user;
    }

    @Override
    public String getUserNameById(Long userId) {
        return baseMapper.getById(userId).getUsername();
    }

    @Override
    public Result<Map<Long, String>> getUsernameMapByIds(Set<Long> ids) {
        if (CollUtil.isEmpty(ids)) return Result.ok(Collections.emptyMap());
        List<SysUserEntity> users = baseMapper.selectList(
                new LambdaQueryWrapper<SysUserEntity>()
                        .in(SysUserEntity::getId, ids)
                        .eq(SysUserEntity::getDeleted, 0)
        );
        return Result.ok(users.stream()
                .collect(Collectors.toMap(SysUserEntity::getId, SysUserEntity::getUsername)));
    }

    @Override
    public List<RepairUserVO> getAllUserInfo() {
        List<SysUserEntity> list = sysUserDao.getAllUserInfo();
        return SysUserConvert.INSTANCE.convert3List(list);
    }

}
