package com.botong.system.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.botong.framework.common.page.PageResult;
import com.botong.framework.common.utils.DateUtils;
import com.botong.framework.common.utils.ExcelUtils;
import com.botong.framework.mybatis.service.impl.BaseServiceImpl;
import com.botong.system.convert.BillExpensesListConvert;
import com.botong.system.dao.BillExpensesListDao;
import com.botong.system.entity.BillExpensesListEntity;
import com.botong.system.query.BillExpensesListQuery;
import com.botong.system.service.BillExpensesListService;
import com.botong.system.vo.MaintenanceBillExcelVO;
import com.botong.system.vo.MaintenanceBillVO;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * 运维费账单
 *
 * <AUTHOR>
 * @since 1.0.0 2025-01-09
 */
@Service
@AllArgsConstructor
public class BillExpensesListServiceImpl extends BaseServiceImpl<BillExpensesListDao, BillExpensesListEntity> implements BillExpensesListService {

    private final BillExpensesListDao billExpensesListDao;

    @Override
    public PageResult<MaintenanceBillVO> page(BillExpensesListQuery query) {
        Page<MaintenanceBillVO> page = new Page<>(query.getPage(), query.getLimit());
        IPage<MaintenanceBillVO> iPage = billExpensesListDao.page(query, page);
        return new PageResult<>(iPage.getRecords(), page.getTotal());
    }

    @Override
    public void export() {
        IPage<MaintenanceBillVO> iPage = billExpensesListDao.page(new BillExpensesListQuery(), new Page<>(0, Integer.MAX_VALUE));
        List<MaintenanceBillExcelVO> excelList = BillExpensesListConvert.INSTANCE.convertList(iPage.getRecords());
        // 写到浏览器打开
        ExcelUtils.excelExport(MaintenanceBillExcelVO.class, "博通运维账单" + DateUtils.format(new Date()), null, excelList);
    }


}