package com.botong.system.service;

import com.botong.api.module.system.vo.DictDataVO;
import com.botong.framework.common.page.PageResult;
import com.botong.framework.mybatis.service.BaseService;
import com.botong.system.entity.SysDictDataEntity;
import com.botong.system.query.SysDictDataQuery;
import com.botong.system.vo.SysDictDataVO;

import java.util.List;

/**
 * 数据字典
 */
public interface SysDictDataService extends BaseService<SysDictDataEntity> {

    PageResult<SysDictDataVO> page(SysDictDataQuery query);

    void save(SysDictDataVO vo);

    void update(SysDictDataVO vo);

    void delete(List<Long> idList);

    List<DictDataVO> getDictDataByTypeId(Long dictTypeId);
}