package com.botong.system.service;

import com.botong.framework.common.page.PageResult;
import com.botong.framework.mybatis.service.BaseService;
import com.botong.system.entity.SysLogOperateEntity;
import com.botong.system.query.SysLogOperateQuery;
import com.botong.system.vo.SysLogOperateVO;

/**
 * 操作日志
 */
public interface SysLogOperateService extends BaseService<SysLogOperateEntity> {

    PageResult<SysLogOperateVO> page(SysLogOperateQuery query);
}