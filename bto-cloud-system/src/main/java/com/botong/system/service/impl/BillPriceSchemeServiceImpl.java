package com.botong.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.botong.framework.mybatis.service.impl.BaseServiceImpl;
import com.botong.system.convert.BillPriceSchemeConvert;
import com.botong.system.dao.BillPriceSchemeDao;
import com.botong.system.entity.BillPriceSchemeEntity;
import com.botong.system.service.BillPriceSchemeService;
import com.botong.system.vo.BillPriceSchemeVO;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 运维费用方案表
 *
 * <AUTHOR>
 * @since 1.0.0 2025-01-11
 */
@Service
@AllArgsConstructor
public class BillPriceSchemeServiceImpl extends BaseServiceImpl<BillPriceSchemeDao, BillPriceSchemeEntity> implements BillPriceSchemeService {

    @Override
    public void save(BillPriceSchemeVO vo) {
        BillPriceSchemeEntity entity = BillPriceSchemeConvert.INSTANCE.convert(vo);
        baseMapper.insert(entity);
    }

    @Override
    public void update(BillPriceSchemeVO vo) {
        BillPriceSchemeEntity entity = BillPriceSchemeConvert.INSTANCE.convert(vo);

        updateById(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(List<Long> idList) {
        removeByIds(idList);
    }

    @Override
    public void removeBySchemeIds(List<String> schemeIds) {
        LambdaUpdateWrapper<BillPriceSchemeEntity> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(BillPriceSchemeEntity::getSchemeId, schemeIds);
        baseMapper.delete(updateWrapper);
    }

}