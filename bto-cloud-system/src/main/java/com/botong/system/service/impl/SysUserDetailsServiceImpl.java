package com.botong.system.service.impl;

import com.botong.framework.security.user.UserDetail;
import com.botong.system.dao.SysRoleDao;
import com.botong.system.dao.SysRoleDataScopeDao;
import com.botong.system.dao.SysUserRoleDao;
import com.botong.system.enums.DataScopeEnum;
import com.botong.system.enums.UserStatusEnum;
import com.botong.system.service.*;
import lombok.AllArgsConstructor;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * 用户 UserDetails 信息
 */
@Service
@AllArgsConstructor
public class SysUserDetailsServiceImpl implements SysUserDetailsService {
    private final SysMenuService sysMenuService;
    private final SysOrgService sysOrgService;
    private final SysRoleDao sysRoleDao;
    private final SysUserPostService sysUserPostService;
    private final SysUserOrgService sysUserOrgService;
    private final SysPostService sysPostService;
    private final SysUserRoleDao sysUserRoleDao;
    private final SysRoleDataScopeDao sysRoleDataScopeDao;

    @Override
    public UserDetails getUserDetails(UserDetail userDetail) {
        // 账号不可用
        if (userDetail.getStatus() == UserStatusEnum.DISABLE.getValue()) {
            userDetail.setEnabled(false);
        }
        // 机构列表
        List<Long> orgIdList = sysUserOrgService.getOrgIdList(userDetail.getId());
        userDetail.setOrgIds(orgIdList);

        // 数据权限范围
        userDetail.setDataScope(getDataScopeByUserId(userDetail.getId()));
        List<Long> dataScopeList = getDataScope(userDetail);
        userDetail.setDataScopeList(dataScopeList);

        // 用户权限列表
        Set<String> authoritySet = sysMenuService.getUserAuthority(userDetail);
        userDetail.setAuthoritySet(authoritySet);

        // 用户角色列表
        List<Long> roles = getRolesByUserId(userDetail.getId());
        userDetail.setRoleIds(roles);

        // 岗位列表
        List<Long> postIdList = sysUserPostService.getPostIdList(userDetail.getId());
        if (!postIdList.isEmpty()) {
            List<String> postNameList = sysPostService.getNamesByIds(postIdList);
            userDetail.setPostNames(postNameList);
        }


        return userDetail;
    }

    private List<Long> getDataScope(UserDetail userDetail) {
        Integer dataScope = getDataScopeByUserId(userDetail.getId());
        if (dataScope == null) {
            return new ArrayList<>();
        }

        if (dataScope.equals(DataScopeEnum.ALL.getValue())) {
            // 全部数据权限，则返回null
            return null;
        } else if (dataScope.equals(DataScopeEnum.ORG_AND_CHILD.getValue())) {
            // 本机构及子机构数据
            List<Long> dataScopeList = sysOrgService.getSubOrgIdList(userDetail.getOrgIds());
            // 自定义数据权限范围
            dataScopeList.addAll(sysRoleDataScopeDao.getDataScopeList(userDetail.getId()));

            return dataScopeList;
        } else if (dataScope.equals(DataScopeEnum.ORG_ONLY.getValue())) {
            // 本机构数据
            List<Long> dataScopeList = new ArrayList<>();
            dataScopeList.addAll(userDetail.getOrgIds());
            // 自定义数据权限范围
            dataScopeList.addAll(sysRoleDataScopeDao.getDataScopeList(userDetail.getId()));

            return dataScopeList;
        } else if (dataScope.equals(DataScopeEnum.CUSTOM.getValue())) {
            // 自定义数据权限范围
            return sysRoleDataScopeDao.getDataScopeList(userDetail.getId());
        }

        return new ArrayList<>();
    }

    private List<Long> getRolesByUserId(Long userId) {
        return sysUserRoleDao.getRoleIdList(userId);
    }

    private Integer getDataScopeByUserId(Long userId) {
        return sysRoleDao.getDataScopeByUserId(userId);
    }
}
