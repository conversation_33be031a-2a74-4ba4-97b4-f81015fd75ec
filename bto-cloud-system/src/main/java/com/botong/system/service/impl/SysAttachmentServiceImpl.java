package com.botong.system.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.botong.framework.common.page.PageResult;
import com.botong.framework.mybatis.service.impl.BaseServiceImpl;
import com.botong.system.convert.SysAttachmentConvert;
import com.botong.system.dao.SysAttachmentDao;
import com.botong.system.entity.SysAttachmentEntity;
import com.botong.system.entity.SysUserEntity;
import com.botong.system.query.SysAttachmentQuery;
import com.botong.system.service.SysAttachmentService;
import com.botong.system.vo.SysAttachmentVO;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 附件管理
 */
@Service
@AllArgsConstructor
public class SysAttachmentServiceImpl extends BaseServiceImpl<SysAttachmentDao, SysAttachmentEntity> implements SysAttachmentService {

    @Override
    public PageResult<SysAttachmentVO> page(SysAttachmentQuery query) {
        MPJLambdaWrapper<SysAttachmentEntity> wrapper = new MPJLambdaWrapper<>();
        wrapper.selectAll(SysAttachmentEntity.class)
                .select(SysUserEntity::getUsername)
                .leftJoin(SysUserEntity.class, SysUserEntity::getId, SysAttachmentEntity::getCreator)
                .eq(StrUtil.isNotBlank(query.getPlatform()), SysAttachmentEntity::getPlatform, query.getPlatform())
                .eq(ObjectUtil.isNotNull(query.getTypeId()), SysAttachmentEntity::getTypeId, query.getTypeId())
                .like(StrUtil.isNotBlank(query.getName()), SysAttachmentEntity::getName, query.getName())
                .orderByDesc(SysAttachmentEntity::getId);
        IPage<SysAttachmentVO> page = baseMapper.selectJoinPage(
                new Page<>(query.getPage(), query.getLimit()),
                SysAttachmentVO.class,
                wrapper
        );
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    private LambdaQueryWrapper<SysAttachmentEntity> getWrapper(SysAttachmentQuery query) {
        LambdaQueryWrapper<SysAttachmentEntity> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(StrUtil.isNotBlank(query.getPlatform()), SysAttachmentEntity::getPlatform, query.getPlatform());
        wrapper.eq(ObjectUtil.isNotNull(query.getTypeId()), SysAttachmentEntity::getTypeId, query.getTypeId());
        wrapper.like(StrUtil.isNotBlank(query.getName()), SysAttachmentEntity::getName, query.getName());
        wrapper.orderByDesc(SysAttachmentEntity::getId);
        return wrapper;
    }

    @Override
    public void save(SysAttachmentVO vo) {
        SysAttachmentEntity entity = SysAttachmentConvert.INSTANCE.convert(vo);

        baseMapper.insert(entity);
    }

    @Override
    public void update(SysAttachmentVO vo) {
        SysAttachmentEntity entity = SysAttachmentConvert.INSTANCE.convert(vo);

        updateById(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(List<Long> idList) {
        removeByIds(idList);
    }

}