package com.botong.system.service;

import com.botong.api.module.system.vo.RepairUserVO;
import com.botong.framework.common.page.PageResult;
import com.botong.framework.common.utils.Result;
import com.botong.framework.mybatis.service.BaseService;
import com.botong.system.entity.SysUserEntity;
import com.botong.system.query.SysRoleUserQuery;
import com.botong.system.query.SysUserQuery;
import com.botong.system.vo.OrgWithUserVO;
import com.botong.system.vo.SysUserVO;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 用户管理
 */
public interface SysUserService extends BaseService<SysUserEntity> {

    PageResult<SysUserVO> page(SysUserQuery query);

    void save(SysUserVO vo);

    void update(SysUserVO vo);

    void delete(List<Long> idList);

    SysUserVO getByMobile(String mobile);

    /**
     * 修改密码
     *
     * @param id          用户ID
     * @param newPassword 新密码
     */
    void updatePassword(Long id, String newPassword);

    /**
     * 分配角色，用户列表
     */
    PageResult<SysUserVO> roleUserPage(SysRoleUserQuery query);

    /**
     * 批量导入用户
     *
     * @param file     excel文件
     * @param password 密码
     */
    void importByExcel(MultipartFile file, String password);

    /**
     * 导出用户信息表格
     */
    void export();

    /**
     * 按id列表获取用户
     *
     * @param userIdList 用户id列表
     * @return {@link List }<{@link RepairUserVO }>
     * <AUTHOR>
     * @since 2023-08-23 11:58:31
     */
    List<RepairUserVO> getUserByIdList(List<Long> userIdList, List<Long> orgIdList);

    /**
     * 按照工单id查询维修人员
     *
     * @param workId 工单id
     * @return {@link List }<{@link RepairUserVO }>
     * <AUTHOR>
     * @since 2023-08-23 11:58:35
     */
    List<RepairUserVO> repairUserByWorkId(Long workId);

    /**
     * 按工作id列出同行人员
     *
     * @param workId 工作id
     * @return {@link List }<{@link RepairUserVO }>
     * <AUTHOR>
     * @since 2025-03-07 10:04:08
     */
    List<RepairUserVO> companionUserByWorkId(Long workId);


    List<RepairUserVO> repairByName(Integer dictValue);

    List<RepairUserVO> getUserListByOrgId(List<Long> orgIdList);

    List<OrgWithUserVO> getOrgWithUserByWorkId(Long workId);

    SysUserVO info();

    Map<String, List<RepairUserVO>> listByRemarkWithCity(Integer dictValue);

    String getUserNameById(Long userId);

    Result<Map<Long, String>> getUsernameMapByIds(Set<Long> allRepairIds);

    List<RepairUserVO> getAllUserInfo();
}
