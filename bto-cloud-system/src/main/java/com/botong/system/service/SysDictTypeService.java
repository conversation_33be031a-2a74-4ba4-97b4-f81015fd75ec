package com.botong.system.service;

import com.botong.api.module.system.vo.DictDataVO;
import com.botong.framework.common.page.PageResult;
import com.botong.framework.mybatis.service.BaseService;
import com.botong.system.entity.SysDictTypeEntity;
import com.botong.system.query.SysDictTypeQuery;
import com.botong.system.vo.SysDictTypeVO;
import com.botong.system.vo.SysDictVO;

import java.util.List;

/**
 * 数据字典
 */
public interface SysDictTypeService extends BaseService<SysDictTypeEntity> {

    PageResult<SysDictTypeVO> page(SysDictTypeQuery query);

    void save(SysDictTypeVO vo);

    void update(SysDictTypeVO vo);

    void delete(List<Long> idList);

    /**
     * 获取动态SQL数据
     */
    List<SysDictVO.DictData> getDictSql(Long id);

    /**
     * 获取全部字典列表
     */
    List<SysDictVO> getDictList();


    /**
     * 根据字典类型查询字典类型ID
     * @param dictType 字典类型
     * @return 字典类型ID
     */
    Long getDictIdByType(String dictType);

    /**
     * 根据字典类型查询动态sql列表
     * @param dictType
     * @return
     */
    List<DictDataVO> getDictSqlByTypeId(String dictType);
}