package com.botong.system.service;


import com.botong.framework.mybatis.service.BaseService;
import com.botong.system.entity.SysUserOrgEntity;

import java.util.List;

/**
 * 用户机构关系
 *
 * <AUTHOR> by zhb on 2023/9/19.
 */
public interface SysUserOrgService extends BaseService<SysUserOrgEntity> {

    /**
     * 保存或修改
     * @param userId      用户ID
     * @param orgIds      机构ID列表
     */
    void saveOrUpdate(Long userId, List<Long> orgIds);


    /**
     * 根据用户ID获取机构列表
     * @param userId 用户ID
     */
    List<Long> getOrgIdList(Long userId);

    /**
     * 根据用户id列表，删除用户机构关系
     * @param userIdList 用户id列表
     */
    void deleteByUserIdList(List<Long> userIdList);
}