package com.botong.system.service;

import com.botong.framework.common.page.PageResult;
import com.botong.framework.mybatis.service.BaseService;
import com.botong.system.entity.ElectricityBillSummaryEntity;
import com.botong.system.query.ElectricityBillSummaryQuery;
import com.botong.system.vo.ElectricityBillSummaryVO;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 上网电量电费管理
 *
 * <AUTHOR>
 * @since 2024-12-24
 */
public interface ElectricityBillSummaryService extends BaseService<ElectricityBillSummaryEntity> {

    PageResult<ElectricityBillSummaryVO> page(ElectricityBillSummaryQuery query);

    void save(ElectricityBillSummaryVO vo);

    void update(ElectricityBillSummaryVO vo);

    void delete(List<String> idList);

    void importByExcel(MultipartFile file);

    void exportToExcel(ElectricityBillSummaryQuery query);
}