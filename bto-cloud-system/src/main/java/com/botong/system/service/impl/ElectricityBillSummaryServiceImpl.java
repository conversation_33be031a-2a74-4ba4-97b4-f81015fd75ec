package com.botong.system.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.botong.framework.common.excel.ExcelFinishCallBack;
import com.botong.framework.common.exception.ServerException;
import com.botong.framework.common.page.PageResult;
import com.botong.framework.common.utils.DateUtils;
import com.botong.framework.common.utils.ExcelUtils;
import com.botong.framework.mybatis.service.impl.BaseServiceImpl;
import com.botong.system.convert.ElectricityBillSummaryConvert;
import com.botong.system.dao.ElectricityBillSummaryDao;
import com.botong.system.entity.ElectricityBillSummaryEntity;
import com.botong.system.query.ElectricityBillSummaryQuery;
import com.botong.system.service.ElectricityBillSummaryService;
import com.botong.system.vo.ElectricityBillSummaryExcelVO;
import com.botong.system.vo.ElectricityBillSummaryVO;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 上网电量电费管理
 *
 * <AUTHOR>
 * @since 2024-12-24
 */
@Service
@AllArgsConstructor
public class ElectricityBillSummaryServiceImpl extends BaseServiceImpl<ElectricityBillSummaryDao, ElectricityBillSummaryEntity> implements ElectricityBillSummaryService {

    @Override
    public PageResult<ElectricityBillSummaryVO> page(ElectricityBillSummaryQuery query) {
        IPage<ElectricityBillSummaryEntity> page = baseMapper.selectPage(getPage(query), getWrapper(query));

        return new PageResult<>(ElectricityBillSummaryConvert.INSTANCE.convertList(page.getRecords()), page.getTotal());
    }

    private LambdaQueryWrapper<ElectricityBillSummaryEntity> getWrapper(ElectricityBillSummaryQuery query) {
        LambdaQueryWrapper<ElectricityBillSummaryEntity> wrapper = Wrappers.lambdaQuery();
        wrapper
                .like(StrUtil.isNotBlank(query.getMeterId()), ElectricityBillSummaryEntity::getMeterId, query.getMeterId())
                .like(StrUtil.isNotBlank(query.getFactoryName()), ElectricityBillSummaryEntity::getFactoryName, query.getFactoryName())
                .like(StrUtil.isNotBlank(query.getSettlementPeriod()), ElectricityBillSummaryEntity::getSettlementPeriod, query.getSettlementPeriod())
                .like(StrUtil.isNotBlank(query.getAdministrator()), ElectricityBillSummaryEntity::getAdministrator, query.getAdministrator())
        ;
        return wrapper;
    }

    @Override
    public void save(ElectricityBillSummaryVO vo) {
        ElectricityBillSummaryEntity entity = ElectricityBillSummaryConvert.INSTANCE.convert(vo);

        baseMapper.insert(entity);
    }

    @Override
    public void update(ElectricityBillSummaryVO vo) {
        ElectricityBillSummaryEntity entity = ElectricityBillSummaryConvert.INSTANCE.convert(vo);

        updateById(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(List<String> idList) {
        removeByIds(idList);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void importByExcel(MultipartFile file) {

        ExcelUtils.readAnalysis(file, 1, ElectricityBillSummaryExcelVO.class, new ExcelFinishCallBack<ElectricityBillSummaryExcelVO>() {
            @Override
            public void doAfterAllAnalysed(List<ElectricityBillSummaryExcelVO> result) {
                saveElectricityBillSummaryEntity(result);
            }

            @Override
            public void doSaveBatch(List<ElectricityBillSummaryExcelVO> result) {
                saveElectricityBillSummaryEntity(result);
            }

            private void saveElectricityBillSummaryEntity(List<ElectricityBillSummaryExcelVO> result) {
                // 过滤掉发电量或含税电费为0的记录
                List<ElectricityBillSummaryExcelVO> filteredResult = result.stream()
                        .filter(vo -> vo.getElectricity().compareTo(BigDecimal.ZERO) != 0 || vo.getElectricityWithTax().compareTo(BigDecimal.ZERO) != 0)
                        .distinct()
                        .collect(Collectors.toList());

                if (CollUtil.isEmpty(filteredResult)) {
                    return;
                }

                // 校验 settlementPeriod 的格式
                for (ElectricityBillSummaryExcelVO vo : filteredResult) {
                    if (!DateUtils.isValidDate(vo.getSettlementPeriod(), "yyyy年MM月")) {
                        throw new ServerException("数据格式错误，日期应为 yyyy年MM月 格式: " + vo.getSettlementPeriod());
                    }
                }
                // 将Excel数据转换为Entity对象
                List<ElectricityBillSummaryEntity> entities = ElectricityBillSummaryConvert.INSTANCE.convertExcel2List(filteredResult);

                // 构建查询条件
                LambdaQueryWrapper<ElectricityBillSummaryEntity> wrapper = Wrappers.lambdaQuery();
                for (ElectricityBillSummaryEntity entity : entities) {
                    wrapper.or(w -> w.eq(ElectricityBillSummaryEntity::getMeterId, entity.getMeterId())
                            .eq(ElectricityBillSummaryEntity::getSettlementPeriod, entity.getSettlementPeriod()));
                }

                // 批量查询数据库中已有的记录
                List<ElectricityBillSummaryEntity> list = list(wrapper);
                Map<String, ElectricityBillSummaryEntity> existingEntitiesMap = list.stream()
                        .collect(Collectors.toMap(
                                entity -> entity.getMeterId() + "_" + entity.getSettlementPeriod(),
                                Function.identity()
                        ));

                // 分为需要更新的和新的数据
                List<ElectricityBillSummaryEntity> entitiesToUpdate = new ArrayList<>();
                List<ElectricityBillSummaryEntity> entitiesToInsert = new ArrayList<>();

                for (ElectricityBillSummaryEntity entity : entities) {
                    String key = entity.getMeterId() + "_" + entity.getSettlementPeriod();
                    ElectricityBillSummaryEntity existingEntity = existingEntitiesMap.get(key);
                    // 如果已有记录，且需要更新的字段（为0的字段）
                    if (existingEntity != null) {
                        // 需要更新
                        entitiesToUpdate.add(entity);
                    } else {
                        // 新记录
                        entitiesToInsert.add(entity);
                    }
                }

                // 批量更新数据库中的记录
                if (!entitiesToUpdate.isEmpty()) {
                    for (ElectricityBillSummaryEntity item : entitiesToUpdate) {
                        wrapper.clear();
                        wrapper
                                .eq(ElectricityBillSummaryEntity::getMeterId,item.getMeterId())
                                .eq(ElectricityBillSummaryEntity::getSettlementPeriod,item.getSettlementPeriod());
                        update(item,wrapper);
                    }
                }

                // 批量插入新的记录
                if (!entitiesToInsert.isEmpty()) {
                    saveBatch(entitiesToInsert);
                }
            }
        });
    }


    @Override
    public void exportToExcel(ElectricityBillSummaryQuery query) {
        query.setPage(-1);
        query.setLimit(-1);
        IPage<ElectricityBillSummaryEntity> page = baseMapper.selectPage(getPage(query), getWrapper(query));

        List<ElectricityBillSummaryExcelVO> list = ElectricityBillSummaryConvert.INSTANCE.convertList2Excel(page.getRecords());
        // 写到浏览器打开
        ExcelUtils.excelExport(ElectricityBillSummaryExcelVO.class, "bill_bto_electricity_summary_excel" + DateUtils.format(new Date()), null, list);

    }

    private String extractDuplicateEntry(String message) {
        try {
            int start = message.indexOf("'") + 1;
            int end = message.indexOf("'", start);
            return message.substring(start, end);
        } catch (Exception ex) {
            // 如果解析失败，返回原始消息
            return "未知记录";
        }
    }
}