package com.botong.system.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.botong.framework.mybatis.service.impl.BaseServiceImpl;
import com.botong.system.dao.SysUserOrgDao;
import com.botong.system.entity.SysUserOrgEntity;
import com.botong.system.service.SysUserOrgService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 用户机构关系
 * <AUTHOR> by zhb on 2023/9/19.
 */
@Service
@AllArgsConstructor
public class SysUserOrgServiceImpl extends BaseServiceImpl<SysUserOrgDao, SysUserOrgEntity> implements SysUserOrgService {

    @Override
    public void saveOrUpdate(Long userId, List<Long> orgIds) {
        if (CollUtil.isEmpty(orgIds)) {
            this.lambdaUpdate().eq(SysUserOrgEntity::getUserId, userId).remove();
            return;
        }
        // 数据库机构ID列表
        List<Long> dbOrgIdList = getOrgIdList(userId);

        // 需要新增的角色ID
        Collection<Long> insertOrgIdList = CollUtil.subtract(orgIds, dbOrgIdList);
        if (CollUtil.isNotEmpty(insertOrgIdList)) {
            List<SysUserOrgEntity> orgList = insertOrgIdList.stream().map(orgId -> {
                SysUserOrgEntity entity = new SysUserOrgEntity();
                entity.setUserId(userId);
                entity.setOrgId(orgId);
                return entity;
            }).collect(Collectors.toList());

            // 批量新增
            saveBatch(orgList);
        }

        // 需要删除的角色ID
        Collection<Long> deleteOrgIdList = CollUtil.subtract(dbOrgIdList, orgIds);
        if (CollUtil.isNotEmpty(deleteOrgIdList)) {
            LambdaQueryWrapper<SysUserOrgEntity> queryWrapper = new LambdaQueryWrapper<>();
            remove(queryWrapper.eq(SysUserOrgEntity::getUserId, userId).in(SysUserOrgEntity::getOrgId, deleteOrgIdList));
        }
    }

    @Override
    public List<Long> getOrgIdList(Long userId) {
        LambdaQueryWrapper<SysUserOrgEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(SysUserOrgEntity::getOrgId);
        wrapper.eq(SysUserOrgEntity::getUserId, userId);
        return baseMapper.selectList(wrapper).stream().map(SysUserOrgEntity::getOrgId).collect(Collectors.toList());
    }

    @Override
    public void deleteByUserIdList(List<Long> userIdList) {
        remove(new LambdaQueryWrapper<SysUserOrgEntity>().in(SysUserOrgEntity::getUserId, userIdList));
    }
}