package com.botong.system.service.impl;

import com.botong.framework.mybatis.service.impl.BaseServiceImpl;
import com.botong.system.dao.YxElectricBillDao;
import com.botong.system.entity.ElectricBill;
import com.botong.system.service.YxElectricityBillService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> by zhb on 2024/12/31.
 */

@Service
@Transactional
public class YxElectricityBillServiceImpl extends BaseServiceImpl<YxElectricBillDao, ElectricBill> implements YxElectricityBillService {

    @Autowired
    private YxElectricBillDao yxElectricBillDao;

    @Override
    public String saveBills(List<ElectricBill> electricBills) {
        // 先做去重与金额处理
        electricBills = handleBills(electricBills);

        List<ElectricBill> oldElectricBills = yxElectricBillDao.selectAllElectricBill();

        Map<String, ElectricBill> oldBillMap = new HashMap<>();
        if (!oldElectricBills.isEmpty()) {
            for (ElectricBill bill : oldElectricBills) {
                String key = bill.getEntry_number() + "-" + bill.getBill_time();
                oldBillMap.put(key, bill);
            }
        }

        List<ElectricBill> billsToUpdate = new ArrayList<>();
        List<ElectricBill> billsToInsert = new ArrayList<>();

        for (ElectricBill newBill : electricBills) {
            String key = newBill.getEntry_number() + "-" + newBill.getBill_time();
            ElectricBill existingBill = oldBillMap.get(key);

            if (existingBill != null) {
                billsToUpdate.add(existingBill);
            } else {
                // 新增记录
                billsToInsert.add(newBill);
            }
        }

        if (!billsToUpdate.isEmpty()) {
            this.updateBatchById(billsToUpdate); // 批量更新
        }

        if (!billsToInsert.isEmpty()) {
            this.saveBatch(billsToInsert); // 批量插入
        }

        return "新增了 " + (billsToInsert.size()) + " 条记录，更新了 " + (billsToUpdate.size()) + " 条记录。";
    }


    public List<ElectricBill> handleBills(List<ElectricBill> electricBills) {
        Map<String, ElectricBill> mergedMap = new HashMap<>();
        ArrayList<ElectricBill> cardSource = new ArrayList<>();
        for (ElectricBill bill : electricBills) {
            BigDecimal amount = bill.getAmount();
            if (amount == null || amount.compareTo(BigDecimal.ZERO) == 0) {
                bill.setAmount(bill.getAmount_after_refund());
            }
            if (bill.getAmount() != null) {
                bill.setAmount(bill.getAmount().setScale(2, RoundingMode.HALF_UP));
            }
            if (bill.getAmount_after_refund() != null) {
                bill.setAmount_after_refund(bill.getAmount_after_refund().setScale(2, RoundingMode.HALF_UP));
            }
            if (bill.getSource().equals("二类卡南方")) {
                cardSource.add(bill);
                continue;
            }
            String key = bill.getEntry_number() + "-" + bill.getBill_time();
            if (mergedMap.containsKey(key)) {
                ElectricBill existingBill = mergedMap.get(key);
                // 判断电费渠道：自然人、项目公司备案
                if (bill.getSource().equals("项目公司备案(户用)")) {
                    BigDecimal existingBillAmount = existingBill.getAmount();
                    // bill和existingBill比较，得出较小值，并且看看较小值是否小于1，如果较小值小于1，则舍去该记录
                    BigDecimal minAmount = amount.min(existingBillAmount);
                    if (minAmount.compareTo(BigDecimal.ONE) < 0) {
                        // 判断是否相等
                        if (minAmount.compareTo(existingBillAmount) == 0) {
                            // 移除旧的，加入新的
                            mergedMap.remove(key);
                            mergedMap.put(key, bill);
                        }
                    } else {
                        // 较小值大于1
                        // 查询数据库的值，是否相近
                        BigDecimal dbValue = new BigDecimal(yxElectricBillDao.selectElectricBillByMonth(bill.getBill_time(), bill.getEntry_number()));
                        // 获取差值
                        BigDecimal diffAmount = dbValue.subtract(amount).abs();
                        if (diffAmount.compareTo(BigDecimal.ONE) < 0) {
                            // 移除旧的，加入新的
                            mergedMap.remove(key);
                            mergedMap.put(key, bill);
                        }
                    }
                }
            } else {
                mergedMap.put(key, bill);
            }
        }
        // 对二类南方卡类型进行特殊处理
        for (ElectricBill bill : processElectricBills(cardSource)) {
            mergedMap.put(bill.getEntry_number() + "-" + bill.getBill_time(), bill);
        }

        return new ArrayList<>(mergedMap.values());
    }

    public List<ElectricBill> processElectricBills(List<ElectricBill> cardSource) {
        // 按 entry_number 分组
        Map<String, List<ElectricBill>> grouped = cardSource.stream()
                .collect(Collectors.groupingBy(ElectricBill::getEntry_number));

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy年M月");

        for (Map.Entry<String, List<ElectricBill>> entry : grouped.entrySet()) {
            List<ElectricBill> bills = entry.getValue();

            // 判断是否存在多个相同 bill_time（冲突）
            Set<String> uniqueBillTimes = bills.stream()
                    .map(ElectricBill::getBill_time)
                    .collect(Collectors.toSet());

            if (uniqueBillTimes.size() == 1 && bills.size() > 1) {
                // 数据库查询并网日期
                LocalDate gridDate = yxElectricBillDao.getPlantCreateTime(entry.getKey());
                // 从并网日期的下一个月开始顺延
                LocalDate current = gridDate.plusMonths(1);
                // 对每条记录赋值新的 bill_time
                for (ElectricBill bill : bills) {
                    bill.setBill_time(current.format(formatter));
                    current = current.plusMonths(1);
                }
            }
        }

        return cardSource;

    }
}