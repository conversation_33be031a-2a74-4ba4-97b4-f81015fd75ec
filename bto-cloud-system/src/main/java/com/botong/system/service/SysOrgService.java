package com.botong.system.service;

import com.botong.api.module.workorder.entity.OrgBaseEntity;
import com.botong.framework.mybatis.service.BaseService;
import com.botong.system.entity.SysOrgEntity;
import com.botong.system.vo.SysOrgVO;

import java.util.List;

/**
 * 机构管理
 */
public interface SysOrgService extends BaseService<SysOrgEntity> {

    List<SysOrgVO> getList();

    void save(SysOrgVO vo);

    String saveReturnId(SysOrgVO vo);

    void update(SysOrgVO vo);

    void delete(Long id);

    /**
     * 根据机构ID，获取子机构ID列表(包含本机构ID)
     * @param id   机构ID
     */
    List<Long> getSubOrgIdList(Long id);

    /**
     * 根据机构ID，获取子机构ID列表(包含本机构ID)
     * @param ids   机构IDS
     */
    List<Long> getSubOrgIdList(List<Long> ids);

    /**
     * 根据组织名称获取组织id
     * @param name 组织名称
     * @return 组织id集合
     */
    Long getOrgIdByName(String name);

    /**
     * 根据组织ID获取组织名称
     * @param ids 组织ids
     * @return 组织名称集合
     */
    List<String> getOrgNamesByIds(List<Long> ids);

    List<OrgBaseEntity> getAllOrg();
}