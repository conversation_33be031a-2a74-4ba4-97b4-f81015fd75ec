package com.botong.system.service;

import com.botong.framework.common.page.PageResult;
import com.botong.framework.mybatis.service.BaseService;
import com.botong.system.entity.BillPriceMaintenanceEntity;
import com.botong.system.query.BillPriceMaintenanceQuery;
import com.botong.system.vo.BillPriceMaintenanceVO;

import java.util.List;

/**
 * 运维费单价
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-01-09
 */
public interface BillPriceMaintenanceService extends BaseService<BillPriceMaintenanceEntity> {

    PageResult<BillPriceMaintenanceVO> page(BillPriceMaintenanceQuery query);

    void save(BillPriceMaintenanceVO vo);

    void update(BillPriceMaintenanceVO vo);

    void delete(List<Long> idList);
}