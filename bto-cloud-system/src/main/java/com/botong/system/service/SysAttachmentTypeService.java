package com.botong.system.service;

import com.botong.framework.common.page.PageResult;
import com.botong.framework.mybatis.service.BaseService;
import com.botong.system.entity.SysAttachmentTypeEntity;
import com.botong.system.query.SysAttachmentQuery;
import com.botong.system.query.SysAttachmentTypeQuery;
import com.botong.system.vo.SysAttachmentTypeVO;
import com.botong.system.vo.SysAttachmentVO;

import java.util.List;

/**
 * 附件分类管理
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-03-04
 */
public interface SysAttachmentTypeService extends BaseService<SysAttachmentTypeEntity> {

    PageResult<SysAttachmentTypeVO> page(SysAttachmentTypeQuery query);

    void save(SysAttachmentTypeVO vo);

    void update(SysAttachmentTypeVO vo);

    void delete(List<Long> idList);

    PageResult<SysAttachmentVO> getAttachmentByTypeId(SysAttachmentQuery query);
}