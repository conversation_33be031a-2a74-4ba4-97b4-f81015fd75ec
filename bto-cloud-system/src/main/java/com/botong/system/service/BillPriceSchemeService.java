package com.botong.system.service;

import com.botong.framework.mybatis.service.BaseService;
import com.botong.system.entity.BillPriceSchemeEntity;
import com.botong.system.vo.BillPriceSchemeVO;

import java.util.List;

/**
 * 运维费用方案表
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-01-11
 */
public interface BillPriceSchemeService extends BaseService<BillPriceSchemeEntity> {

    void save(BillPriceSchemeVO vo);

    void update(BillPriceSchemeVO vo);

    void delete(List<Long> idList);

    void removeBySchemeIds(List<String> schemeIds);
}