package com.botong.system.service.impl;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.botong.framework.common.page.PageResult;
import com.botong.framework.mybatis.service.impl.BaseServiceImpl;
import com.botong.system.convert.BillPriceMaintenanceConvert;
import com.botong.system.convert.BillPriceSchemeConvert;
import com.botong.system.dao.BillPriceMaintenanceDao;
import com.botong.system.entity.BillPriceMaintenanceEntity;
import com.botong.system.entity.BillPriceSchemeEntity;
import com.botong.system.query.BillPriceMaintenanceQuery;
import com.botong.system.service.BillPriceMaintenanceService;
import com.botong.system.service.BillPriceSchemeService;
import com.botong.system.vo.BillPriceMaintenanceVO;
import com.botong.system.vo.BillPriceSchemeVO;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 运维费单价
 *
 * <AUTHOR>
 * @since 1.0.0 2025-01-09
 */
@Service
@AllArgsConstructor
public class BillPriceMaintenanceServiceImpl extends BaseServiceImpl<BillPriceMaintenanceDao, BillPriceMaintenanceEntity> implements BillPriceMaintenanceService {

    private final BillPriceSchemeService billPriceSchemeService;

    @Override
    public PageResult<BillPriceMaintenanceVO> page(BillPriceMaintenanceQuery query) {
        Page<BillPriceMaintenanceVO> page = new Page<>(query.getPage(), query.getLimit());
        IPage<BillPriceMaintenanceVO> iPage = baseMapper.page(query, page);
        return new PageResult<>(buildStructure(iPage.getRecords()), page.getTotal());
    }

    private LambdaQueryWrapper<BillPriceMaintenanceEntity> getWrapper(BillPriceMaintenanceQuery query) {
        LambdaQueryWrapper<BillPriceMaintenanceEntity> wrapper = Wrappers.lambdaQuery();

        return wrapper;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void save(BillPriceMaintenanceVO vo) {
        BillPriceMaintenanceEntity entity = BillPriceMaintenanceConvert.INSTANCE.convert(vo);
        entity.setSchemeId(vo.getInvestorId() + DateUtil.format(new Date(), "yyyyMMdd"));
        baseMapper.insert(entity);
        List<BillPriceSchemeVO> billPriceScheme = vo.getBillPriceScheme();
        if (!billPriceScheme.isEmpty()) {
            List<BillPriceSchemeEntity> billPriceSchemeEntities = BillPriceSchemeConvert.INSTANCE.convertList(billPriceScheme);
            billPriceSchemeService.saveBatch(addSchemeId(billPriceSchemeEntities));
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(BillPriceMaintenanceVO vo) {
        List<BillPriceSchemeVO> billPriceScheme = vo.getBillPriceScheme();
        if (!billPriceScheme.isEmpty()) {
            List<String> schemeIds = billPriceScheme.stream().map(BillPriceSchemeVO::getSchemeId).collect(Collectors.toList());
            billPriceSchemeService.removeBySchemeIds(schemeIds);
            List<BillPriceSchemeEntity> billPriceSchemeEntities = BillPriceSchemeConvert.INSTANCE.convertList(billPriceScheme);
            billPriceSchemeService.saveBatch(addSchemeId(billPriceSchemeEntities));
        }
        BillPriceMaintenanceEntity entity = BillPriceMaintenanceConvert.INSTANCE.convert(vo);
        entity.setSchemeId(vo.getSchemeId() + DateUtil.format(new Date(), "yyyyMMdd"));
        updateById(entity);
    }

    public List<BillPriceSchemeEntity> addSchemeId(List<BillPriceSchemeEntity> billPriceSchemeEntities) {
        for (BillPriceSchemeEntity billPriceSchemeEntity : billPriceSchemeEntities) {
            billPriceSchemeEntity.setSchemeId(billPriceSchemeEntity.getInvestorId() + DateUtil.format(new Date(), "yyyyMMdd"));
        }
        return billPriceSchemeEntities;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(List<Long> idList) {
        removeByIds(idList);
        List<String> collect = this.listByIds(idList).stream().map(BillPriceMaintenanceEntity::getSchemeId).collect(Collectors.toList());
        billPriceSchemeService.removeBySchemeIds(collect);
    }

    /**
     * 构建父子结构
     */
    public List<BillPriceMaintenanceVO> buildStructure(List<BillPriceMaintenanceVO> list) {
        return list.stream()
                .collect(Collectors.groupingBy(item -> Objects.toString(item.getSchemeId(), "unknown")))
                .entrySet().stream()
                .map(entry -> {
                    String schemeId = entry.getKey();
                    List<BillPriceMaintenanceVO> maintenanceList = entry.getValue();

                    BillPriceMaintenanceVO dto = new BillPriceMaintenanceVO();
                    BillPriceMaintenanceVO firstRecord = maintenanceList.get(0);

                    dto.setId(firstRecord.getId());
                    dto.setInvestorId(firstRecord.getInvestorId());
                    dto.setSchemeId(schemeId);
                    dto.setStageStart(firstRecord.getStageStart());
                    dto.setStageEnd(firstRecord.getStageEnd());

                    List<BillPriceSchemeVO> childList = maintenanceList.stream()
                            .map(maintenance -> {
                                BillPriceSchemeVO schemeDTO = new BillPriceSchemeVO();
                                schemeDTO.setParentId(schemeId);
                                schemeDTO.setStageFrom(maintenance.getStageFrom());
                                schemeDTO.setStageTo(maintenance.getStageTo());
                                schemeDTO.setPrice(maintenance.getPrice());
                                schemeDTO.setProvince(maintenance.getProvince());
                                schemeDTO.setCity(maintenance.getCity());
                                schemeDTO.setMaintenancePhase(maintenance.getStageFrom() + "~" + maintenance.getStageTo());
                                return schemeDTO;
                            })
                            .collect(Collectors.toList());

                    dto.setBillPriceScheme(childList);
                    return dto;
                })
                .collect(Collectors.toList());
    }

}


