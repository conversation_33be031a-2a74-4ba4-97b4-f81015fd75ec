package com.botong.system.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.botong.api.module.workorder.entity.OrgBaseEntity;
import com.botong.framework.common.constant.BtoConstant;
import com.botong.framework.common.exception.ServerException;
import com.botong.framework.common.utils.TreeUtils;
import com.botong.framework.mybatis.service.impl.BaseServiceImpl;
import com.botong.system.convert.SysOrgConvert;
import com.botong.system.dao.SysOrgDao;
import com.botong.system.dao.SysUserDao;
import com.botong.system.entity.SysOrgEntity;
import com.botong.system.entity.SysUserEntity;
import com.botong.system.service.SysOrgService;
import com.botong.system.vo.SysOrgVO;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 机构管理
 */
@Service
@AllArgsConstructor
public class SysOrgServiceImpl extends BaseServiceImpl<SysOrgDao, SysOrgEntity> implements SysOrgService {
    private final SysUserDao sysUserDao;
    private final SysOrgDao sysOrgDao;

    @Override
    public List<SysOrgVO> getList() {
        Map<String, Object> params = new HashMap<>();

        // 数据权限
        params.put(BtoConstant.DATA_SCOPE, getDataScope("t1", "id"));

        // 机构列表
        List<SysOrgEntity> entityList = baseMapper.getList(params);

        return TreeUtils.build(SysOrgConvert.INSTANCE.convertList(entityList));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void save(SysOrgVO vo) {
        SysOrgEntity entity = SysOrgConvert.INSTANCE.convert(vo);

        baseMapper.insert(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(SysOrgVO vo) {
        SysOrgEntity entity = SysOrgConvert.INSTANCE.convert(vo);

        // 上级机构不能为自身
        if (entity.getId().equals(entity.getPid())) {
            throw new ServerException("上级机构不能为自身");
        }

        // 上级机构不能为下级
        List<Long> subOrgList = getSubOrgIdList(entity.getId());
        if (subOrgList.contains(entity.getPid())) {
            throw new ServerException("上级机构不能为下级");
        }

        updateById(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Long id) {
        // 判断是否有子机构
        long orgCount = count(new QueryWrapper<SysOrgEntity>().eq("pid", id));
        if (orgCount > 0) {
            throw new ServerException("请先删除子机构");
        }

        // 判断机构下面是否有用户
        long userCount = sysUserDao.selectCount(new QueryWrapper<SysUserEntity>().eq("org_id", id));
        if (userCount > 0) {
            throw new ServerException("机构下面有用户，不能删除");
        }

        // 删除
        removeById(id);
    }

    @Override
    public List<Long> getSubOrgIdList(Long id) {
        // 所有机构的id、pid列表
        List<SysOrgEntity> orgList = baseMapper.getIdAndPidList();

        // 递归查询所有子机构ID列表
        List<Long> subIdList = new ArrayList<>();
        getTree(id, orgList, subIdList);

        // 本机构也添加进去
        subIdList.add(id);

        return subIdList;
    }

    @Override
    public List<Long> getSubOrgIdList(List<Long> ids) {
        ArrayList<Long> subIdList = new ArrayList<>();
        ids.forEach(id -> {
            List<Long> subOrgIdList = getSubOrgIdList(id);
            subIdList.addAll(subOrgIdList);
        });
        return subIdList;
    }

    @Override
    public Long getOrgIdByName(String name) {
        LambdaQueryWrapper<SysOrgEntity> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(SysOrgEntity::getName, name);
        wrapper.last("limit 1");
        SysOrgEntity sysOrgEntity = baseMapper.selectOne(wrapper);
        if (Objects.isNull(sysOrgEntity)) {
            return 0L;
        }
        return sysOrgEntity.getId();
    }

    @Override
    public List<String> getOrgNamesByIds(List<Long> ids) {
        if (CollUtil.isNotEmpty(ids)) {
            LambdaQueryWrapper<SysOrgEntity> wrapper = Wrappers.lambdaQuery();
            wrapper.select(SysOrgEntity::getName);
            wrapper.in(SysOrgEntity::getId, ids);
            return baseMapper.selectList(wrapper).stream().map(SysOrgEntity::getName).collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    @Override
    public List<OrgBaseEntity> getAllOrg() {
        return sysOrgDao.getAllOrg();
    }

    private LambdaQueryWrapper<SysOrgEntity> getWrapper(String name) {
        LambdaQueryWrapper<SysOrgEntity> wrapper = Wrappers.lambdaQuery();
        return wrapper;
    }

    private void getTree(Long id, List<SysOrgEntity> orgList, List<Long> subIdList) {
        for (SysOrgEntity org : orgList) {
            if (org.getPid().equals(id)) {
                getTree(org.getId(), orgList, subIdList);
                subIdList.add(org.getId());
            }
        }
    }

}