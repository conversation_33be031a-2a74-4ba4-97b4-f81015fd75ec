package com.botong.system.service;

import com.botong.framework.common.page.PageResult;
import com.botong.framework.mybatis.service.BaseService;
import com.botong.system.entity.BillExpensesListEntity;
import com.botong.system.query.BillExpensesListQuery;
import com.botong.system.vo.MaintenanceBillVO;

/**
 * 运维费账单
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-01-09
 */
public interface BillExpensesListService extends BaseService<BillExpensesListEntity> {

    PageResult<MaintenanceBillVO> page(BillExpensesListQuery query);

    void export();
}