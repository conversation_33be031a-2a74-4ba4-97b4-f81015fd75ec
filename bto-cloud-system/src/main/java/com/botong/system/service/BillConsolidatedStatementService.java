package com.botong.system.service;

import com.botong.framework.mybatis.service.BaseService;
import com.botong.system.entity.BillConsolidatedStatementEntity;
import com.botong.system.query.BillConsolidatedStatementQuery;
import com.botong.system.vo.CombinedBillPageVO;

/**
 * 电费账单
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-01-04
 */
public interface BillConsolidatedStatementService extends BaseService<BillConsolidatedStatementEntity> {

    CombinedBillPageVO page(BillConsolidatedStatementQuery query);

    void export(BillConsolidatedStatementQuery query);

    void updateDataBases();

}