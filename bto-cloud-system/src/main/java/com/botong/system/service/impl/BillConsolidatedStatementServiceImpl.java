package com.botong.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.botong.framework.common.page.PageResult;
import com.botong.framework.common.utils.DateUtils;
import com.botong.framework.common.utils.ExcelUtils;
import com.botong.framework.mybatis.service.impl.BaseServiceImpl;
import com.botong.system.convert.BillConsolidatedStatementConvert;
import com.botong.system.dao.BillConsolidatedStatementDao;
import com.botong.system.entity.BillConsolidatedStatementEntity;
import com.botong.system.query.BillConsolidatedStatementQuery;
import com.botong.system.service.BillConsolidatedStatementService;
import com.botong.system.vo.BillConsolidatedStatementExcelVO;
import com.botong.system.vo.BillConsolidatedStatementVO;
import com.botong.system.vo.CombinedBillPageVO;
import com.botong.system.vo.ElectricitySummaryResultVO;
import com.fhs.trans.service.impl.TransService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * 电费账单
 *
 * <AUTHOR>
 * @since 1.0.0 2025-01-04
 */
@Service
@AllArgsConstructor
public class BillConsolidatedStatementServiceImpl extends BaseServiceImpl<BillConsolidatedStatementDao, BillConsolidatedStatementEntity> implements BillConsolidatedStatementService {

    private final BillConsolidatedStatementDao billConsolidatedStatementDao;

    private final TransService transService;

    @Override
    public CombinedBillPageVO page(BillConsolidatedStatementQuery query) {
        query.setPage((query.getPage() - 1) * query.getLimit());
        List<BillConsolidatedStatementVO> list = billConsolidatedStatementDao.selectList(query);
        ElectricitySummaryResultVO result = billConsolidatedStatementDao.getStatisticalResult(query);
        return new CombinedBillPageVO(new PageResult<>(list, result.getTotalCount()), result);
    }

    @Override
    public void export(BillConsolidatedStatementQuery query) {
        query.setPage(null);
        query.setLimit(null);
        List<BillConsolidatedStatementVO> list = billConsolidatedStatementDao.selectList(query);
        List<BillConsolidatedStatementExcelVO> excelList = BillConsolidatedStatementConvert.INSTANCE.convertList(list);
        transService.transBatch(excelList);
        // 写到浏览器打开
        ExcelUtils.excelExport(BillConsolidatedStatementExcelVO.class, "博通合并账单" + DateUtils.format(new Date()), null, excelList);
    }

    @Override
    public void updateDataBases() {
        billConsolidatedStatementDao.updateDataBases();
    }

    private LambdaQueryWrapper<BillConsolidatedStatementEntity> getWrapper(BillConsolidatedStatementQuery query) {
        LambdaQueryWrapper<BillConsolidatedStatementEntity> wrapper = Wrappers.lambdaQuery();

        return wrapper;
    }

}