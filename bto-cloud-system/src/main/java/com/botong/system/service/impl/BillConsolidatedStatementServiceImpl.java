package com.botong.system.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.botong.framework.common.cache.RedisCache;
import com.botong.framework.common.cache.RedisKeys;
import com.botong.framework.common.page.PageResult;
import com.botong.framework.common.utils.DateUtils;
import com.botong.framework.common.utils.ExcelUtils;
import com.botong.framework.common.utils.Result;
import com.botong.framework.mybatis.service.impl.BaseServiceImpl;
import com.botong.system.constant.ElectricChargeConstant;
import com.botong.system.convert.BillConsolidatedStatementConvert;
import com.botong.system.dao.BillConsolidatedStatementDao;
import com.botong.system.entity.BillConsolidatedStatementEntity;
import com.botong.system.entity.BodyElement;
import com.botong.system.entity.RequestBody;
import com.botong.system.query.BillConsolidatedStatementQuery;
import com.botong.system.query.ElectricChargeQuery;
import com.botong.system.service.BillConsolidatedStatementService;
import com.botong.system.utils.ElectricChargeUtil;
import com.botong.system.vo.BillConsolidatedStatementExcelVO;
import com.botong.system.vo.BillConsolidatedStatementVO;
import com.botong.system.vo.CombinedBillPageVO;
import com.botong.system.vo.ElectricitySummaryResultVO;
import com.fhs.trans.service.impl.TransService;
import com.google.gson.Gson;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 电费账单
 *
 * <AUTHOR>
 * @since 1.0.0 2025-01-04
 */
@Service
@AllArgsConstructor
public class BillConsolidatedStatementServiceImpl extends BaseServiceImpl<BillConsolidatedStatementDao, BillConsolidatedStatementEntity> implements BillConsolidatedStatementService {

    private final BillConsolidatedStatementDao billConsolidatedStatementDao;

    private final TransService transService;

    private final ElectricChargeUtil electricChargeUtil;

    private final RedisCache redisCache;

    @Override
    public CombinedBillPageVO page(BillConsolidatedStatementQuery query) {
        query.setPage((query.getPage() - 1) * query.getLimit());
        List<BillConsolidatedStatementVO> list = billConsolidatedStatementDao.selectList(query);
        ElectricitySummaryResultVO result = billConsolidatedStatementDao.getStatisticalResult(query);
        return new CombinedBillPageVO(new PageResult<>(list, result.getTotalCount()), result);
    }

    @Override
    public void export(BillConsolidatedStatementQuery query) {
        query.setPage(null);
        query.setLimit(null);
        List<BillConsolidatedStatementVO> list = billConsolidatedStatementDao.selectList(query);
        List<BillConsolidatedStatementExcelVO> excelList = BillConsolidatedStatementConvert.INSTANCE.convertList(list);
        transService.transBatch(excelList);
        // 写到浏览器打开
        ExcelUtils.excelExport(BillConsolidatedStatementExcelVO.class, "博通合并账单" + DateUtils.format(new Date()), null, excelList);
    }

    @Override
    public void updateDataBases() {
        billConsolidatedStatementDao.updateDataBases();
    }

    @Override
    public Result<String> updateYxBill() {
        // 一个月只允许更新一次，越秀每月5号滚动更新
        Date o = (Date) redisCache.get(RedisKeys.getYxBillUpdateTimeKey());
        if (o != null) {
            // 获取当前日期的年月
            LocalDate now = LocalDate.now();
            // 获取本月6号零点时间
            LocalDateTime month6thStart = LocalDateTime.of(now.getYear(), now.getMonth(), 6, 0, 0);
            Date month6thDate = Date.from(month6thStart.atZone(ZoneId.systemDefault()).toInstant());
            if (!o.before(month6thDate)) {
                return Result.ok("本月已更新，更新时间为：" + DateUtil.format(o, "yyyy-MM-dd HH:mm:ss"));
            }
        }
        // 获取token
        String token = electricChargeUtil.getToken();
        // 获取电费账单
        ElectricChargeQuery query = new ElectricChargeQuery();
        query.setInterfaceUrl(ElectricChargeConstant.ELECTRIC_CHARGE_URL);
        query.setUrlParam("?access_token=" + token);
        BodyElement bodyElement = new BodyElement();
        bodyElement.setFileType("electricChargeBill");
        bodyElement.setQueryType("bill_inc");

        List<BodyElement> bodyList = new ArrayList<>();
        bodyList.add(bodyElement);
        RequestBody requestBody = new RequestBody();
        requestBody.setBody(bodyList);

        requestBody.setHead(electricChargeUtil.getHeadParam());
        Gson gson = new Gson();
        query.setBodyParam(gson.toJson(requestBody));
        System.out.println(query);
        JSONObject request = electricChargeUtil.request(query);
        System.out.println(request);
        // 记录数据更新日期
        redisCache.set(RedisKeys.getYxBillUpdateTimeKey(), new Date(), RedisCache.HOUR_SIX_EXPIRE);
        // 更新数据库
        updateDataBases();
        return Result.ok(Objects.isNull(request) ? "更新失败" : request.getStr("result"));
    }

    private LambdaQueryWrapper<BillConsolidatedStatementEntity> getWrapper(BillConsolidatedStatementQuery query) {
        LambdaQueryWrapper<BillConsolidatedStatementEntity> wrapper = Wrappers.lambdaQuery();

        return wrapper;
    }

}