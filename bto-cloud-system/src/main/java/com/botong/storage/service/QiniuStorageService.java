package com.botong.storage.service;

import com.alibaba.fastjson.JSONObject;
import com.botong.framework.common.exception.ServerException;
import com.botong.framework.common.utils.StringUtils;
import com.botong.storage.properties.StorageProperties;
import com.qiniu.common.QiniuException;
import com.qiniu.http.Response;
import com.qiniu.storage.BucketManager;
import com.qiniu.storage.Configuration;
import com.qiniu.storage.Region;
import com.qiniu.storage.UploadManager;
import com.qiniu.storage.model.BatchStatus;
import com.qiniu.util.Auth;
import com.qiniu.util.IOUtils;

import java.io.IOException;
import java.io.InputStream;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Base64;

/**
 * 七牛云存储
 */
public class QiniuStorageService extends StorageService {
    private final UploadManager uploadManager;
    private final String token;

    public QiniuStorageService(StorageProperties properties) {
        this.properties = properties;

        uploadManager = new UploadManager(new Configuration(Region.autoRegion()));
        token = generateToken();
    }

    public String generateToken() {
        return Auth.create(properties.getQiniu().getAccessKey(), properties.getQiniu().getSecretKey()).
                uploadToken(properties.getQiniu().getBucketName());
    }

    @Override
    public String upload(byte[] data, String path) {
        try {
            String uploadToken = token;
            Boolean tokenVerify = tokenVerify(uploadToken);

            if (Boolean.TRUE.equals(tokenVerify)) {
                uploadToken = generateToken();
            }

            Response res = uploadManager.put(data, path, uploadToken);
            if (!res.isOK()) {
                throw new ServerException(res.toString());
            }
            return properties.getConfig().getDomain() + "/" + path;
        } catch (Exception e) {
            throw new ServerException("上传文件失败：", e);
        }
    }

    @Override
    public String upload(InputStream inputStream, String path) {
        try {
            byte[] data = IOUtils.toByteArray(inputStream);
            return this.upload(data, path);
        } catch (IOException e) {
            throw new ServerException("上传文件失败：", e);
        }
    }


    /**
     * 七牛云token校验是否过期
     */
    private Boolean tokenVerify(String token) {
        String[] tokenInfo = token.split(StringUtils.COLON);
        // 进行Base64解码
        byte[] decodedBytes = Base64.getDecoder().decode(tokenInfo[2]);

        // 将字节数组转换为字符串
        String decodedString = new String(decodedBytes);
        JSONObject jsonObject = JSONObject.parseObject(decodedString);
        // 获取当前时间戳进行比较是否过期
        long timestamp = Instant.now().getEpochSecond();
        long deadline = Long.parseLong(jsonObject.get("deadline").toString());
        return timestamp > deadline;
    }

    @Override
    public String deleteBatchFile(String[] filePaths) {

        String resultMessage = "成功删除文件：";
        String errorMessage = "成功删除文件：";

        if (filePaths.length > 50) {
            throw new ServerException("单次批量请求的文件数量不得超过50");
        }
        for (int i = 0; i < filePaths.length; i++) {
            filePaths[i] = filePaths[i].replace("https://qiniu.btosolarman.com/", "");
        }

        Configuration cfg = new Configuration(Region.region0());
        Auth auth = Auth.create(properties.getQiniu().getAccessKey(), properties.getQiniu().getSecretKey());
        String bucketName = properties.getQiniu().getBucketName();
        BucketManager bucketManager = new BucketManager(auth, cfg);

        try {

            BucketManager.BatchOperations batchOperations = new BucketManager.BatchOperations();
            batchOperations.addDeleteOp(bucketName, filePaths);
            Response response = bucketManager.batch(batchOperations);
            if (!response.isOK()) {
                throw new ServerException("删除文件失败");
            }
            BatchStatus[] batchStatusList = response.jsonToObject(BatchStatus[].class);
            ArrayList<String> successKeys = new ArrayList<>();
            ArrayList<String> errorKeys = new ArrayList<>();
            for (int i = 0; i < filePaths.length; i++) {
                BatchStatus status = batchStatusList[i];
                String key = filePaths[i];
                if (status.code == 200) {
                    successKeys.add(key);
                } else {
                    errorKeys.add(key);
                }
            }
            resultMessage += successKeys.toString();
            if (!errorKeys.isEmpty()) {
                resultMessage += "\n" + errorMessage + errorKeys.toString();
            }
            return resultMessage;
        } catch (QiniuException e) {
            throw new ServerException("删除文件失败：", e.response.toString());
        }
    }


}