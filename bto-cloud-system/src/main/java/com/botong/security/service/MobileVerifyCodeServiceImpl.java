package com.botong.security.service;

import com.botong.api.module.message.SmsApi;
import com.botong.framework.common.utils.Result;
import com.botong.framework.security.mobile.MobileVerifyCodeService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 短信验证码效验
 */
@Service
@AllArgsConstructor
public class MobileVerifyCodeServiceImpl implements MobileVerifyCodeService {
    private final SmsApi smsApi;

    @Override
    public boolean verifyCode(String mobile, String code) {
        Result<Boolean> result = smsApi.verifyCode(mobile, code);

        return result.getData();
    }
}
