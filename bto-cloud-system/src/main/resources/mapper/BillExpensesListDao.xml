<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.botong.system.dao.BillExpensesListDao">

    <resultMap type="com.botong.system.entity.BillExpensesListEntity" id="billExpensesListMap">
        <result property="plantUid" column="plant_uid"/>
        <result property="closingYear" column="closing_year"/>
        <result property="january" column="january"/>
        <result property="february" column="february"/>
        <result property="march" column="march"/>
        <result property="april" column="april"/>
        <result property="may" column="may"/>
        <result property="june" column="june"/>
        <result property="july" column="july"/>
        <result property="august" column="august"/>
        <result property="september" column="september"/>
        <result property="october" column="october"/>
        <result property="november" column="november"/>
        <result property="december" column="december"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <resultMap id="MaintenanceBillMap" type="com.botong.system.vo.MaintenanceBillVO">
        <result property="plantUid" column="plant_uid"/>
        <result property="closingYear" column="closing_year"/>
        <result property="january" column="january"/>
        <result property="february" column="february"/>
        <result property="march" column="march"/>
        <result property="april" column="april"/>
        <result property="may" column="may"/>
        <result property="june" column="june"/>
        <result property="july" column="july"/>
        <result property="august" column="august"/>
        <result property="september" column="september"/>
        <result property="october" column="october"/>
        <result property="november" column="november"/>
        <result property="december" column="december"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="plantName" column="plant_name"/>
        <result property="plantCapacity" column="plant_capacity"/>
        <result property="city" column="city"/>
        <result property="meterId" column="meter_id"/>
        <result property="contractId" column="contract_id"/>
        <result property="orderId" column="order_id"/>
        <result property="btoContractId" column="bto_contract_id"/>
        <result property="projectCompany" column="project_company"/>
    </resultMap>
    <select id="page" resultType="com.botong.system.vo.MaintenanceBillVO">
        SELECT
            v_plant_base.plant_uid,
            v_plant_base.plant_name,
            v_plant_base.plant_capacity,
            v_plant_base.project_company,
            v_plant_base.city,
            v_plant_base.meter_id,
            v_plant_base.contract_id,
            v_plant_base.order_id,
            v_plant_base.create_time,
            bill_expenses_list.closing_year,
            bill_expenses_list.january,
            bill_expenses_list.february,
            bill_expenses_list.march,
            bill_expenses_list.april,
            bill_expenses_list.may,
            bill_expenses_list.june,
            bill_expenses_list.july,
            bill_expenses_list.august,
            bill_expenses_list.september,
            bill_expenses_list.october,
            bill_expenses_list.november,
            bill_expenses_list.december,
            bill_expenses_list.update_time
        FROM
           bill_expenses_list
                LEFT JOIN v_plant_base ON v_plant_base.plant_uid = bill_expenses_list.plant_uid
        <where>
            <if test="query.plantName != null and query.plantName != ''">
                AND v_plant_base.plant_name LIKE concat('%', #{query.plantName}, '%')
            </if>
            <if test="query.meterId != null and query.meterId != ''">
                AND v_plant_base.meter_id = #{query.meterId}
            </if>
            <if test="query.contractId != null and query.contractId != ''">
                AND v_plant_base.contract_id = #{query.contractId}
            </if>
            <if test="query.orderId != null and query.orderId != ''">
                AND v_plant_base.order_id = #{query.orderId}
            </if>
            <if test="query.city != null and query.city != ''">
                AND v_plant_base.city = #{query.city}
            </if>
            <if test="query.startYear != null and query.startYear != '' and query.endYear != null and query.endYear != ''">
                AND bill_expenses_list.closing_year between #{query.startYear} and #{query.endYear}
            </if>
        </where>
        order by v_plant_base.create_time desc
    </select>

</mapper>