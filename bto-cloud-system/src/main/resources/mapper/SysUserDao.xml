<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.botong.system.dao.SysUserDao">

	<select id="getList" resultType="com.botong.system.entity.SysUserEntity">
		select t1.* from sys_user t1 where t1.super_admin = 0 and t1.deleted = 0
		<if test="username != null and username.trim() != ''">
			and t1.username = #{username}
		</if>
		<if test="mobile != null and mobile.trim() != ''">
			and t1.mobile = #{mobile}
		</if>
		<if test="gender != null">
			and t1.gender = #{gender}
		</if>
	</select>

	<select id="getById" resultType="com.botong.system.entity.SysUserEntity">
		select t1.*, (select t2.name from sys_org t2 where t2.id = t1.org_id) orgName from sys_user t1
			where t1.id = #{id} and t1.deleted = 0
	</select>

	<select id="getRoleUserList" resultType="com.botong.system.entity.SysUserEntity">
		select t1.* from sys_user t1 left join sys_user_role t2 on t1.id = t2.user_id
		where t1.deleted = 0 and t2.deleted = 0 and t2.role_id = #{roleId}
		<if test="username != null and username.trim() != ''">
			and t1.username = #{username}
		</if>
		<if test="mobile != null and mobile.trim() != ''">
			and t1.mobile = #{mobile}
		</if>
		<if test="gender != null">
			and t1.gender = #{gender}
		</if>
	</select>
    <select id="getRepairIdByWorkId" resultType="java.lang.Long">
		SELECT repair_id FROM work_operation_person_info WHERE  work_id = #{workId} and deleted = 0
	</select>
	<select id="getCompanionByWorkId" resultType="java.lang.String">
		SELECT companion FROM work_operation_info WHERE  work_id = #{workId} and deleted = 0
	</select>


</mapper>