<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.botong.system.dao.BillConsolidatedStatementDao">

    <resultMap type="com.botong.system.entity.BillConsolidatedStatementEntity" id="billConsolidatedStatementMap">
        <result property="plantUid" column="plant_uid"/>
        <result property="electricity" column="electricity"/>
        <result property="settlementDate" column="settlement_date"/>
        <result property="taxRate" column="tax_rate"/>
        <result property="taxAmount" column="tax_amount"/>
        <result property="electrovalence" column="electrovalence"/>
        <result property="electricityWithTax" column="electricity_with_tax"/>
        <result property="electricityWithoutTax" column="electricity_without_tax"/>
        <result property="administrator" column="administrator"/>
        <result property="electricityWithTaxYx" column="electricity_with_tax_yx"/>
        <result property="projectCompany" column="project_company"/>
        <result property="meterId" column="meter_id"/>
        <result property="orderId" column="order_id"/>
        <result property="contractId" column="contract_id"/>
        <result property="isMonth" column="is_month"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>
    <select id="updateDataBases">
        call proc_electricity_peculiar_while()
    </select>

    <select id="selectList" resultType="com.botong.system.vo.BillConsolidatedStatementVO">
        WITH w_statement AS (
            SELECT
                plant_uid,
                electricity,
                settlement_date,
                tax_rate,
                tax_amount,
                electrovalence,
                electricity_with_tax,
                electricity_without_tax,
                administrator,
                electricity_with_tax_yx,
                project_company,
                meter_id,
                order_id,
                contract_id,
                is_month,
                create_time,
                update_time
            FROM
                bill_consolidated_statement
        <where>
            <if test="query.settlementDate != null and query.settlementDate != ''">
                AND settlement_date = #{query.settlementDate}
            </if>
        </where>
        ) SELECT
              v.plant_uid,
              v.plant_name,
              v.create_time connectionDate,
              v.plant_capacity,
              v.city,
              w.electricity,
              w.settlement_date,
              w.tax_rate,
              w.tax_amount,
              w.electrovalence,
              w.electricity_with_tax,
              w.electricity_without_tax,
              w.administrator,
              w.electricity_with_tax_yx,
              v.project_company,
              v.meter_id,
              v.order_id,
              v.contract_id,
              w.is_month,
                CASE
                WHEN ( electricity_with_tax IS NULL OR electricity_with_tax = '' ) THEN
                1
                WHEN ( electricity_with_tax_yx IS NULL OR electricity_with_tax_yx = '' ) THEN
                2
                WHEN electricity_with_tax_yx = electricity_with_tax
                AND ( electricity_with_tax IS NOT NULL AND electricity_with_tax_yx IS NOT NULL AND electricity_with_tax != '' ) THEN
                0 ELSE - 1
                END AS accord,
              w.create_time,
              w.update_time
        FROM
            v_plant_base v
                LEFT JOIN w_statement w ON v.plant_uid = w.plant_uid
        <where>
            <if test="query.plantName != null and query.plantName != ''">
                AND v.plant_name LIKE CONCAT('%',#{query.plantName},'%')
            </if>
            <if test="query.city != null and query.city != ''">
                AND v.city LIKE CONCAT('%',#{query.city},'%')
            </if>
            <if test="query.meterId != null and query.meterId != ''">
                AND v.meter_id = #{query.meterId}
            </if>
            <if test="query.contractId != null and query.contractId != ''">
                AND v.contract_id = #{query.contractId}
            </if>
            <if test="query.orderId != null and query.orderId != ''">
                AND v.order_id = #{query.orderId}
            </if>
            <if test="query.administrator != null and query.administrator != ''">
                AND w.administrator = #{query.administrator}
            </if>
            <if test="query.projectCompany != null and query.projectCompany != ''">
                AND w.project_company = #{query.projectCompany}
            </if>
            <choose>
                <when test="query.settlementType == 'monthly'">
                    AND w.is_month = 1
                </when>
                <when test="query.settlementType == 'combined'">
                    AND w.is_month != 1
                </when>
            </choose>
            <if test="query.accord != null">
                AND w.accord = #{query.accord}
            </if>
        </where>
        order by v.create_time DESC
        <if test="query.page != null and query.limit != null">
            LIMIT #{query.page},#{query.limit}
        </if>
    </select>
    <select id="getStatisticalResult" resultType="com.botong.system.vo.ElectricitySummaryResultVO">
        WITH w_statement AS (
            SELECT
                plant_uid,
                electricity,
                settlement_date,
                tax_rate,
                tax_amount,
                electrovalence,
                electricity_with_tax,
                electricity_without_tax,
                administrator,
                electricity_with_tax_yx,
                project_company,
                meter_id,
                order_id,
                contract_id,
                is_month,
                CASE
                    WHEN ( electricity_with_tax IS NULL OR electricity_with_tax = '' ) THEN
                        1
                    WHEN ( electricity_with_tax_yx IS NULL OR electricity_with_tax_yx = '' ) THEN
                        2
                    WHEN electricity_with_tax_yx = electricity_with_tax
                        AND ( electricity_with_tax IS NOT NULL AND electricity_with_tax_yx IS NOT NULL AND electricity_with_tax != '' ) THEN
                        0 ELSE - 1
                    END AS accord,
                create_time,
                update_time
            FROM
                bill_consolidated_statement
        <where>
            <if test="query.settlementDate != null and query.settlementDate != ''">
                AND settlement_date = #{query.settlementDate}
            </if>
        </where>
        ) SELECT
              COUNT(*) AS total_count,
              SUM( w.electricity_with_tax_yx ) AS sum_electricity_with_tax_yx,
              SUM( w.electricity ) AS sum_electricity,
              SUM( w.tax_amount ) AS sum_tax_amount,
              SUM( w.electricity_with_tax ) AS sum_electricity_with_tax,
              SUM( w.electricity_without_tax ) AS sum_electricity_without_tax
        FROM
            v_plant_base v
                LEFT JOIN w_statement w ON v.plant_uid = w.plant_uid
        <where>
            <if test="query.plantName != null and query.plantName != ''">
                AND v.plant_name LIKE CONCAT('%',#{query.plantName},'%')
            </if>
            <if test="query.city != null and query.city != ''">
                AND v.city LIKE CONCAT('%',#{query.city},'%')
            </if>
            <if test="query.meterId != null and query.meterId != ''">
                AND v.meter_id = #{query.meterId}
            </if>
            <if test="query.contractId != null and query.contractId != ''">
                AND v.contract_id = #{query.contractId}
            </if>
            <if test="query.orderId != null and query.orderId != ''">
                AND v.order_id = #{query.orderId}
            </if>
            <if test="query.administrator != null and query.administrator != ''">
                AND w.administrator = #{query.administrator}
            </if>
            <if test="query.projectCompany != null and query.projectCompany != ''">
                AND w.project_company = #{query.projectCompany}
            </if>
            <choose>
                <when test="query.settlementType == 'monthly'">
                    AND w.is_month = 1
                </when>
                <when test="query.settlementType == 'combined'">
                    AND w.is_month != 1
                </when>
            </choose>
            <if test="query.accord != null">
                AND w.accord = #{query.accord}
            </if>
        </where>
    </select>

</mapper>