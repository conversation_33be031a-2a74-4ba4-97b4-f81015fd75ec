<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.botong.system.dao.BillPriceMaintenanceDao">

    <resultMap type="com.botong.system.entity.BillPriceMaintenanceEntity" id="billPriceMaintenanceMap">
        <result property="id" column="id"/>
        <result property="stageStart" column="stage_start"/>
        <result property="stageEnd" column="stage_end"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>
    <select id="page" resultType="com.botong.system.vo.BillPriceMaintenanceVO">
        SELECT
            bill_price_maintenance.id,
            bill_price_scheme.scheme_id,
            bill_price_maintenance.stage_start,
            bill_price_maintenance.stage_end,
            bill_price_scheme.scheme_id parentId,
            bill_price_scheme.investor_id,
            bill_price_scheme.stage_from,
            bill_price_scheme.stage_to,
            bill_price_scheme.price,
            bill_price_scheme.province,
            bill_price_scheme.city
        FROM
            bill_price_maintenance
                LEFT JOIN bill_price_scheme ON bill_price_maintenance.scheme_id = bill_price_scheme.scheme_id
        where bill_price_scheme.is_deleted = 0
    </select>

</mapper>