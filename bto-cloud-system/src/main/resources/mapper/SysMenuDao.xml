<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.botong.system.dao.SysMenuDao">

	<select id="getMenuList" resultType="com.botong.system.entity.SysMenuEntity">
		select * from sys_menu where deleted = 0
		<if test="type != null">
			and type = #{type}
		</if>
		order by sort asc
	</select>

	<select id="getUserMenuList" resultType="com.botong.system.entity.SysMenuEntity">
		select t3.* from sys_user_role t1
			left join sys_role_menu t2 on t1.role_id = t2.role_id
			left join sys_menu t3 on t2.menu_id = t3.id
		where t1.user_id = #{userId} and t1.deleted = 0 and t2.deleted = 0 and t3.deleted = 0
		<if test="type != null">
			and t3.type = #{type}
		</if>
		order by t3.sort asc
	</select>

	<select id="getUserAuthorityList" resultType="string">
		select t3.authority from sys_user_role t1
		    left join sys_role_menu t2 on t1.role_id = t2.role_id
			left join sys_menu t3 on t2.menu_id = t3.id
		where t1.user_id = #{userId} and t1.deleted = 0 and t2.deleted = 0 and t3.deleted = 0 order by t3.sort asc
	</select>

	<select id="getAuthorityList" resultType="string">
		select authority from sys_menu where deleted = 0
	</select>
	
</mapper>