<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.botong.system.dao.SysOrgDao">

    <select id="getList" resultType="com.botong.system.entity.SysOrgEntity">
        select t1.*, (select t2.name from sys_org t2 where t2.id = t1.pid) parentName
        from sys_org t1
        where t1.deleted = 0
        order by t1.sort asc
    </select>

    <select id="getIdAndPidList" resultType="com.botong.system.entity.SysOrgEntity">
        select t1.id, t1.pid
        from sys_org t1
        where t1.deleted = 0
    </select>
    <select id="getAllOrg" resultType="com.botong.api.module.workorder.entity.OrgBaseEntity">
        select t1.id, t1.name, t1.pid
        from sys_org t1
        where t1.deleted = 0
    </select>

</mapper>