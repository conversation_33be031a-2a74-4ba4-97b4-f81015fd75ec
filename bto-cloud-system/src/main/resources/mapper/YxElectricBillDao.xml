<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//ES" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.botong.system.dao.YxElectricBillDao">
    <select id="selectAllElectricBill" resultMap="ElectricBillMap">
        SELECT
            bill_id,
            manufacturer_name,
            power_generation_no,
            source,
            amount,
            bill_time,
            payment_time,
            partners_contract_number,
            bank_account_num_second,
            ds,
            medium_name,
            entry_number,
            amount_after_refund
        FROM
            bill_yx_electricity_summary
    </select>
    <select id="selectElectricBillByMonth" resultType="java.lang.String">
        SELECT
            electricity_with_tax
        FROM
            bill_bto_electricity_summary
        WHERE
            entry_number = #{entryNumber}
            AND bill_time = #{month}
    </select>
    <select id="selectPlantCreateTime" resultType="java.util.Date">
        SELECT
            create_time
        FROM
            v_plant_base
        WHERE
            order_id = #{entryNumber}
    </select>
    <select id="getPlantCreateTime" resultType="java.time.LocalDate">
        SELECT
            create_time
        FROM
            v_plant_base
        WHERE
            order_id = #{entryNumber}
    </select>

    <!-- 定义结果映射 -->
    <resultMap id="ElectricBillMap" type="com.botong.system.entity.ElectricBill">
        <id property="bill_id" column="bill_id"/>
        <result property="manufacturer_name" column="manufacturer_name"/>
        <result property="power_generation_no" column="power_generation_no"/>
        <result property="source" column="source"/>
        <result property="amount" column="amount"/>
        <result property="bill_time" column="bill_time"/>
        <result property="payment_time" column="payment_time"/>
        <result property="partners_contract_number" column="partners_contract_number"/>
        <result property="bank_account_num_second" column="bank_account_num_second"/>
        <result property="ds" column="ds"/>
        <result property="medium_name" column="medium_name"/>
        <result property="entry_number" column="entry_number"/>
        <result property="amount_after_refund" column="amount_after_refund"/>
    </resultMap>
</mapper>