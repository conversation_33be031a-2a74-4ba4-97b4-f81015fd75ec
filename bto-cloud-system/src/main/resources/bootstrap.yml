server:
  port: 58081

spring:
  servlet:
    multipart:
      max-file-size: 20MB
      max-request-size: 20MB
  application:
    name: work-order-cloud-system
  profiles:
    active: dev
  cloud:
    nacos:
      discovery:
        server-addr: ${nacos_host:127.0.0.1}:${nacos_port:8848}
        # 命名空间，默认：public
        namespace: ${nacos_namespace:}
        service: ${spring.application.name}
      config:
        server-addr: ${spring.cloud.nacos.discovery.server-addr}
        namespace: ${spring.cloud.nacos.discovery.namespace}
        file-extension: yaml
        # 指定配置
        extension-configs:
          - data-id: datasource.yaml
            refresh: true
          - data-id: common.yaml
            refresh: true


storage:
  enabled: true
  config:
    # 存储类型：local、aliyun、tencent、qiniu、huawei、minio
    type: qiniu
    domain: https://qiniu.btosolarman.com
  qiniu:
    access-key: nwju17wky7CkM8C2BWuyQnggHbFOujFTv1XN9ZKT
    secret-key: dfi2hOvtnJaYN8qU_qI_tR-TMG2Gz_cDNMeRgfX6
    bucket-name: bto-work-order